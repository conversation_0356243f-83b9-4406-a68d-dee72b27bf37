# 🏠 CTRON Home - Professional Home Services Platform

A comprehensive home services platform connecting homeowners with certified technicians for HVAC, plumbing, electrical, and general maintenance services.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL 14+
- Git

### 1. <PERSON><PERSON> and Install
```bash
git clone <repository-url>
cd CTRON-Home/apps

# Install dependencies for all apps
npm install --prefix backend
npm install --prefix web
npm install --prefix mobile
```

### 2. Database Setup
```bash
# Start PostgreSQL and create database
createdb ctron

# Setup backend database
cd backend
cp .env.development .env
npx prisma db push
npx prisma generate
```

### 3. Start Development Servers
```bash
# Terminal 1 - Backend API
cd backend
npm run dev

# Terminal 2 - Web Admin Panel
cd web
npm run dev

# Terminal 3 - Mobile App
cd mobile
npx expo start
```

## 📱 Mobile App Testing

### Expo Go Setup
1. Install **Expo Go 52** on your phone
2. Scan QR code from terminal
3. App loads with full functionality

### Network Configuration
For mobile device testing, update environment variables:
```bash
# In mobile/.env.development
EXPO_PUBLIC_API_URL=http://YOUR_IP:3001/api
EXPO_PUBLIC_API_BASE_URL=http://YOUR_IP:3001
EXPO_PUBLIC_SOCKET_URL=http://YOUR_IP:3001
```

## 🏗️ Architecture

### Backend (`/backend`)
- **Framework**: Express.js + TypeScript
- **Database**: PostgreSQL + Prisma ORM
- **Authentication**: JWT with role-based access
- **Real-time**: Socket.IO for chat and notifications
- **Payments**: Stripe integration
- **Security**: Helmet, CORS, rate limiting

### Web Admin (`/web`)
- **Framework**: React + TypeScript + Vite
- **Styling**: Tailwind CSS
- **State**: React Query + Context API
- **Routing**: React Router v6
- **UI**: Custom component library

### Mobile App (`/mobile`)
- **Framework**: React Native + Expo 52
- **Navigation**: React Navigation v6
- **State**: Context API + AsyncStorage
- **UI**: Custom design system
- **Platform**: iOS + Android support

## 🔧 Environment Configuration

### Development
```bash
# Backend
PORT=3001
HOST=localhost
DATABASE_URL="postgresql://postgres:admin@localhost:5432/ctron"

# Mobile
EXPO_PUBLIC_API_URL=http://localhost:3001/api
EXPO_PUBLIC_API_BASE_URL=http://localhost:3001
```

### Production
```bash
# Backend
PORT=3001
HOST=0.0.0.0
DATABASE_URL="your_production_database_url"

# Mobile
EXPO_PUBLIC_API_URL=https://api.yourdomain.com/api
EXPO_PUBLIC_API_BASE_URL=https://api.yourdomain.com
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
npm test
npm run test:coverage
```

### Mobile Testing
```bash
cd mobile
npm test
npx expo start --clear  # Clear cache if needed
```

## 🔒 Security Features

- JWT authentication with refresh tokens
- Role-based access control (HOMEOWNER/TECHNICIAN/ADMIN)
- Rate limiting on API endpoints
- Input validation with Zod
- SQL injection prevention with Prisma
- XSS protection with Helmet
- CORS configuration
- Environment-based security settings

## 📊 Key Features

### For Homeowners
- Service request creation and tracking
- Technician search and booking
- Real-time chat with technicians
- Payment processing and history
- Review and rating system

### For Technicians
- Job browsing and application
- Schedule management
- Customer communication
- Earnings tracking
- Profile and certification management

### For Administrators
- User and technician management
- Job oversight and analytics
- Payment and commission tracking
- System configuration
- Comprehensive dashboard

## 🛠️ Development Tools

### Code Quality
- TypeScript for type safety
- ESLint + Prettier for code formatting
- Husky for git hooks
- Conventional commits

### Debugging
- Comprehensive debug logging (development only)
- Network connectivity testing
- API request/response monitoring
- Error boundary implementation

## 📚 Documentation

- [Environment Setup](./ENVIRONMENT_SETUP.md)
- [API Documentation](./backend/docs/api.md)
- [Database Schema](./backend/docs/database.md)
- [Mobile App Guide](./mobile/docs/setup.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in this repository
- Check existing documentation
- Review environment setup guide

---

**Built with ❤️ for professional home services**
