# ===========================================
# CTRON HOME BACKEND - DEVELOPMENT TEMPLATE
# ===========================================
#
# ⚠️  SECURITY WARNING: This is a TEMPLATE file for development environment.
#
# 1. Copy this to .env and update with your REAL values
# 2. NEVER commit real API keys or secrets to version control
# 3. Use strong, unique secrets for production
# 4. The .env file should contain your actual configuration
#
# All placeholder values below MUST be replaced with real values
#

# Server Configuration
NODE_ENV=development
PORT=8080
HOST=0.0.0.0

# Database Configuration
DATABASE_URL="postgresql://postgres:admin1234@localhost:5432/ctron"

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-here-min-32-chars
JWT_EXPIRES_IN=7d

# Stripe Payment Configuration (Test Keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here
STRIPE_API_VERSION=2025-04-30.basil

# OpenAI Configuration
OPENAI_API_KEY=sk-proj-your_openai_api_key_here
OPENAI_API_URL=https://api.openai.com/v1

# AWS S3 Configuration (Development)
AWS_REGION=eu-west-2
AWS_BUCKET_NAME=your-s3-bucket-name
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Expo Configuration
EXPO_TOKEN=your_expo_access_token_here
EXPO_API_URL=https://exp.host/--/api/v2

# CORS Configuration (Development - Allow all local origins)
CORS_ORIGIN=http://localhost:5173,http://localhost:3000,http://localhost:19006,exp://*************:8081,http://*************:8080

# Socket.IO Configuration
SOCKET_CORS_ORIGIN=*

# Rate Limiting (More lenient for development)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
