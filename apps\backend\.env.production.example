# ===========================================
# CTRON HOME BACKEND - PRODUCTION ENVIRONMENT
# ===========================================

# Server Configuration
NODE_ENV=production
PORT=8080
HOST=0.0.0.0

# Database Configuration (Use production database URL)
DATABASE_URL="******************************************************/ctron_prod"

# Authentication & Security (Use strong production secrets)
JWT_SECRET=your-super-secure-production-jwt-secret-here
JWT_EXPIRES_IN=7d

# Stripe Payment Configuration (Live Keys - Replace with actual live keys)
STRIPE_SECRET_KEY=sk_live_your_live_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret_here
STRIPE_API_VERSION=2025-04-30.basil

# OpenAI Configuration
OPENAI_API_KEY=your-production-openai-api-key-here
OPENAI_API_URL=https://api.openai.com/v1

# AWS S3 Configuration (Production)
AWS_REGION=eu-west-2
AWS_BUCKET_NAME=ctron-proof-photos-prod
AWS_ACCESS_KEY_ID=your-production-aws-access-key
AWS_SECRET_ACCESS_KEY=your-production-aws-secret-key
AWS_S3_ENDPOINT=https://s3.eu-west-2.amazonaws.com

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-email-password

# Expo Configuration
EXPO_TOKEN=your-production-expo-token
EXPO_API_URL=https://exp.host/--/api/v2

# CORS Configuration (Production - Restrict to your domains)
CORS_ORIGIN=https://app.ctron.com,https://admin.ctron.com

# Socket.IO Configuration
SOCKET_CORS_ORIGIN=https://app.ctron.com,https://admin.ctron.com

# Rate Limiting (Stricter for production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
