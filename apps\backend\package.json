{"name": "ctron-backend", "version": "1.0.0", "type": "commonjs", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development nodemon", "dev:js": "cross-env NODE_ENV=development node server-js.js", "dev:staging": "cross-env NODE_ENV=staging nodemon --env-file=.env.staging src/index.ts", "dev:production": "cross-env NODE_ENV=production nodemon --env-file=.env.production src/index.ts", "build": "tsc", "start": "cross-env NODE_ENV=production node dist/src/index.js", "start:js": "cross-env NODE_ENV=production node server-js.js", "start:staging": "cross-env NODE_ENV=staging node --env-file=.env.staging dist/index.js", "start:development": "cross-env NODE_ENV=development node --env-file=.env.development dist/index.js", "seed": "ts-node prisma/seed.ts", "test": "cross-env NODE_ENV=test jest --runInBand --setupFiles dotenv/config", "release:expired-jobs": "ts-node scripts/autoReleaseCompletedJobs.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@faker-js/faker": "^9.7.0", "@prisma/client": "^6.10.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "expo-server-sdk": "^3.15.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "node-cron": "^4.0.5", "nodemailer": "^6.10.1", "openai": "^4.95.1", "prisma": "^6.10.1", "socket.io": "^4.8.1", "stripe": "^18.0.0", "uuid": "^11.1.0", "zod": "^3.25.31"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.2", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.1", "@types/supertest": "^6.0.2", "cross-env": "^7.0.3", "jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.0.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}