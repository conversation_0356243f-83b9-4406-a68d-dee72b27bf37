/*
  Warnings:

  - You are about to drop the column `ompletedAt` on the `Job` table. All the data in the column will be lost.
  - Made the column `createdAt` on table `Job` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "Job" DROP CONSTRAINT "Job_technicianId_fkey";

-- AlterTable
ALTER TABLE "Job" DROP COLUMN "ompletedAt",
ADD COLUMN     "completedAt" TIMESTAMP(3),
ALTER COLUMN "technicianId" DROP NOT NULL,
ALTER COLUMN "createdAt" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_technicianId_fkey" FOREIGN KEY ("technicianId") REFERENCES "Technician"("id") ON DELETE SET NULL ON UPDATE CASCADE;
