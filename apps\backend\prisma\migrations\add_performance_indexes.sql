-- Performance optimization indexes for CTRON Home database

-- User table indexes
CREATE INDEX IF NOT EXISTS idx_user_email ON "User"(email);
CREATE INDEX IF NOT EXISTS idx_user_role ON "User"(role);
CREATE INDEX IF NOT EXISTS idx_user_created_at ON "User"("createdAt");

-- Job table indexes
CREATE INDEX IF NOT EXISTS idx_job_status ON "Job"(status);
CREATE INDEX IF NOT EXISTS idx_job_homeowner_id ON "Job"("homeownerId");
CREATE INDEX IF NOT EXISTS idx_job_technician_id ON "Job"("technicianId");
CREATE INDEX IF NOT EXISTS idx_job_scheduled_at ON "Job"("scheduledAt");
CREATE INDEX IF NOT EXISTS idx_job_created_at ON "Job"("createdAt");
CREATE INDEX IF NOT EXISTS idx_job_location ON "Job"(location);

-- Message table indexes
CREATE INDEX IF NOT EXISTS idx_message_chat_id ON "Message"("chatId");
CREATE INDEX IF NOT EXISTS idx_message_sender_id ON "Message"("senderId");
CREATE INDEX IF NOT EXISTS idx_message_created_at ON "Message"("createdAt");

-- Chat table indexes
CREATE INDEX IF NOT EXISTS idx_chat_job_id ON "Chat"("jobId");
CREATE INDEX IF NOT EXISTS idx_chat_created_at ON "Chat"("createdAt");

-- Payment table indexes
CREATE INDEX IF NOT EXISTS idx_payment_job_id ON "Payment"("jobId");
CREATE INDEX IF NOT EXISTS idx_payment_status ON "Payment"(status);
CREATE INDEX IF NOT EXISTS idx_payment_created_at ON "Payment"("createdAt");

-- Review table indexes
CREATE INDEX IF NOT EXISTS idx_review_job_id ON "Review"("jobId");
CREATE INDEX IF NOT EXISTS idx_review_reviewer_id ON "Review"("reviewerId");
CREATE INDEX IF NOT EXISTS idx_review_technician_id ON "Review"("technicianId");
CREATE INDEX IF NOT EXISTS idx_review_rating ON "Review"(rating);

-- Notification table indexes
CREATE INDEX IF NOT EXISTS idx_notification_user_id ON "Notification"("userId");
CREATE INDEX IF NOT EXISTS idx_notification_read ON "Notification"(read);
CREATE INDEX IF NOT EXISTS idx_notification_created_at ON "Notification"("createdAt");

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_job_homeowner_status ON "Job"("homeownerId", status);
CREATE INDEX IF NOT EXISTS idx_job_technician_status ON "Job"("technicianId", status);
CREATE INDEX IF NOT EXISTS idx_message_chat_created ON "Message"("chatId", "createdAt");
CREATE INDEX IF NOT EXISTS idx_notification_user_read ON "Notification"("userId", read);
