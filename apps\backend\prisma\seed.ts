// prisma/seed.ts
import { prisma } from '../src/config/db';
import { faker } from '@faker-js/faker';
import bcrypt from 'bcryptjs';

async function main() {
  console.log('🌱 Seeding database...');

  // Create Homeowners
  const homeowners = await Promise.all(
    Array.from({ length: 5 }).map(async () => {
      return prisma.user.create({
        data: {
          fullName: faker.person.fullName(),
          email: faker.internet.email(),
          phone: '07' + faker.string.numeric(9),
          password: await bcrypt.hash('password123', 10),
          role: 'HOMEOWNER',
        },
      });
    })
  );

  // Create Technicians
  const technicians = await Promise.all(
    Array.from({ length: 3 }).map(async () => {
      const user = await prisma.user.create({
        data: {
          fullName: faker.person.fullName(),
          email: faker.internet.email(),
          phone: '07' + faker.string.numeric(9),
          password: await bcrypt.hash('password123', 10),
          role: 'TECHNICIAN',
        },
      });

      return prisma.technician.create({
        data: {
          userId: user.id,
          specialization: faker.person.jobType(),
          rating: parseFloat((Math.random() * 5).toFixed(1)),
        },
      });
    })
  );

  // Create Jobs
  await Promise.all(
    homeowners.map(async (homeowner) => {
      await prisma.job.create({
        data: {
          issue: faker.lorem.sentence(4),
          status: 'PENDING',
          scheduledAt: faker.date.soon({ days: 10 }),
          userId: homeowner.id,
          technicianId: faker.helpers.arrayElement(technicians).id, // ✅ Technician.id now (not userId)
        },
      });
    })
  );

  console.log('✅ Seeding complete.');
}

main()
  .catch((e) => {
    console.error('❌ Seed error', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
