// backend/scripts/autoReleaseCompletedJobs.ts
import <PERSON><PERSON> from 'stripe';
import { prisma } from '../src/config/db';
import { env } from '../src/config/env';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: env.STRIPE_API_VERSION as any,
});

const runAutoRelease = async () => {
  console.log('🔁 Starting auto-release check...');

  const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

  const jobs = await prisma.job.findMany({
    where: {
      status: 'COMPLETED',
      completedAt: { lt: cutoff },
      payment: {
        isReleased: false,
      },
    },
    include: {
      payment: true,
    },
  });

  if (jobs.length === 0) {
    console.log('✅ No jobs eligible for auto-release.');
    return;
  }

  console.log(`⚠️ Found ${jobs.length} jobs to auto-release.`);

  for (const job of jobs) {
    const intentId = job.payment?.stripePaymentIntentId;

    try {
      if (!intentId) throw new Error('Missing PaymentIntent');

      await stripe.paymentIntents.capture(intentId);

      await prisma.payment.update({
        where: { jobId: job.id },
        data: {
          isReleased: true,
          releasedAt: new Date(),
        },
      });

      await prisma.job.update({
        where: { id: job.id },
        data: {
          confirmedAt: new Date(), // Auto-confirm if homeowner didn't
        },
      });

      console.log(`✅ Auto-released payment for job: ${job.id}`);
    } catch (err) {
      console.error(`❌ Failed to auto-release job ${job.id}:`, err);
    }
  }
};

runAutoRelease().finally(() => {
  prisma.$disconnect();
});
