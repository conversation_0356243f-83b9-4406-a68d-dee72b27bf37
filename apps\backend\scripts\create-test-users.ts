// scripts/create-test-users.ts
import { prisma } from '../src/config/db';
import bcrypt from 'bcryptjs';

async function createTestUsers() {
  console.log('🧪 Creating test users...');

  try {
    const testPassword = process.env.TEST_USER_PASSWORD || 'TestPassword123!';
    const testDomain = process.env.TEST_EMAIL_DOMAIN || 'test.local';

    // Create test homeowner
    const homeowner = await prisma.user.create({
      data: {
        fullName: 'Test Homeowner',
        email: `homeowner@${testDomain}`,
        phone: `+44${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        password: await bcrypt.hash(testPassword, 10),
        role: 'HOMEOWNER',
      },
    });

    console.log('✅ Created test homeowner:', {
      email: `homeowner@${testDomain}`,
      password: testPassword,
      role: 'HOMEOWNER'
    });

    // Create test technician user
    const technicianUser = await prisma.user.create({
      data: {
        fullName: 'Test Technician',
        email: `technician@${testDomain}`,
        phone: `+44${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        password: await bcrypt.hash(testPassword, 10),
        role: 'TECHNICIAN',
      },
    });

    // Create technician profile
    const technician = await prisma.technician.create({
      data: {
        userId: technicianUser.id,
        specialization: 'Plumbing',
        rating: 4.8,
        isAvailable: true,
      },
    });

    console.log('✅ Created test technician:', {
      email: '<EMAIL>',
      password: 'password123',
      role: 'TECHNICIAN',
      specialization: 'Plumbing'
    });

    // Create a test job for the homeowner
    const job = await prisma.job.create({
      data: {
        issue: 'Kitchen sink is leaking and needs repair',
        description: 'The kitchen sink has been leaking for a few days. Water is dripping from the faucet and pooling under the sink.',
        priority: 'medium',
        status: 'PENDING',
        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        userId: homeowner.id,
        technicianId: technician.id,
      },
    });

    console.log('✅ Created test job:', {
      id: job.id,
      issue: job.issue,
      status: job.status
    });

    console.log('\n🎯 Test Credentials:');
    console.log('==================');
    console.log('Homeowner Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: password123');
    console.log('');
    console.log('Technician Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: password123');
    console.log('==================');

  } catch (error) {
    console.error('❌ Error creating test users:', error);
    throw error;
  }
}

createTestUsers()
  .catch((e) => {
    console.error('❌ Script error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
