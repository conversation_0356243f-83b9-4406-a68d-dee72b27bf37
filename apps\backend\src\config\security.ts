import helmet, { HelmetOptions } from 'helmet';
import cors from 'cors';
import { env } from './env';
import { logger } from '../utils/logger';

// Production allowed origins (must be explicitly set in environment)
const productionAllowedOrigins = env.NODE_ENV === 'production'
  ? (env.CORS_ORIGIN ? env.CORS_ORIGIN.split(',').map(origin => origin.trim()) : [])
  : [];

// Development allowed origins
const developmentAllowedOrigins = [
  'http://localhost:3000',     // Web admin panel (Vite dev)
  'http://localhost:5173',     // Web admin panel (Vite default)
  'http://localhost:3001',     // Backend server
  'http://localhost:19006',    // Mobile app development
  'http://localhost:8081',     // Mobile app Metro bundler
  'exp://localhost:8081',      // Expo development
];

// Get allowed origins based on environment
const allowedOrigins = env.NODE_ENV === 'production'
  ? productionAllowedOrigins
  : (env.CORS_ORIGIN
      ? env.CORS_ORIGIN.split(',').map(origin => origin.trim())
      : developmentAllowedOrigins);

// Validate production origins
if (env.NODE_ENV === 'production' && allowedOrigins.length === 0) {
  throw new Error('❌ Production environment requires explicit CORS_ORIGIN configuration');
}

export const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    const requestInfo = {
      origin,
      environment: env.NODE_ENV,
      timestamp: new Date().toISOString(),
    };

    // Handle requests with no origin
    if (!origin) {
      if (env.NODE_ENV === 'development') {
        logger.debug('CORS: Allowing request with no origin (development)', requestInfo);
        callback(null, true);
        return;
      } else {
        logger.warn('CORS: Blocked request with no origin (production)', requestInfo);
        callback(new Error('Origin header required in production'));
        return;
      }
    }

    // Production: Strict origin checking
    if (env.NODE_ENV === 'production') {
      if (allowedOrigins.includes(origin)) {
        logger.debug('CORS: Allowed production origin', requestInfo);
        callback(null, true);
      } else {
        logger.warn('CORS: Blocked unauthorized origin in production', {
          ...requestInfo,
          allowedOriginsCount: allowedOrigins.length,
        });
        callback(new Error('Origin not allowed by CORS policy'));
      }
      return;
    }

    // Development: More flexible but still secure
    if (env.NODE_ENV === 'development') {
      // Check explicit allowed origins first
      if (allowedOrigins.includes(origin)) {
        logger.debug('CORS: Allowed explicit origin (development)', requestInfo);
        callback(null, true);
        return;
      }

      // Allow specific localhost patterns for development
      const devOriginPatterns = [
        /^http:\/\/localhost:\d+$/,
        /^http:\/\/127\.0\.0\.1:\d+$/,
        /^https:\/\/localhost:\d+$/,
        /^https:\/\/127\.0\.0\.1:\d+$/,
        /^exp:\/\/\d+\.\d+\.\d+\.\d+:\d+$/,
        /^http:\/\/\d+\.\d+\.\d+\.\d+:\d+$/,
        /^https:\/\/\d+\.\d+\.\d+\.\d+:\d+$/,
      ];

      const isValidDevOrigin = devOriginPatterns.some(pattern => pattern.test(origin));

      if (isValidDevOrigin) {
        logger.debug('CORS: Allowed development pattern origin', requestInfo);
        callback(null, true);
        return;
      }
    }

    // Block all other origins
    logger.warn('CORS: Blocked unauthorized origin', {
      ...requestInfo,
      allowedOriginsCount: allowedOrigins.length,
      reason: 'Origin not in allowed list and does not match development patterns',
    });

    callback(new Error('Origin not allowed by CORS policy'));
  },

  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],

  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'X-API-Key',
    'X-Request-ID',
    'Accept',
    'Origin',
    'User-Agent',
  ],

  exposedHeaders: [
    'X-Request-ID',
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
  ],

  credentials: true,
  maxAge: env.NODE_ENV === 'production' ? 86400 : 300, // 24h prod, 5min dev
  optionsSuccessStatus: 200,
  preflightContinue: false,
};

// Helmet configuration
export const helmetOptions: HelmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: env.NODE_ENV === 'development'
        ? ["'self'", "'unsafe-inline'"] // Remove unsafe-eval, keep unsafe-inline for dev only
        : ["'self'"], // Production: only self
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
      connectSrc: [
        "'self'",
        'https://api.stripe.com',
        'https://exp.host', // Expo push notifications
        'wss:', // WebSocket connections
        'ws:' // WebSocket connections (dev only)
      ].filter(src => env.NODE_ENV === 'development' || !src.startsWith('ws:')),
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", 'blob:'],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      manifestSrc: ["'self'"],
      workerSrc: ["'self'", 'blob:'],
    },
    reportOnly: env.NODE_ENV === 'development', // Only report in dev
  },
  crossOriginEmbedderPolicy: false, // Disable for Socket.IO compatibility
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
  crossOriginResourcePolicy: { policy: 'cross-origin' }, // Allow cross-origin for mobile
  dnsPrefetchControl: { allow: false },
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: process.env.NODE_ENV === 'production' ? {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  } : false, // Disable HSTS in development
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: { permittedPolicies: 'none' },
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  xssFilter: true,
};

// Input sanitization configuration
export const sanitizationOptions = {
  // Remove HTML tags and dangerous characters
  allowedTags: [],
  allowedAttributes: {},
  disallowedTagsMode: 'discard',
  enforceHtmlBoundary: true,
};