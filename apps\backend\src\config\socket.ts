import { Server, Socket } from 'socket.io';
const jwt = require('jsonwebtoken');
import http from 'http';
import { registerSockets } from '../sockets';
import { env } from './env';

interface UserPayload {
  id: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  email: string;
  // add more fields if needed
}

interface AuthenticatedSocket extends Socket {
  user?: UserPayload;
}

export let io: Server;

/**
 * Initialise the global socket instance.
 * Attaches JWT auth guard and user room tracking.
 */
export const initSocket = (server: http.Server) => {
  io = new Server(server, {
    cors: {
      origin: env.SOCKET_CORS_ORIGIN?.split(',') || (env.NODE_ENV === 'development'
        ? ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:19006']
        : false),
      methods: ['GET', 'POST'],
      credentials: true
    },
    transports: ['websocket', 'polling'],
    allowEIO3: true,
    pingTimeout: env.SOCKET_PING_TIMEOUT || 60000,
    pingInterval: env.SOCKET_PING_INTERVAL || 25000,
  });

  // JWT Authentication Middleware
  io.use((socket: AuthenticatedSocket, next) => {
    const token = socket.handshake.auth?.token;

    if (!token) {
      return next(new Error('Unauthenticated: No token'));
    }

    try {
      const decoded = jwt.verify(token, env.JWT_SECRET as string) as UserPayload;
      socket.user = decoded;
      next();
    } catch (err) {
      return next(new Error('Unauthenticated: Invalid token'));
    }
  });

  // Register all socket handlers
  registerSockets(io);
};
