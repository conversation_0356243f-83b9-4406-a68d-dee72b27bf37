// CTRON Home - Chat Controller
// Handles chat creation, messaging, and chat management

import { Request, Response, NextFunction } from 'express';
import { ChatService } from '../services/chat.service';
import { AuthenticatedRequest } from '../types/auth';
import {
  createChatSchema,
  sendMessageSchema,
  getChatMessagesSchema,
  markMessageReadSchema,
  markMessagesReadSchema,
  updateChatStatusSchema,
  type CreateChatInput,
  type SendMessageInput,
  type GetChatMessagesInput,
  type MarkMessageReadInput,
  type MarkMessagesReadInput,
  type UpdateChatStatusInput
} from '../schemas/chat.schemas';
import { io } from '../config/socket';

export const ChatController = {
  /**
   * GET /api/chats/job/:jobId
   * Get or create chat for a specific job
   */
  getOrCreateJobChat: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const { jobId } = req.params;

      // Validate input
      const validatedData = createChatSchema.parse({ jobId }) as CreateChatInput;

      // Check if user has access to this job
      const hasAccess = await ChatService.verifyJobAccess(validatedData.jobId, user.userId);
      if (!hasAccess) {
        res.status(403).json({ message: 'Access denied to this job' });
        return;
      }

      const chat = await ChatService.getOrCreateJobChat(validatedData.jobId, user.userId);

      res.status(200).json({
        success: true,
        data: chat
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * POST /api/chats/:chatId/messages
   * Send a message to a chat
   */
  sendMessage: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const { chatId } = req.params;
      const messageData = { chatId, ...req.body };

      // Validate input
      const validatedData = sendMessageSchema.parse(messageData) as SendMessageInput;

      // Check if user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(validatedData.chatId, user.userId);
      if (!isParticipant) {
        res.status(403).json({ message: 'Access denied to this chat' });
        return;
      }

      const message = await ChatService.sendMessage(
        validatedData.chatId,
        user.userId,
        validatedData.content,
        validatedData.attachments
      );

      // Emit real-time message to chat participants
      io.to(`chat:${validatedData.chatId}`).emit('message:new', {
        message,
        chatId: validatedData.chatId
      });

      res.status(201).json({
        success: true,
        data: message
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * GET /api/chats/:chatId/messages
   * Get paginated message history for a chat
   */
  getChatMessages: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const { chatId } = req.params;
      const queryData = { chatId, ...req.query };

      // Validate input
      const validatedData = getChatMessagesSchema.parse(queryData) as GetChatMessagesInput;

      // Check if user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(validatedData.chatId, user.userId);
      if (!isParticipant) {
        res.status(403).json({ message: 'Access denied to this chat' });
        return;
      }

      const result = await ChatService.getChatMessages(
        validatedData.chatId,
        {
          page: validatedData.page || 1,
          limit: validatedData.limit || 50,
          before: validatedData.before,
          after: validatedData.after
        }
      );

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * PUT /api/messages/:messageId/read
   * Mark a specific message as read
   */
  markMessageRead: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const { messageId } = req.params;

      // Validate input
      const validatedData = markMessageReadSchema.parse({ messageId }) as MarkMessageReadInput;

      // Verify user can mark this message as read
      const canMarkRead = await ChatService.verifyMessageAccess(validatedData.messageId, user.userId);
      if (!canMarkRead) {
        res.status(403).json({ message: 'Access denied to this message' });
        return;
      }

      await ChatService.markMessageRead(validatedData.messageId, user.userId);

      res.status(200).json({
        success: true,
        message: 'Message marked as read'
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * PUT /api/chats/:chatId/messages/read
   * Mark all messages in a chat as read for the current user
   */
  markAllMessagesRead: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const { chatId } = req.params;

      // Validate input
      const validatedData = markMessagesReadSchema.parse({ chatId }) as MarkMessagesReadInput;

      // Check if user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(validatedData.chatId, user.userId);
      if (!isParticipant) {
        res.status(403).json({ message: 'Access denied to this chat' });
        return;
      }

      const updatedCount = await ChatService.markAllMessagesRead(validatedData.chatId, user.userId);

      res.status(200).json({
        success: true,
        message: `${updatedCount} messages marked as read`
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * GET /api/chats/user/:userId
   * Get all active chats for a user
   */
  getUserChats: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      // Users can only access their own chats
      const { userId } = req.params;
      if (userId !== user.userId) {
        res.status(403).json({ message: 'Access denied' });
        return;
      }

      const chats = await ChatService.getUserChats(user.userId);

      res.status(200).json({
        success: true,
        data: chats
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * PUT /api/chats/:chatId/status
   * Update chat status (ACTIVE, CLOSED, ARCHIVED)
   */
  updateChatStatus: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const { chatId } = req.params;
      const statusData = { chatId, ...req.body };

      // Validate input
      const parseResult = updateChatStatusSchema.safeParse(statusData);
      if (!parseResult.success) {
        res.status(400).json({
          message: 'Invalid input',
          errors: parseResult.error.errors
        });
        return;
      }

      // Extract validated values with proper typing
      const { chatId: validChatId, status: validStatus } = parseResult.data;

      // Type assertions to resolve TypeScript inference issues
      const chatIdString = validChatId as string;
      const statusValue = validStatus as any;

      // Check if user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(chatIdString, user.userId);
      if (!isParticipant) {
        res.status(403).json({ message: 'Access denied to this chat' });
        return;
      }

      const updatedChat = await ChatService.updateChatStatus(
        chatIdString,
        statusValue
      );

      // Emit status change to chat participants
      io.to(`chat:${chatIdString}`).emit('chat:statusChanged', {
        chatId: chatIdString,
        status: statusValue,
        updatedBy: user.userId
      });

      res.status(200).json({
        success: true,
        data: updatedChat
      });
    } catch (error) {
      next(error);
    }
  }
};
