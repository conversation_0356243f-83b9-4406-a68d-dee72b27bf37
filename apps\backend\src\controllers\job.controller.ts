// backend/src/controllers/job.controller.ts

import { Request, Response } from 'express';
import { JobModel } from '../models/job.model';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { JobStatus } from '@prisma/client';
import { prisma } from '../config/db';
import { PaymentModel } from '../models/payment.model';
import Stripe from 'stripe';
import { env } from '../config/env';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: env.STRIPE_API_VERSION as any,
});

export const JobController = {
  createJob: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    const { technicianId, issue, photoUrl, scheduledAt } = req.body;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      const job = await <PERSON>Model.createJob({
        userId: user.userId,
        technicianId,
        issue,
        photoUrl,
        scheduledAt: new Date(scheduledAt),
      });

      res.status(201).json({ job });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  getMyJobs: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;

    if (!user) {
      console.error('❌ getMyJobs: No user in request');
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      console.log('📥 Fetching jobs for user:', user.userId);
      const jobs = await JobModel.getJobsByUser(user.userId);
      console.log('✅ Jobs fetched successfully:', jobs.length);
      res.status(200).json({ jobs });
    } catch (err) {
      console.error('❌ Error in getMyJobs:', err);
      console.error('❌ User ID:', user.userId);
      console.error('❌ Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
      });
      res.status(500).json({
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err : undefined
      });
    }
  },

  getAssignedJobs: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      const jobs = await JobModel.getJobsByTechnician(user.userId);
      res.status(200).json({ jobs });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  updateJob: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    const { jobId } = req.params;
    const { issue, description, scheduledAt, priority } = req.body;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      // Check if job exists and belongs to the user
      const existingJob = await JobModel.getJobById(jobId);
      if (!existingJob) {
        res.status(404).json({ message: 'Job not found' });
        return;
      }

      if (existingJob.userId !== user.userId) {
        res.status(403).json({ message: 'You can only edit your own jobs' });
        return;
      }

      if (existingJob.status !== 'PENDING') {
        res.status(400).json({ message: 'Only pending jobs can be edited' });
        return;
      }

      const updateData: any = {};
      if (issue) updateData.issue = issue;
      if (description !== undefined) updateData.description = description;
      if (scheduledAt) updateData.scheduledAt = new Date(scheduledAt);
      if (priority) updateData.priority = priority;

      const updatedJob = await prisma.job.update({
        where: { id: jobId },
        data: updateData,
        include: {
          user: true,
          technician: {
            include: {
              user: true,
            },
          },
        },
      });

      res.status(200).json({ job: updatedJob });
    } catch (err) {
      console.error('❌ updateJob error:', err);
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  updateJobStatus: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    const { jobId } = req.params;
    const { status, photoUrl, proofImageKey, cancellationReason } = req.body;

    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Validate status
    const validStatuses = ['IN_PROGRESS', 'COMPLETED', 'CANCELLED'];
    if (!status || !validStatuses.includes(status)) {
      res.status(400).json({ message: 'Invalid status' });
      return;
    }

    try {
      // Check if job exists
      const existingJob = await JobModel.getJobById(jobId);
      if (!existingJob) {
        res.status(404).json({ message: 'Job not found' });
        return;
      }

      // Authorization checks
      if (status === 'CANCELLED') {
        // Only homeowners can cancel their own jobs
        if (user.role !== 'HOMEOWNER' || existingJob.userId !== user.userId) {
          res.status(403).json({ message: 'You can only cancel your own jobs' });
          return;
        }
      } else {
        // Only technicians can update to IN_PROGRESS or COMPLETED
        if (user.role !== 'TECHNICIAN' || existingJob.technicianId !== user.userId) {
          res.status(403).json({ message: 'Only assigned technicians can update job status' });
          return;
        }
      }

      let updatedJob;
      if (status === 'CANCELLED') {
        // Handle cancellation
        updatedJob = await prisma.job.update({
          where: { id: jobId },
          data: {
            status: status as JobStatus,
            cancellationReason,
            cancelledAt: new Date(),
          },
          include: {
            user: true,
            technician: {
              include: {
                user: true,
              },
            },
          },
        });
      } else {
        // Handle other status updates with proof
        const imageReference = proofImageKey || photoUrl;
        updatedJob = await JobModel.updateStatusWithProof(
          jobId,
          status as JobStatus,
          imageReference
        );
      }

      res.status(200).json({ job: updatedJob });
    } catch (err) {
      console.error('❌ updateJobStatus error:', err);
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  getJobDetails: async (req: Request, res: Response): Promise<void> => {
    const { jobId } = req.params;

    try {
      const job = await JobModel.getJobById(jobId);
      if (!job) {
        res.status(404).json({ message: 'Job not found' });
        return;
      }

      res.status(200).json({ job });
    } catch (err) {
      res.status(500).json({ message: 'Internal server error' });
    }
  },

  confirmJobCompletion: async (req: Request, res: Response): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    const { jobId } = req.params;

    if (!user || user.role !== 'HOMEOWNER') {
      res.status(403).json({ message: 'Only homeowners can confirm jobs' });
      return;
    }

    try {
      const job = await prisma.job.findUnique({ where: { id: jobId } });

      if (!job || job.userId !== user.userId) {
        res.status(404).json({ message: 'Job not found or not yours' });
        return;
      }

      if (job.status !== 'COMPLETED') {
        res.status(400).json({ message: 'Job must be completed first' });
        return;
      }

      const payment = await PaymentModel.getPaymentByJobId(jobId);
      if (!payment?.stripePaymentIntentId) {
        res.status(400).json({ message: 'No payment intent found' });
        return;
      }

      // Check if payment is already released
      if (payment.isReleased) {
        res.status(400).json({ message: 'Payment has already been released' });
        return;
      }

      // Check if payment is frozen
      if (payment.isFrozen) {
        res.status(400).json({ message: 'Payment is frozen and cannot be released' });
        return;
      }

      try {
        // ✅ Capture payment with proper error handling
        await stripe.paymentIntents.capture(payment.stripePaymentIntentId);
        console.log(`✅ Stripe payment captured successfully for job ${jobId}`);
      } catch (stripeError: any) {
        console.error('❌ Stripe payment capture failed:', {
          jobId,
          paymentIntentId: payment.stripePaymentIntentId,
          error: stripeError.message,
          code: stripeError.code,
          type: stripeError.type
        });

        // Handle specific Stripe errors
        if (stripeError.code === 'payment_intent_unexpected_state') {
          res.status(400).json({
            message: 'Payment cannot be captured in its current state',
            details: 'The payment may have already been captured or cancelled'
          });
        } else if (stripeError.code === 'payment_intent_not_found') {
          res.status(404).json({
            message: 'Payment intent not found in Stripe',
            details: 'The payment intent may have been deleted or does not exist'
          });
        } else {
          res.status(500).json({
            message: 'Payment capture failed',
            details: 'There was an error processing the payment with Stripe'
          });
        }
        return;
      }

      try {
        // ✅ Mark as released in database
        await PaymentModel.markAsReleased(jobId);
        console.log(`✅ Payment marked as released in database for job ${jobId}`);
      } catch (dbError: any) {
        console.error('❌ Database update failed after successful Stripe capture:', {
          jobId,
          error: dbError.message
        });

        // This is a critical error - payment was captured but DB wasn't updated
        // TODO: Implement compensation logic or manual intervention alert
        res.status(500).json({
          message: 'Payment was captured but database update failed',
          details: 'Please contact support immediately'
        });
        return;
      }

      try {
        // ✅ Add job.confirmedAt timestamp
        await prisma.job.update({
          where: { id: jobId },
          data: { confirmedAt: new Date() },
        });
        console.log(`✅ Job confirmation timestamp updated for job ${jobId}`);
      } catch (jobUpdateError: any) {
        console.error('❌ Job confirmation timestamp update failed:', {
          jobId,
          error: jobUpdateError.message
        });
        // This is less critical - payment was processed successfully
        // Continue with success response but log the error
      }

      res.status(200).json({
        message: 'Job confirmed and payment released to technician',
        jobId,
        confirmedAt: new Date().toISOString()
      });
    } catch (err) {
      console.error('❌ Error in confirmJobCompletion:', err);
      res.status(500).json({ message: 'Failed to confirm and release payment' });
    }
  },
};
