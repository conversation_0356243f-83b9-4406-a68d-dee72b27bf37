// src/controllers/payment.controller.ts

import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'stripe';
import { env } from '../config/env';
import { PaymentModel } from '../models/payment.model';
import { prisma } from '../config/db';
import { AuthenticatedRequest } from '../middleware/auth.middleware';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: env.STRIPE_API_VERSION as any,
});

export const PaymentController = {
  /**
   * Step 1: Create Stripe PaymentIntent & save to DB
   */
  createPayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const { jobId, amount, currency = 'GBP' } = req.body;

    try {
      const intent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency,
        metadata: { jobId },
        automatic_payment_methods: { enabled: true },
      });

      await PaymentModel.createPayment(
        jobId,
        user.userId,
        amount,
        currency,
        intent.id
      );

      res.status(200).json({ clientSecret: intent.client_secret });
    } catch (err) {
      console.error('❌ Error creating payment intent:', err);
      next(err);
    }
  },

  /**
   * Step 2: Get payment status (webhook handling moved to webhook.controller.ts)
   */

  /**
   * Step 3: Get payment by jobId
   */
  getPayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      res.status(200).json({ payment });
    } catch (err) {
      next(err);
    }
  },

  /**
   * Step 4: Capture payment on homeowner confirmation
   */
  capturePayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment || !payment.stripePaymentIntentId) {
        res.status(404).json({ message: 'Payment or intent not found' });
        return;
      }

      // Check if payment is already released
      if (payment.isReleased) {
        res.status(400).json({ message: 'Payment has already been released' });
        return;
      }

      // Check if payment is frozen
      if (payment.isFrozen) {
        res.status(400).json({ message: 'Payment is frozen and cannot be released' });
        return;
      }

      try {
        // Capture payment with proper error handling
        await stripe.paymentIntents.capture(payment.stripePaymentIntentId);
        console.log(`✅ Stripe payment captured successfully for job ${jobId}`);
      } catch (stripeError: any) {
        console.error('❌ Stripe payment capture failed:', {
          jobId,
          paymentIntentId: payment.stripePaymentIntentId,
          error: stripeError.message,
          code: stripeError.code,
          type: stripeError.type
        });

        // Handle specific Stripe errors
        if (stripeError.code === 'payment_intent_unexpected_state') {
          res.status(400).json({
            message: 'Payment cannot be captured in its current state',
            details: 'The payment may have already been captured or cancelled'
          });
        } else if (stripeError.code === 'payment_intent_not_found') {
          res.status(404).json({
            message: 'Payment intent not found in Stripe',
            details: 'The payment intent may have been deleted or does not exist'
          });
        } else {
          res.status(500).json({
            message: 'Payment capture failed',
            details: 'There was an error processing the payment with Stripe'
          });
        }
        return;
      }

      try {
        // Mark as released in database
        await PaymentModel.markAsReleased(jobId);
        console.log(`✅ Payment marked as released in database for job ${jobId}`);
      } catch (dbError: any) {
        console.error('❌ Database update failed after successful Stripe capture:', {
          jobId,
          error: dbError.message
        });

        res.status(500).json({
          message: 'Payment was captured but database update failed',
          details: 'Please contact support immediately'
        });
        return;
      }

      res.status(200).json({
        message: 'Payment captured and released',
        jobId,
        capturedAt: new Date().toISOString()
      });
    } catch (err) {
      console.error('❌ Unexpected error in capturePayment:', err);
      next(err);
    }
  },

  /**
   * Step 5: Admin override release
   */
  releaseManually: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      if (payment.isReleased) {
        res.status(400).json({ message: 'Payment already released' });
        return;
      }

      if (!payment.stripePaymentIntentId) {
        res.status(400).json({ message: 'No Stripe payment intent found' });
        return;
      }

      try {
        // Capture payment with proper error handling
        await stripe.paymentIntents.capture(payment.stripePaymentIntentId);
        console.log(`✅ Admin manual capture successful for job ${jobId}`);
      } catch (stripeError: any) {
        console.error('❌ Admin manual Stripe capture failed:', {
          jobId,
          paymentIntentId: payment.stripePaymentIntentId,
          error: stripeError.message,
          code: stripeError.code,
          type: stripeError.type
        });

        // Handle specific Stripe errors
        if (stripeError.code === 'payment_intent_unexpected_state') {
          res.status(400).json({
            message: 'Payment cannot be captured in its current state',
            details: 'The payment may have already been captured or cancelled'
          });
        } else if (stripeError.code === 'payment_intent_not_found') {
          res.status(404).json({
            message: 'Payment intent not found in Stripe',
            details: 'The payment intent may have been deleted or does not exist'
          });
        } else {
          res.status(500).json({
            message: 'Payment capture failed',
            details: 'There was an error processing the payment with Stripe'
          });
        }
        return;
      }

      try {
        // Mark as released in database
        await PaymentModel.markAsReleased(jobId);
        console.log(`✅ Admin manual release marked in database for job ${jobId}`);
      } catch (dbError: any) {
        console.error('❌ Database update failed after successful admin Stripe capture:', {
          jobId,
          error: dbError.message
        });

        res.status(500).json({
          message: 'Payment was captured but database update failed',
          details: 'Please contact support immediately'
        });
        return;
      }

      try {
        // Update job confirmation timestamp
        await prisma.job.update({
          where: { id: jobId },
          data: {
            confirmedAt: new Date(),
          },
        });
        console.log(`✅ Job confirmation timestamp updated for admin release ${jobId}`);
      } catch (jobUpdateError: any) {
        console.error('❌ Job confirmation timestamp update failed:', {
          jobId,
          error: jobUpdateError.message
        });
        // This is less critical - payment was processed successfully
      }

      res.status(200).json({
        message: 'Manual payment release successful',
        jobId,
        releasedAt: new Date().toISOString()
      });
    } catch (err) {
      console.error('❌ Unexpected error in releaseManually:', err);
      next(err);
    }
  },

  /**
   * Step 6: Admin dispute – freeze payment
   */
  freezePayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;
    const { reason } = req.body;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      if (payment.isReleased) {
        res.status(400).json({ message: 'Payment already released' });
        return;
      }

      if (payment.isFrozen) {
        res.status(400).json({ message: 'Payment already frozen' });
        return;
      }

      await PaymentModel.freezePayment(jobId, reason);
      res.status(200).json({ message: 'Payment frozen successfully' });
    } catch (err) {
      console.error('❌ Error freezing payment:', err);
      next(err);
    }
  },

  /**
   * Step 7: Admin unfreeze – resolve dispute
   */
  unfreezePayment: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { jobId } = req.params;

    try {
      const payment = await PaymentModel.getPaymentByJobId(jobId);

      if (!payment) {
        res.status(404).json({ message: 'Payment not found' });
        return;
      }

      if (!payment.isFrozen) {
        res.status(400).json({ message: 'Payment is not frozen' });
        return;
      }

      await PaymentModel.unfreezePayment(jobId);
      res.status(200).json({ message: 'Payment unfrozen successfully' });
    } catch (err) {
      console.error('❌ Error unfreezing payment:', err);
      next(err);
    }
  },
};

// Note: Webhook handling has been consolidated to webhook.controller.ts
// to prevent duplicate payment processing
