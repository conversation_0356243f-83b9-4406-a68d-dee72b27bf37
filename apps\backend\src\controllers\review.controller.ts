// 📁 File: src/controllers/review.controller.ts

import { Request, Response, NextFunction } from 'express';
import { ReviewModel } from '../models/review.model';
import { AuthenticatedRequest } from '../middleware/auth.middleware';

export const ReviewController = {
  /**
   * Create a new review for a job
   */
  createReview: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    const { user } = req as AuthenticatedRequest;
    const { jobId, rating, comment } = req.body;

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    try {
      const review = await ReviewModel.createReview({
        userId: user.userId,
        jobId,
        rating,
        comment,
      });

      return res.status(201).json({ review });
    } catch (err) {
      console.error('❌ Error creating review:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }
  },

  /**
   * Get all reviews left by the currently authenticated user
   */
  getUserReviews: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    const { user } = req as AuthenticatedRequest;

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    try {
      const reviews = await ReviewModel.getReviewsByUser(user.userId);
      return res.status(200).json({ reviews });
    } catch (err) {
      console.error('❌ Error fetching user reviews:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }
  },

  /**
   * Get all reviews for a specific job
   */
  getJobReviews: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    const { jobId } = req.params;

    try {
      const reviews = await ReviewModel.getReviewsByJob(jobId);
      return res.status(200).json({ reviews });
    } catch (err) {
      console.error(`❌ Error fetching reviews for job ${jobId}:`, err);
      return res.status(500).json({ message: 'Internal server error' });
    }
  },

  /**
   * Get all reviews for a specific technician
   */
  getReviewsForTechnician: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    const { technicianId } = req.params;

    try {
      const reviews = await ReviewModel.getReviewsByTechnician(technicianId);
      return res.status(200).json({ reviews });
    } catch (err) {
      console.error(`❌ Error fetching reviews for technician ${technicianId}:`, err);
      return res.status(500).json({ message: 'Internal server error' });
    }
  },

  /**
   * Delete a review by ID
   */
  deleteReview: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    const { id } = req.params;
    const { user } = req as AuthenticatedRequest;

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    try {
      await ReviewModel.deleteReview(id, user.userId);
      return res.status(204).send();
    } catch (err) {
      console.error(`❌ Error deleting review ${id}:`, err);
      return res.status(500).json({ message: 'Internal server error' });
    }
  },
};
