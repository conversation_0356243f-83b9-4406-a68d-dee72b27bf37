// 📁 File: src/controllers/settings.controller.ts

import { Request, Response } from 'express';
import { prisma } from '../config/db';

export const SettingsController = {
  getSettings: async (req: Request, res: Response) => {
    try {
      const settings = await prisma.setting.findFirst();
      res.status(200).json(settings);
    } catch (err) {
      res.status(500).json({ message: 'Failed to fetch settings' });
    }
  },

  updateSettings: async (req: Request, res: Response) => {
    const { gracePeriodHours, stripeTestMode, statusMessage } = req.body;

    try {
      const existing = await prisma.setting.findFirst();
      if (!existing) {
        const created = await prisma.setting.create({
          data: {
            gracePeriodHours,
            stripeTestMode,
            statusMessage,
          },
        });
        return res.status(201).json(created);
      }

      const updated = await prisma.setting.update({
        where: { id: existing.id },
        data: {
          gracePeriodHours,
          stripeTestMode,
          statusMessage,
        },
      });

      res.status(200).json(updated);
    } catch (err) {
      res.status(500).json({ message: 'Failed to update settings' });
    }
  },
};
