// backend/src/controllers/technician.controller.ts

import { Request, Response, NextFunction } from 'express';
import { TechnicianModel } from '../models/technician.model';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { prisma } from '../config/db';
import Stripe from 'stripe';
import { asyncHandler } from '../utils/asyncHandler';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export const TechnicianController = {
  // Onboard a new technician profile
  onboard: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const { specialization } = req.body;
    try {
      const technician = await TechnicianModel.createTechnician({
        userId: user.userId,
        specialization,
      });
      res.status(201).json({ technician });
    } catch (err) {
      next(err);
    }
  },

  // Create a Stripe Identity verification session
  createVerificationSession: asyncHandler(async (req: Request, res: Response) => {
    const { technicianId } = req.body;

    const session = await stripe.identity.verificationSessions.create({
      type: 'document',
      metadata: { technicianId },
      return_url: process.env.STRIPE_RETURN_URL!,
    });

    res.json({ url: session.url });
  }),

  // Fetch the technician's own profile/dashboard info
  getDashboard: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    try {
      const technician = await TechnicianModel.getByUserId(user.userId);
      if (!technician) {
        res.status(404).json({ message: 'Technician not found' });
        return;
      }
      res.status(200).json({ technician });
    } catch (err) {
      next(err);
    }
  },

  // Toggle online/offline availability
  setAvailability: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const { user } = req as AuthenticatedRequest;
    if (!user) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const { isAvailable } = req.body;
    try {
      const updated = await TechnicianModel.updateAvailability(
        user.userId,
        isAvailable
      );
      res.status(200).json({ technician: updated });
    } catch (err) {
      next(err);
    }
  },

  // Find nearby technicians based on location + minRating
  getNearbyTechnicians: async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { lat, lng, minRating } = req.query;

      const latitude = parseFloat(lat as string);
      const longitude = parseFloat(lng as string);
      const ratingThreshold = parseFloat(minRating as string) || 0;

      if (isNaN(latitude) || isNaN(longitude)) {
        res.status(400).json({ message: 'Latitude and longitude are required' });
        return;
      }

      const technicians = await prisma.$queryRawUnsafe(`
        SELECT
          t.*,
          u."fullName",
          u."phone",
          (
            6371 * acos(
              cos(radians(${latitude}))
              * cos(radians(t."latitude"))
              * cos(radians(t."longitude") - radians(${longitude}))
              + sin(radians(${latitude})) * sin(radians(t."latitude"))
            )
          ) AS distance
        FROM "Technician" t
        JOIN "User" u ON t."userId" = u."id"
        WHERE t."isAvailable" = true
          AND t."latitude" IS NOT NULL
          AND t."longitude" IS NOT NULL
          AND (t."rating" IS NULL OR t."rating" >= ${ratingThreshold})
        ORDER BY distance ASC
        LIMIT 15;
      `);

      res.status(200).json({ technicians });
    } catch (err) {
      next(err);
    }
  },
};
