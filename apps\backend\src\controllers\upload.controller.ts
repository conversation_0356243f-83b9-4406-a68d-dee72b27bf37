// src/controllers/upload.controller.ts

import { Request, Response } from 'express';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

const s3 = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export const UploadController = {
  getPresignedUrl: async (req: Request, res: Response): Promise<void> => {
    try {
      const { fileType } = req.body;

      if (!fileType) {
        res.status(400).json({ error: 'Missing file type' });
        return;
      }

      const extension = fileType.split('/')[1] || 'jpg';
      const fileKey = `uploads/${uuidv4()}.${extension}`;

      const command = new PutObjectCommand({
        Bucket: process.env.AWS_BUCKET_NAME!,
        Key: fileKey,
        ContentType: fileType,
      });

      const url = await getSignedUrl(s3, command, { expiresIn: 60 });

      res.status(200).json({ url, key: fileKey });
    } catch (error) {
      console.error('Error generating presigned URL:', error);
      res.status(500).json({ error: 'Failed to generate upload URL' });
    }
  },
};
