generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  fullName  String
  password  String
  phone     String?
  role      Role
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt // ✅ added

  // 🔁 Relations
  technician Technician?
  jobs       Job[]       @relation("JobUser")
  reviews    Review[]
  payments   Payment[] // ✅ added for back-relation
}

model Technician {
  id             String   @id @default(uuid())
  userId         String   @unique
  specialization String
  isAvailable    Boolean  @default(true)
  rating         Float? // ✅ added
  createdAt      DateTime @default(now())

  // 🔁 Relations
  user    User     @relation(fields: [userId], references: [id])
  jobs    Job[]    @relation("JobTechnician")
  reviews Review[]
}

model Job {
  id          String    @id @default(uuid())
  issue       String
  photoUrl    String?
  status      JobStatus @default(PENDING)
  scheduledAt DateTime
  createdAt   DateTime? @default(now())
  completedAt DateTime?

  // Relations
  userId String
  user   User   @relation("JobUser", fields: [userId], references: [id])

  technicianId String
  technician   Technician @relation("JobTechnician", fields: [technicianId], references: [id])

  review  Review?
  payment Payment?
}

model Payment {
  id                    String    @id @default(uuid())
  jobId                 String    @unique
  amount                Float
  currency              String    @default("GBP")
  stripePaymentIntentId String    @unique // ✅ added
  isReleased            Boolean   @default(false) // ✅ added
  releasedAt            DateTime? // ✅ added
  createdAt             DateTime  @default(now())

  job    Job    @relation(fields: [jobId], references: [id])
  user   User   @relation(fields: [userId], references: [id])
  userId String // ✅ added to support user.payments
}

model Review {
  id     String @id @default(uuid())
  userId String
  user   User   @relation(fields: [userId], references: [id])

  jobId String @unique
  job   Job    @relation(fields: [jobId], references: [id])

  rating       Int
  comment      String?
  createdAt    DateTime    @default(now())
  technicianId String?
  Technician   Technician? @relation(fields: [technicianId], references: [id])
}

enum Role {
  HOMEOWNER
  TECHNICIAN
}

enum JobStatus {
  PENDING
  ACCEPTED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
