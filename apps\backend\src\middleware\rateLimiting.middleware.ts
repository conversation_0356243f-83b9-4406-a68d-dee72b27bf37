// CTRON Home - Rate Limiting Middleware
// Implements comprehensive rate limiting for different endpoint types

import rateLimitLib from 'express-rate-limit';
import { Request, Response, RequestHandler } from 'express';
import { env } from '../config/env';
import { logger } from '../utils/logger';

// Rate limiting configuration for different endpoint types
export const rateLimits = {
  // Authentication endpoints - very strict limits
  auth: {
    windowMs: env.RATE_LIMIT_WINDOW_MS, // 15 minutes
    max: 5, // 5 attempts per window
    message: {
      error: 'Too many authentication attempts',
      message: 'Please try again later',
      retryAfter: Math.ceil(env.RATE_LIMIT_WINDOW_MS / 1000 / 60) // minutes
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true, // Don't count successful requests
  },

  // Chat endpoints - moderate limits for real-time communication
  chat: {
    windowMs: 60000, // 1 minute
    max: 30, // 30 messages per minute
    message: {
      error: 'Too many messages sent',
      message: 'Please slow down your messaging',
      retryAfter: 60
    },
    standardHeaders: true,
    legacyHeaders: false,
  },

  // General API endpoints - standard limits
  api: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    max: env.RATE_LIMIT_MAX_REQUESTS,
    message: {
      error: 'Too many requests',
      message: 'Please try again later',
      retryAfter: Math.ceil(env.RATE_LIMIT_WINDOW_MS / 1000 / 60)
    },
    standardHeaders: true,
    legacyHeaders: false,
  },

  // File upload endpoints - very strict limits
  upload: {
    windowMs: 300000, // 5 minutes
    max: 10, // 10 uploads per 5 minutes
    message: {
      error: 'Too many file uploads',
      message: 'Please wait before uploading more files',
      retryAfter: 300
    },
    standardHeaders: true,
    legacyHeaders: false,
  },

  // Payment endpoints - extremely strict limits
  payment: {
    windowMs: 900000, // 15 minutes
    max: 3, // 3 payment attempts per 15 minutes
    message: {
      error: 'Too many payment attempts',
      message: 'Please contact support if you continue to have issues',
      retryAfter: 900
    },
    standardHeaders: true,
    legacyHeaders: false,
  }
};

// Custom key generator for user-specific rate limiting
const keyGenerator = (req: Request): string => {
  // Use user ID if authenticated, otherwise fall back to IP
  const user = (req as any).user;
  if (user?.userId) {
    return `user:${user.userId}`;
  }
  return req.ip || 'unknown';
};

// Custom handler for rate limit exceeded
const onLimitReached = (req: Request, res: Response) => {
  const user = (req as any).user;
  const identifier = user?.userId ? `User ${user.userId}` : `IP ${req.ip}`;

  // Log security event for monitoring
  if (user?.userId) {
    // Could integrate with security monitoring service here
    logger.warn(`🔒 Security Alert: Rate limit exceeded for user ${user.userId}`);
  } else {
    logger.warn(`🚫 Rate limit exceeded for IP ${req.ip} on ${req.path}`);
  }
};

// Create rate limiter with specific configuration
export const createRateLimit = (config: typeof rateLimits.api): RequestHandler => {
  return rateLimitLib({
    ...config,
    keyGenerator,
    handler: onLimitReached,
    // Skip rate limiting in test environment
    skip: (req: Request) => {
      return process.env.NODE_ENV === 'test';
    },
  });
};

// Pre-configured rate limiters for different endpoint types
export const authRateLimit = createRateLimit(rateLimits.auth);
export const chatRateLimit = createRateLimit(rateLimits.chat);
export const apiRateLimit = createRateLimit(rateLimits.api);
export const uploadRateLimit = createRateLimit(rateLimits.upload);
export const paymentRateLimit = createRateLimit(rateLimits.payment);

// Generic rate limiter function for custom configurations
export const rateLimitMiddleware = (config: typeof rateLimits.api): RequestHandler => {
  return createRateLimit(config);
};

export default {
  rateLimits,
  rateLimitMiddleware,
  authRateLimit,
  chatRateLimit,
  apiRateLimit,
  uploadRateLimit,
  paymentRateLimit
};
