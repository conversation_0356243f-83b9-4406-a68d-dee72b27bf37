// src/middleware/role.middleware.ts
import { Request, Response, NextFunction, RequestHandler } from 'express';
import { AuthenticatedRequest } from './auth.middleware';

export const requireRole = (roles: string[]): RequestHandler => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const { user } = req as AuthenticatedRequest;

    if (!user || !roles.includes(user.role)) {
      res.status(403).json({ message: 'Forbidden: Insufficient role' });
      return;
    }

    next();
  };
};
