// CTRON Home - Input Sanitization Middleware
// Sanitizes user input to prevent XSS and injection attacks

import { Request, Response, NextFunction } from 'express';
import DOMPurify from 'isomorphic-dompurify';
import { logger } from '../utils/logger';

// Extend Request interface to include file properties
interface RequestWithFiles extends Request {
  file?: {
    originalname?: string;
    [key: string]: any;
  };
  files?: Array<{
    originalname?: string;
    [key: string]: any;
  }>;
}

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    // Remove HTML tags and dangerous characters
    const sanitized = DOMPurify.sanitize(obj, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
    });
    
    // Additional sanitization for common injection patterns
    return sanitized
      .replace(/[<>]/g, '') // Remove any remaining angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // Sanitize both key and value
        const sanitizedKey = typeof key === 'string' ? 
          key.replace(/[<>]/g, '').trim() : key;
        sanitized[sanitizedKey] = sanitizeObject(obj[key]);
      }
    }
    return sanitized;
  }

  return obj;
}

/**
 * Middleware to sanitize request body, query, and params
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }

    // Sanitize route parameters
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }

    next();
  } catch (error) {
    logger.error('Input sanitization error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid input data'
    });
  }
};

/**
 * Middleware specifically for chat messages
 * Allows basic formatting but removes dangerous content
 */
export const sanitizeChatMessage = (req: Request, res: Response, next: NextFunction) => {
  try {
    if (req.body && req.body.content && typeof req.body.content === 'string') {
      // Allow basic text formatting but remove scripts and dangerous content
      req.body.content = DOMPurify.sanitize(req.body.content, {
        ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'br'],
        ALLOWED_ATTR: [],
        KEEP_CONTENT: true,
      });

      // Limit message length
      if (req.body.content.length > 1000) {
        req.body.content = req.body.content.substring(0, 1000);
      }
    }

    next();
  } catch (error) {
    logger.error('Chat message sanitization error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid message content'
    });
  }
};

/**
 * Middleware for file upload sanitization
 */
export const sanitizeFileUpload = (req: RequestWithFiles, res: Response, next: NextFunction) => {
  try {
    // Sanitize file metadata
    if (req.file) {
      // Sanitize filename
      if (req.file.originalname) {
        req.file.originalname = req.file.originalname
          .replace(/[<>:"/\\|?*]/g, '') // Remove dangerous filename characters
          .replace(/\.\./g, '') // Remove directory traversal
          .trim();
      }
    }

    // Sanitize multiple files
    if (req.files && Array.isArray(req.files)) {
      req.files.forEach(file => {
        if (file.originalname) {
          file.originalname = file.originalname
            .replace(/[<>:"/\\|?*]/g, '')
            .replace(/\.\./g, '')
            .trim();
        }
      });
    }

    next();
  } catch (error) {
    logger.error('File upload sanitization error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid file upload'
    });
  }
};
