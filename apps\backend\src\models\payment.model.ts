// src/models/payment.model.ts

import { prisma } from '../config/db';

export const PaymentModel = {
  // Step 1: Create payment intent record
  createPayment: async (
    jobId: string,
    userId: string,
    amount: number,
    currency: string = 'GBP',
    stripePaymentIntentId: string
  ) => {
    return prisma.payment.create({
      data: {
        jobId,
        userId,
        amount,
        currency,
        stripePaymentIntentId,
        isReleased: false,
        isFrozen: false,
      },
    });
  },

  // Step 2: Get payment record by job
  getPaymentByJobId: async (jobId: string) => {
    return prisma.payment.findUnique({
      where: { jobId },
    });
  },

  // Step 3: Mark as paid when <PERSON>e confirms success (via webhook)
  markAsPaid: async (jobId: string) => {
    return prisma.payment.update({
      where: { jobId },
      data: {
        isReleased: true,
        releasedAt: new Date(),
      },
    });
  },

  // Step 4: Admin override to release payout manually (no Stripe capture)
  releasePayment: async (jobId: string) => {
    return prisma.payment.update({
      where: { jobId },
      data: {
        releasedAt: new Date(),
      },
    });
  },

  // Optional: Update by Stripe intent ID
  updatePaymentStatus: async (
    stripePaymentIntentId: string,
    updates: Partial<{ isReleased: boolean; releasedAt: Date }>
  ) => {
    return prisma.payment.update({
      where: { stripePaymentIntentId },
      data: updates,
    });
  },

  // ✅ Step 5: Capture & mark as released (final payout step)
  markAsReleased: async (jobId: string) => {
    const payment = await prisma.payment.findUnique({ where: { jobId } });

    if (payment?.isFrozen) {
      throw new Error('Payment is frozen and cannot be released.');
    }

    return prisma.payment.update({
      where: { jobId },
      data: {
        isReleased: true,
        releasedAt: new Date(),
      },
    });
  },

  // ✅ Step 6: Admin freeze logic (flag dispute or issue)
  freezePayment: async (jobId: string, reason?: string) => {
    return prisma.payment.update({
      where: { jobId },
      data: {
        isFrozen: true,
        freezeReason: reason || null,
      },
    });
  },

  // ✅ Step 7: Admin unfreeze logic (flag dispute or issue)
  unfreezePayment: async (jobId: string) => {
    return prisma.payment.update({
      where: { jobId },
      data: {
        isFrozen: false,
        freezeReason: null,
      },
    });
  },
};
