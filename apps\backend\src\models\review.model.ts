// src/models/review.model.ts

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const ReviewModel = {
  createReview: async (data: {
    userId: string;
    jobId: string;
    rating: number;
    comment?: string;
  }) => {
    return prisma.review.create({ data });
  },

  getReviewsByUser: async (userId: string) => {
    return prisma.review.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  getReviewsByJob: async (jobId: string) => {
    return prisma.review.findMany({
      where: { jobId },
      orderBy: { createdAt: 'desc' },
    });
  },

  getReviewsByTechnician: async (technicianId: string) => {
    return prisma.review.findMany({
      where: {
        job: {
          technicianId,
        },
      },
      orderBy: { createdAt: 'desc' },
      include: {
        job: true,
        user: true,
      },
    });
  },

  deleteReview: async (reviewId: string, userId: string) => {
    const review = await prisma.review.findUnique({
      where: { id: reviewId },
    });

    if (!review || review.userId !== userId) {
      throw new Error('Review not found or permission denied');
    }

    return prisma.review.delete({
      where: { id: reviewId },
    });
  },
};
