//src/models/technician.model.ts
import { PrismaClient } from '@prisma/client'; // ✅ FIXED

const prisma = new PrismaClient();

export const TechnicianModel = {
  createTechnician: async (data: {
    userId: string;
    specialization: string;
  }) => {
    return prisma.technician.create({
      data: {
        ...data,
        isAvailable: true,
        rating: 0,
      },
    });
  },

  getByUserId: async (userId: string) => {
    return prisma.technician.findUnique({
      where: { userId },
      include: {
        user: true,
        jobs: true,
      },
    });
  },

  updateAvailability: async (userId: string, isAvailable: boolean) => {
    return prisma.technician.update({
      where: { userId },
      data: { isAvailable },
    });
  },

  getAllAvailable: async () => {
    return prisma.technician.findMany({
      where: { isAvailable: true },
      include: { user: true },
    });
  },
};
