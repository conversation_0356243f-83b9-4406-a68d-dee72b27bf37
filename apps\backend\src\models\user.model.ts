// src/models/user.model.ts
import { PrismaClient, Role } from '@prisma/client';

const prisma = new PrismaClient();

export const UserModel = {
  createUser: async (data: {
    email: string;
    password: string;
    fullName: string;
    phone: string;
    role: Role;
  }) => {
    return prisma.user.create({ data });
  },

  findByEmail: async (email: string) => {
    return prisma.user.findUnique({ where: { email } });
  },

  findById: async (id: string) => {
    return prisma.user.findUnique({ where: { id } });
  },

  getAll: async () => {
    return prisma.user.findMany();
  },

  updateUser: async (id: string, data: Partial<{ fullName: string; phone: string }>) => {
    return prisma.user.update({
      where: { id },
      data,
    });
  },
};
