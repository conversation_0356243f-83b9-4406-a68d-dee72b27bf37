// CTRON Home - Chat Routes
// API endpoints for chat functionality

import { Router } from 'express';
import { z } from 'zod';
import { <PERSON>t<PERSON>ontroller } from '../controllers/chat.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { rateLimitMiddleware, rateLimits } from '../middleware/rateLimiting.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { asyncHandler } from '../utils/asyncHandler';

// Validation schemas
const sendMessageSchema = z.object({
  body: z.object({
    content: z.string().min(1, 'Message content is required').max(1000, 'Message too long'),
    type: z.enum(['TEXT', 'IMAGE', 'FILE']).default('TEXT')
  })
});

const updateChatStatusSchema = z.object({
  body: z.object({
    status: z.enum(['ACTIVE', 'CLOSED', 'ARCHIVED'])
  })
});

const paginationSchema = z.object({
  query: z.object({
    page: z.string().regex(/^\d+$/).transform(Number).default('1'),
    limit: z.string().regex(/^\d+$/).transform(Number).default('20')
  })
});

const uuidParamSchema = z.object({
  params: z.object({
    jobId: z.string().uuid().optional(),
    chatId: z.string().uuid().optional(),
    messageId: z.string().uuid().optional(),
    userId: z.string().uuid().optional()
  })
});

const router = Router();

// Apply authentication to all chat routes
router.use(authMiddleware);

/**
 * GET /api/chats/job/:jobId
 * Get or create chat for a specific job
 * Rate limited to prevent abuse
 */
router.get(
  '/job/:jobId',
  validateRequest(uuidParamSchema),
  rateLimitMiddleware(rateLimits.api),
  asyncHandler(ChatController.getOrCreateJobChat)
);

/**
 * POST /api/chats/:chatId/messages
 * Send a message to a chat
 * Higher rate limit for messaging
 */
router.post(
  '/:chatId/messages',
  validateRequest(uuidParamSchema),
  validateRequest(sendMessageSchema),
  rateLimitMiddleware(rateLimits.chat),
  asyncHandler(ChatController.sendMessage)
);

/**
 * GET /api/chats/:chatId/messages
 * Get paginated message history for a chat
 * Standard API rate limit
 */
router.get(
  '/:chatId/messages',
  validateRequest(uuidParamSchema),
  validateRequest(paginationSchema),
  rateLimitMiddleware(rateLimits.api),
  asyncHandler(ChatController.getChatMessages)
);

/**
 * PUT /api/chat/messages/:messageId/read
 * Mark a specific message as read
 * Standard API rate limit
 */
router.put(
  '/messages/:messageId/read',
  validateRequest(uuidParamSchema),
  rateLimitMiddleware(rateLimits.api),
  asyncHandler(ChatController.markMessageRead)
);

/**
 * PUT /api/chats/:chatId/messages/read
 * Mark all messages in a chat as read for the current user
 * Standard API rate limit
 */
router.put(
  '/:chatId/messages/read',
  validateRequest(uuidParamSchema),
  rateLimitMiddleware(rateLimits.api),
  asyncHandler(ChatController.markAllMessagesRead)
);

/**
 * GET /api/chats/user/:userId
 * Get all active chats for a user
 * Standard API rate limit
 */
router.get(
  '/user/:userId',
  validateRequest(uuidParamSchema),
  rateLimitMiddleware(rateLimits.api),
  asyncHandler(ChatController.getUserChats)
);

/**
 * PUT /api/chats/:chatId/status
 * Update chat status (ACTIVE, CLOSED, ARCHIVED)
 * Standard API rate limit
 */
router.put(
  '/:chatId/status',
  validateRequest(uuidParamSchema),
  validateRequest(updateChatStatusSchema),
  rateLimitMiddleware(rateLimits.api),
  asyncHandler(ChatController.updateChatStatus)
);

export default router;
