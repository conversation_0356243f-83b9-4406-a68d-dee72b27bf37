// ✅ File: backend/src/routes/dashboard.routes.ts

import { Router } from 'express';
import { authMiddleware } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/role.middleware';
import { asyncHandler } from '../utils/asyncHandler';
import { DashboardController } from '../controllers/dashboard.controller';

const router = Router();

router.get(
  '/metrics',
  authMiddleware,
  requireRole(['ADMIN']),
  asyncHandler(DashboardController.getMetrics)
);

export default router;
