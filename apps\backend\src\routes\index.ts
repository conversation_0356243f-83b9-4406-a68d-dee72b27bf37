import { Router } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes';
import technicianRoutes from './technician.routes';
import uploadRoutes from './upload.routes';
import jobRoutes from './job.routes';
import paymentRoutes from './payment.routes';
import reviewRoutes from './review.routes';
import chatRoutes from './chat.routes';

const router = Router();

router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/technicians', technicianRoutes);
router.use('/uploads', uploadRoutes);
router.use('/jobs', jobRoutes);
router.use('/payments', paymentRoutes);
router.use('/reviews', reviewRoutes);
router.use('/chats', chatRoutes);

export default router;