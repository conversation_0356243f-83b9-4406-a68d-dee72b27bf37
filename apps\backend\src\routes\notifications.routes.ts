// CTRON Home - Notification Routes
// Handles notification-related API endpoints

import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { NotificationService } from '../services/notification.service';
import { logger } from '../utils/logger';

const router = Router();

// Validation schemas
const registerTokenSchema = z.object({
  body: z.object({
    pushToken: z.string().min(1, 'Push token is required'),
    platform: z.enum(['ios', 'android']),
    deviceInfo: z.object({
      brand: z.string().optional(),
      modelName: z.string().optional(),
      osName: z.string().optional(),
      osVersion: z.string().optional(),
    }).optional()
  })
});

const sendNotificationSchema = z.object({
  body: z.object({
    userIds: z.array(z.string()),
    title: z.string().min(1, 'Title is required'),
    body: z.string().min(1, 'Body is required'),
    data: z.record(z.any()).optional()
  })
});

/**
 * POST /api/notifications/register-token
 * Register a device token for push notifications
 */
router.post('/register-token',
  authenticateToken,
  validateRequest(registerTokenSchema),
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { pushToken, platform, deviceInfo } = req.body;
      const userId = req.user!.id;

      // Register the push token using the notification service
      await NotificationService.registerPushToken(userId, pushToken, {
        platform,
        ...deviceInfo,
      });

      logger.info(`Push token registered for user ${userId}`);

      res.json({
        success: true,
        message: 'Push token registered successfully'
      });
    } catch (error) {
      logger.error('Failed to register device token:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to register device token'
      });
    }
  }
);

/**
 * POST /api/notifications/send
 * Send push notification to specific users (Admin only)
 */
router.post('/send',
  authenticateToken,
  validateRequest(sendNotificationSchema),
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      // Check if user is admin
      if (req.user!.role !== 'ADMIN') {
        res.status(403).json({
          success: false,
          message: 'Only admins can send notifications'
        });
        return;
      }

      const { userIds, title, body, data } = req.body;

      await NotificationService.sendPushNotification(userIds, {
        title,
        body,
        data
      });

      res.json({
        success: true,
        message: `Notification sent to ${userIds.length} users`
      });
    } catch (error) {
      logger.error('Failed to send notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send notification'
      });
    }
  }
);

/**
 * POST /api/notifications/test
 * Test notification endpoint (Development only)
 */
router.post('/test',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (process.env.NODE_ENV === 'production') {
        res.status(404).json({
          success: false,
          message: 'Test endpoint not available in production'
        });
        return;
      }

      const userId = req.user!.id;

      await NotificationService.sendPushNotification([userId], {
        title: 'Test Notification',
        body: 'This is a test notification from CTRON Home',
        data: {
          type: 'test',
          timestamp: new Date().toISOString()
        }
      });

      res.json({
        success: true,
        message: 'Test notification sent'
      });
    } catch (error) {
      logger.error('Failed to send test notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send test notification'
      });
    }
  }
);

export default router;
