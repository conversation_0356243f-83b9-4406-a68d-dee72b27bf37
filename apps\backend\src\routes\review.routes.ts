// 📁 File: src/routes/review.routes.ts

import express from 'express';
import { ReviewController } from '../controllers/review.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { asyncHandler } from '../utils/asyncHandler';

const router = express.Router();

// ✅ Create a new review (requires login)
router.post(
  '/',
  authMiddleware,
  asyncHandler(ReviewController.createReview)
);

// ✅ Get all reviews by the currently authenticated user
router.get(
  '/me',
  authMiddleware,
  asyncHandler(ReviewController.getUserReviews)
);

// ✅ Get all reviews for a specific job
router.get(
  '/job/:jobId',
  asyncHandler(ReviewController.getJobReviews)
);

// ✅ Get all reviews for a technician (public or admin)
router.get(
  '/technician/:technicianId',
  asyncHandler(ReviewController.getReviewsForTechnician)
);

// ✅ Delete a review by ID
router.delete(
  '/:id',
  authMiddleware,
  asyncHandler(ReviewController.deleteReview)
);

export default router;
