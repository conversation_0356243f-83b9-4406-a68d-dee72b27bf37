// src/routes/settings.routes.ts

import { Router } from 'express';
import { SettingsController } from '../controllers/settings.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/role.middleware';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();

router.get(
  '/',
  authMiddleware,
  requireRole(['ADMIN']),
  SettingsController.getSettings
);

router.put(
  '/',
  authMiddleware,
  requireRole(['ADMIN']),
  asyncHandler(SettingsController.updateSettings)
);

export default router;
