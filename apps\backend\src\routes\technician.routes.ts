// 📁 File: backend/src/routes/technician.routes.ts

import { Router } from 'express';
import { authMiddleware } from '../middleware/auth.middleware';
import { requireRole } from '../middleware/role.middleware';
import { asyncHandler } from '../utils/asyncHandler';
import { TechnicianController } from '../controllers/technician.controller';

const router = Router();

/**
 * 👤 POST /technicians/onboard
 * TECHNICIAN creates their profile
 */
router.post(
  '/onboard',
  authMiddleware,
  requireRole(['TECHNICIAN']),
  asyncHandler(TechnicianController.onboard)
);

/**
 * 📊 GET /technicians/dashboard
 * TECHNICIAN fetches their profile overview
 */
router.get(
  '/dashboard',
  authMiddleware,
  requireRole(['TECHNICIAN']),
  async<PERSON>and<PERSON>(TechnicianController.getDashboard)
);

/**
 * 🔄 PUT /technicians/availability
 * TECHNICIAN updates availability
 */
router.put(
  '/availability',
  authMiddleware,
  requireRole(['TECHNICIAN']),
  async<PERSON>and<PERSON>(TechnicianController.setAvailability)
);

/**
 * 📍 GET /technicians/nearby?lat=...&lng=...&minRating=...
 * PUBLIC (or protected) route for discovering nearby technicians
 */
router.get(
  '/nearby',
  authMiddleware, // 🔓 remove if public lookup is desired
  asyncHandler(TechnicianController.getNearbyTechnicians)
);

router.post('/kyc/start', authMiddleware, requireRole(['TECHNICIAN']), TechnicianController.createVerificationSession);

export default router;
