// backend/src/schedulers/autoReleaseScheduler.ts
import cron from 'node-cron';
import <PERSON><PERSON> from 'stripe';
import { prisma } from '../config/db';
import { env } from '../config/env';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: env.STRIPE_API_VERSION as any,
});

const runAutoRelease = async () => {
  const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24h ago

  const jobs = await prisma.job.findMany({
    where: {
      status: 'COMPLETED',
      completedAt: { lt: cutoff },
      payment: {
        isReleased: false,
      },
    },
    include: {
      payment: true,
    },
  });

  if (jobs.length === 0) return;

  for (const job of jobs) {
    const intentId = job.payment?.stripePaymentIntentId;

    try {
      if (!intentId) {
        console.error(`❌ Auto-release failed for job ${job.id}: Missing PaymentIntent ID`);
        continue;
      }

      // Check if payment is already released
      if (job.payment?.isReleased) {
        console.log(`ℹ️ Payment already released for job ${job.id}, skipping`);
        continue;
      }

      // Check if payment is frozen
      if (job.payment?.isFrozen) {
        console.log(`⚠️ Payment frozen for job ${job.id}, skipping auto-release`);
        continue;
      }

      try {
        // Capture payment with proper error handling
        await stripe.paymentIntents.capture(intentId);
        console.log(`✅ Stripe capture successful for auto-release job ${job.id}`);
      } catch (stripeError: any) {
        console.error(`❌ Stripe capture failed for auto-release job ${job.id}:`, {
          paymentIntentId: intentId,
          error: stripeError.message,
          code: stripeError.code,
          type: stripeError.type
        });

        // Skip database updates if Stripe capture failed
        continue;
      }

      try {
        // Update payment status in database
        await prisma.payment.update({
          where: { jobId: job.id },
          data: {
            isReleased: true,
            releasedAt: new Date(),
          },
        });
        console.log(`✅ Payment marked as released for auto-release job ${job.id}`);
      } catch (dbError: any) {
        console.error(`❌ Database payment update failed for auto-release job ${job.id}:`, {
          error: dbError.message
        });
        // Continue to try job update even if payment update failed
      }

      try {
        // Update job confirmation timestamp
        await prisma.job.update({
          where: { id: job.id },
          data: {
            confirmedAt: new Date(),
          },
        });
        console.log(`✅ Job confirmation updated for auto-release job ${job.id}`);
      } catch (jobUpdateError: any) {
        console.error(`❌ Job confirmation update failed for auto-release job ${job.id}:`, {
          error: jobUpdateError.message
        });
        // This is less critical - payment was processed successfully
      }

      console.log(`✅ Auto-released payment for job ${job.id}`);
    } catch (err: any) {
      console.error(`❌ Unexpected error in auto-release for job ${job.id}:`, {
        error: err.message,
        stack: err.stack
      });
    }
  }
};

// 🕓 Schedule: runs every day at 1:00 AM
cron.schedule('0 1 * * *', () => {
  console.log('🕓 Running daily auto-release task...');
  runAutoRelease();
});
