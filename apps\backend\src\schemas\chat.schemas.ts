﻿// CTRON Home - Chat Validation Schemas
// Zod schemas for chat API validation

import { z } from 'zod';

// Temporary enum definition until Prisma client is properly generated
enum ChatStatus {
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  ARCHIVED = 'ARCHIVED'
}

/**
 * Schema for creating a chat for a job
 */
export const createChatSchema = z.object({
  jobId: z.string().uuid('Invalid job ID format'),
});

/**
 * Schema for sending a message
 */
export const sendMessageSchema = z.object({
  chatId: z.string().uuid('Invalid chat ID format'),
  content: z.string()
    .min(1, 'Message content cannot be empty')
    .max(2000, 'Message content cannot exceed 2000 characters')
    .trim(),
  attachments: z.array(z.object({
    filename: z.string().min(1, 'Filename is required'),
    mimeType: z.string().min(1, 'MIME type is required'),
    size: z.number().positive('File size must be positive'),
    url: z.string().url('Invalid file URL')
  })).optional().default([])
});

/**
 * Schema for getting chat messages with pagination
 */
export const getChatMessagesSchema = z.object({
  chatId: z.string().uuid('Invalid chat ID format'),
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(50),
  before: z.string().datetime().optional(),
  after: z.string().datetime().optional()
});

/**
 * Schema for marking a message as read
 */
export const markMessageReadSchema = z.object({
  messageId: z.string().uuid('Invalid message ID format')
});

/**
 * Schema for marking all messages in a chat as read
 */
export const markMessagesReadSchema = z.object({
  chatId: z.string().uuid('Invalid chat ID format')
});

/**
 * Schema for updating chat status
 */
export const updateChatStatusSchema = z.object({
  chatId: z.string().uuid('Invalid chat ID format'),
  status: z.nativeEnum(ChatStatus, {
    errorMap: () => ({ message: 'Invalid chat status. Must be ACTIVE, CLOSED, or ARCHIVED' })
  })
});

/**
 * Schema for typing indicator
 */
export const typingIndicatorSchema = z.object({
  chatId: z.string().uuid('Invalid chat ID format'),
  isTyping: z.boolean()
});

// Export types for TypeScript
export type CreateChatInput = z.infer<typeof createChatSchema>;
export type SendMessageInput = z.infer<typeof sendMessageSchema>;
export type GetChatMessagesInput = z.infer<typeof getChatMessagesSchema>;
export type MarkMessageReadInput = z.infer<typeof markMessageReadSchema>;
export type MarkMessagesReadInput = z.infer<typeof markMessagesReadSchema>;
export type UpdateChatStatusInput = z.infer<typeof updateChatStatusSchema>;
export type TypingIndicatorInput = z.infer<typeof typingIndicatorSchema>;
