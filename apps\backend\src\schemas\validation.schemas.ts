// CTRON Home - Comprehensive Input Validation Schemas
// Zod schemas for all API endpoints to ensure data integrity and security

import { z } from 'zod';

// ===== USER SCHEMAS =====

export const UserRegistrationSchema = z.object({
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must not exceed 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes'),
  
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must not exceed 255 characters')
    .toLowerCase(),
  
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  phone: z.string()
    .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    .optional(),
  
  role: z.enum(['HOMEOWNER', 'TECHNICIAN'], {
    errorMap: () => ({ message: 'Role must be either HOMEOWNER or TECHNICIAN' })
  }),
});

export const UserLoginSchema = z.object({
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must not exceed 255 characters')
    .toLowerCase(),
  
  password: z.string()
    .min(1, 'Password is required')
    .max(128, 'Password must not exceed 128 characters'),
});

export const UserUpdateSchema = z.object({
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must not exceed 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes')
    .optional(),
  
  phone: z.string()
    .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
    .optional(),
  
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must not exceed 255 characters')
    .toLowerCase()
    .optional(),
});

// ===== JOB SCHEMAS =====

export const JobCreationSchema = z.object({
  issue: z.string()
    .min(5, 'Issue description must be at least 5 characters')
    .max(200, 'Issue description must not exceed 200 characters')
    .trim(),
  
  description: z.string()
    .max(1000, 'Description must not exceed 1000 characters')
    .trim()
    .optional(),
  
  priority: z.enum(['low', 'medium', 'high'], {
    errorMap: () => ({ message: 'Priority must be low, medium, or high' })
  }).default('medium'),
  
  scheduledAt: z.string()
    .datetime('Invalid date format')
    .refine((date) => new Date(date) > new Date(), {
      message: 'Scheduled date must be in the future'
    }),
  
  latitude: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .optional(),
  
  longitude: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .optional(),
  
  photoUrl: z.string()
    .url('Invalid photo URL format')
    .optional(),
});

export const JobUpdateSchema = z.object({
  issue: z.string()
    .min(5, 'Issue description must be at least 5 characters')
    .max(200, 'Issue description must not exceed 200 characters')
    .trim()
    .optional(),
  
  description: z.string()
    .max(1000, 'Description must not exceed 1000 characters')
    .trim()
    .optional(),
  
  priority: z.enum(['low', 'medium', 'high'], {
    errorMap: () => ({ message: 'Priority must be low, medium, or high' })
  }).optional(),
  
  scheduledAt: z.string()
    .datetime('Invalid date format')
    .refine((date) => new Date(date) > new Date(), {
      message: 'Scheduled date must be in the future'
    })
    .optional(),
  
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'], {
    errorMap: () => ({ message: 'Invalid job status' })
  }).optional(),
  
  proofImageKey: z.string()
    .min(1, 'Proof image key cannot be empty')
    .optional(),
});

export const JobStatusUpdateSchema = z.object({
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'], {
    errorMap: () => ({ message: 'Invalid job status' })
  }),
  
  cancellationReason: z.string()
    .min(5, 'Cancellation reason must be at least 5 characters')
    .max(500, 'Cancellation reason must not exceed 500 characters')
    .optional(),
});

// ===== CHAT/MESSAGE SCHEMAS =====

export const MessageCreationSchema = z.object({
  content: z.string()
    .min(1, 'Message content cannot be empty')
    .max(2000, 'Message content must not exceed 2000 characters')
    .trim(),
  
  attachments: z.array(z.object({
    url: z.string().url('Invalid attachment URL'),
    type: z.enum(['image', 'document'], {
      errorMap: () => ({ message: 'Attachment type must be image or document' })
    }),
    filename: z.string()
      .min(1, 'Filename cannot be empty')
      .max(255, 'Filename must not exceed 255 characters'),
  })).max(5, 'Maximum 5 attachments allowed').optional(),
});

export const ChatCreationSchema = z.object({
  jobId: z.string()
    .uuid('Invalid job ID format'),
  
  participantIds: z.array(z.string().uuid('Invalid participant ID format'))
    .min(2, 'Chat must have at least 2 participants')
    .max(10, 'Chat cannot have more than 10 participants'),
});

// ===== PAYMENT SCHEMAS =====

export const PaymentCreationSchema = z.object({
  amount: z.number()
    .positive('Payment amount must be positive')
    .max(10000, 'Payment amount cannot exceed £10,000')
    .multipleOf(0.01, 'Payment amount must be in valid currency format'),
  
  currency: z.enum(['GBP', 'USD', 'EUR'], {
    errorMap: () => ({ message: 'Currency must be GBP, USD, or EUR' })
  }).default('GBP'),
  
  jobId: z.string()
    .uuid('Invalid job ID format'),
  
  description: z.string()
    .max(500, 'Payment description must not exceed 500 characters')
    .optional(),
});

export const PaymentUpdateSchema = z.object({
  status: z.enum(['pending', 'processing', 'succeeded', 'failed', 'cancelled'], {
    errorMap: () => ({ message: 'Invalid payment status' })
  }).optional(),
  
  isReleased: z.boolean().optional(),
  isFrozen: z.boolean().optional(),
  
  adminNotes: z.string()
    .max(1000, 'Admin notes must not exceed 1000 characters')
    .optional(),
});

// ===== TECHNICIAN SCHEMAS =====

export const TechnicianRegistrationSchema = z.object({
  specializations: z.array(z.string()
    .min(2, 'Specialization must be at least 2 characters')
    .max(50, 'Specialization must not exceed 50 characters'))
    .min(1, 'At least one specialization is required')
    .max(10, 'Maximum 10 specializations allowed'),
  
  experience: z.number()
    .int('Experience must be a whole number')
    .min(0, 'Experience cannot be negative')
    .max(50, 'Experience cannot exceed 50 years'),
  
  certifications: z.array(z.string()
    .min(2, 'Certification must be at least 2 characters')
    .max(100, 'Certification must not exceed 100 characters'))
    .max(20, 'Maximum 20 certifications allowed')
    .optional(),
  
  hourlyRate: z.number()
    .positive('Hourly rate must be positive')
    .max(500, 'Hourly rate cannot exceed £500')
    .multipleOf(0.01, 'Hourly rate must be in valid currency format')
    .optional(),
});

export const TechnicianUpdateSchema = z.object({
  specializations: z.array(z.string()
    .min(2, 'Specialization must be at least 2 characters')
    .max(50, 'Specialization must not exceed 50 characters'))
    .min(1, 'At least one specialization is required')
    .max(10, 'Maximum 10 specializations allowed')
    .optional(),
  
  isAvailable: z.boolean().optional(),
  
  hourlyRate: z.number()
    .positive('Hourly rate must be positive')
    .max(500, 'Hourly rate cannot exceed £500')
    .multipleOf(0.01, 'Hourly rate must be in valid currency format')
    .optional(),
  
  kycStatus: z.enum(['PENDING', 'APPROVED', 'REJECTED'], {
    errorMap: () => ({ message: 'Invalid KYC status' })
  }).optional(),
});

// ===== ADMIN SCHEMAS =====

export const AdminSettingsSchema = z.object({
  gracePeriodHours: z.number()
    .int('Grace period must be a whole number')
    .min(1, 'Grace period must be at least 1 hour')
    .max(168, 'Grace period cannot exceed 168 hours (1 week)'),
  
  stripeTestMode: z.boolean(),
  
  statusMessage: z.string()
    .max(500, 'Status message must not exceed 500 characters')
    .trim(),
  
  maintenanceMode: z.boolean(),
  
  maxJobsPerTechnician: z.number()
    .int('Max jobs must be a whole number')
    .min(1, 'Max jobs must be at least 1')
    .max(100, 'Max jobs cannot exceed 100'),
  
  commissionRate: z.number()
    .min(0, 'Commission rate cannot be negative')
    .max(1, 'Commission rate cannot exceed 100%')
    .multipleOf(0.001, 'Commission rate must have at most 3 decimal places'),
});

// ===== QUERY PARAMETER SCHEMAS =====

export const PaginationSchema = z.object({
  page: z.string()
    .regex(/^\d+$/, 'Page must be a positive integer')
    .transform(Number)
    .refine(n => n > 0, 'Page must be greater than 0')
    .default('1'),
  
  limit: z.string()
    .regex(/^\d+$/, 'Limit must be a positive integer')
    .transform(Number)
    .refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100')
    .default('10'),
});

export const JobFilterSchema = z.object({
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'])
    .optional(),
  
  priority: z.enum(['low', 'medium', 'high'])
    .optional(),
  
  technicianId: z.string()
    .uuid('Invalid technician ID format')
    .optional(),
  
  userId: z.string()
    .uuid('Invalid user ID format')
    .optional(),
  
  dateFrom: z.string()
    .datetime('Invalid date format')
    .optional(),
  
  dateTo: z.string()
    .datetime('Invalid date format')
    .optional(),
}).merge(PaginationSchema);

// ===== EXPORT TYPES =====

export type UserRegistration = z.infer<typeof UserRegistrationSchema>;
export type UserLogin = z.infer<typeof UserLoginSchema>;
export type UserUpdate = z.infer<typeof UserUpdateSchema>;
export type JobCreation = z.infer<typeof JobCreationSchema>;
export type JobUpdate = z.infer<typeof JobUpdateSchema>;
export type JobStatusUpdate = z.infer<typeof JobStatusUpdateSchema>;
export type MessageCreation = z.infer<typeof MessageCreationSchema>;
export type ChatCreation = z.infer<typeof ChatCreationSchema>;
export type PaymentCreation = z.infer<typeof PaymentCreationSchema>;
export type PaymentUpdate = z.infer<typeof PaymentUpdateSchema>;
export type TechnicianRegistration = z.infer<typeof TechnicianRegistrationSchema>;
export type TechnicianUpdate = z.infer<typeof TechnicianUpdateSchema>;
export type AdminSettings = z.infer<typeof AdminSettingsSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type JobFilter = z.infer<typeof JobFilterSchema>;
