// CTRON Home - Chat Service
// Business logic for chat operations

import { prisma } from '../config/db';
import { sendMessageSchema } from '../validation/schemas';

// Temporary enum definitions until Prisma client is properly generated
enum ChatStatus {
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  ARCHIVED = 'ARCHIVED'
}

enum Role {
  HOMEOWNER = 'HOMEOWNER',
  TECHNICIAN = 'TECHNICIAN'
}

interface PaginationOptions {
  page: number;
  limit: number;
  before?: string;
  after?: string;
}

interface ChatWithDetails {
  id: string;
  jobId: string;
  status: ChatStatus;
  createdAt: Date;
  updatedAt: Date;
  job: {
    id: string;
    issue: string;
    status: string;
    user: {
      id: string;
      fullName: string;
    };
    technician?: {
      id: string;
      user: {
        id: string;
        fullName: string;
      };
    } | null;
  };
  participants: Array<{
    id: string;
    userId: string;
    role: Role;
    user: {
      id: string;
      fullName: string;
    };
  }>;
  _count: {
    messages: number;
  };
  lastMessage?: {
    id: string;
    content: string;
    createdAt: Date;
    sender: {
      id: string;
      fullName: string;
    };
  } | null;
}

export const ChatService = {
  /**
   * Get or create a chat for a specific job
   */
  async getOrCreateJobChat(jobId: string, userId: string): Promise<ChatWithDetails> {
    // TEMPORARY: Return mock data until Prisma client is fixed
    return {
      id: 'temp-chat-id',
      jobId,
      status: ChatStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      job: {
        id: jobId,
        issue: 'Temporary job',
        status: 'PENDING',
        user: { id: userId, fullName: 'Temp User' },
        technician: null
      },
      participants: [],
      _count: { messages: 0 },
      lastMessage: null
    };

    // Original code commented out until Prisma client is fixed
    /*
    let chat = await (prisma as any).chat.findUnique({
      where: { jobId },
      include: {
        job: {
          include: {
            user: {
              select: { id: true, fullName: true }
            },
            technician: {
              include: {
                user: {
                  select: { id: true, fullName: true }
                }
              }
            }
          }
        },
        participants: {
          include: {
            user: {
              select: { id: true, fullName: true }
            }
          }
        },
        _count: {
          select: { messages: true }
        }
      }
    });

    if (!chat) {
      // Create new chat
      chat = await prisma.chat.create({
        data: {
          jobId,
          status: 'ACTIVE'
        },
        include: {
          job: {
            include: {
              user: {
                select: { id: true, fullName: true }
              },
              technician: {
                include: {
                  user: {
                    select: { id: true, fullName: true }
                  }
                }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, fullName: true }
              }
            }
          },
          _count: {
            select: { messages: true }
          }
        }
      });

      // Add homeowner as participant
      await prisma.chatParticipant.create({
        data: {
          chatId: chat.id,
          userId: chat.job.user.id,
          role: 'HOMEOWNER'
        }
      });

      // Add technician as participant if assigned
      if (chat.job.technician) {
        await prisma.chatParticipant.create({
          data: {
            chatId: chat.id,
            userId: chat.job.technician.user.id,
            role: 'TECHNICIAN'
          }
        });
      }

      // Refresh chat with participants
      chat = await prisma.chat.findUnique({
        where: { id: chat.id },
        include: {
          job: {
            include: {
              user: {
                select: { id: true, fullName: true }
              },
              technician: {
                include: {
                  user: {
                    select: { id: true, fullName: true }
                  }
                }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, fullName: true }
              }
            }
          },
          _count: {
            select: { messages: true }
          }
        }
      })!;
    }

    // Ensure chat exists
    if (!chat) {
      throw new Error('Chat not found');
    }

    // Get last message
    const lastMessage = await prisma.message.findFirst({
      where: { chatId: chat.id },
      orderBy: { createdAt: 'desc' },
      include: {
        sender: {
          select: { id: true, fullName: true }
        }
      }
    });

    return {
      ...chat,
      lastMessage
    } as ChatWithDetails;
    */
  },

  /**
   * Send a message to a chat
   */
  async sendMessage(
    chatId: string,
    senderId: string,
    content: string,
    attachments?: Array<{ filename: string; mimeType: string; size: number; url: string }>
  ) {
    // Validate and sanitize input
    const validatedData = sendMessageSchema.parse({
      content,
      type: 'TEXT',
      metadata: attachments ? {
        fileName: attachments[0]?.filename,
        fileSize: attachments[0]?.size,
        mimeType: attachments[0]?.mimeType
      } : undefined
    });

    const message = await prisma.message.create({
      data: {
        chatId,
        senderId,
        content: validatedData.content, // Use sanitized content
        attachments: (attachments?.map(att => att.url) || []) as any,
        read: false
      },
      include: {
        sender: { select: { id: true, fullName: true, role: true } },
        chat: { select: { id: true, jobId: true } }
      }
    });

    return message;
  },

  /**
   * Get paginated messages for a chat
   */
  async getChatMessages(chatId: string, options: PaginationOptions) {
    const skip = (options.page - 1) * options.limit;

    const [messages, totalCount] = await Promise.all([
      prisma.message.findMany({
        where: { chatId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: options.limit,
        include: {
          sender: { select: { id: true, fullName: true, role: true } }
        }
      }),
      prisma.message.count({ where: { chatId } })
    ]);

    const totalPages = Math.ceil(totalCount / options.limit);

    return {
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        page: options.page,
        limit: options.limit,
        totalCount,
        totalPages,
        hasNextPage: options.page < totalPages,
        hasPreviousPage: options.page > 1
      }
    };
  },

  /**
   * Mark a specific message as read
   */
  async markMessageRead(messageId: string, userId: string): Promise<void> {
    // TEMPORARY: No-op until Prisma client is fixed
    return;
  },

  /**
   * Mark all messages in a chat as read for a user
   */
  async markAllMessagesRead(chatId: string, userId: string): Promise<number> {
    // TEMPORARY: Return 0 until Prisma client is fixed
    return 0;
  },

  /**
   * Get all chats for a user
   */
  async getUserChats(userId: string): Promise<ChatWithDetails[]> {
    // TEMPORARY: Return empty array until Prisma client is fixed
    return [];
  },

  /**
   * Update chat status
   */
  async updateChatStatus(chatId: string, status: ChatStatus) {
    // TEMPORARY: Return mock data until Prisma client is fixed
    return {
      id: chatId,
      status,
      updatedAt: new Date(),
      job: { id: 'temp-job-id', issue: 'Temporary job' },
      participants: []
    };
  },

  /**
   * Verify if user has access to a job (and thus its chat)
   */
  async verifyJobAccess(jobId: string, userId: string): Promise<boolean> {
    const job = await prisma.job.findFirst({
      where: {
        id: jobId,
        OR: [
          { userId }, // User is the job owner
          { technician: { userId } } // User is the assigned technician
        ]
      }
    });

    return !!job;
  },

  /**
   * Verify if user is a participant in a chat
   */
  async verifyParticipant(chatId: string, userId: string): Promise<boolean> {
    const participant = await prisma.chatParticipant.findFirst({
      where: { chatId, userId }
    });

    return !!participant;
  },

  /**
   * Verify if user can access a specific message
   */
  async verifyMessageAccess(messageId: string, userId: string): Promise<boolean> {
    const message = await prisma.message.findFirst({
      where: {
        id: messageId,
        chat: {
          participants: {
            some: {
              userId
            }
          }
        }
      }
    });

    return !!message;
  },

  /**
   * Get message with chat information (for socket operations)
   */
  async getMessageWithChat(messageId: string) {
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        chat: {
          select: { id: true, jobId: true }
        },
        sender: {
          select: { id: true, fullName: true, role: true }
        }
      }
    });

    if (!message) {
      throw new Error('Message not found');
    }

    return message;
  }
};
