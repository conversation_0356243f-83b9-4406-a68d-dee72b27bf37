import { prisma } from '../config/db';

export const JobService = {
  async createJob(data: {
    userId: string;
    technicianId: string;
    issue: string;
    scheduledAt: Date;
    photoUrl?: string;
  }) {
    return prisma.job.create({
      data: {
        userId: data.userId,
        technicianId: data.technicianId,
        issue: data.issue,
        scheduledAt: data.scheduledAt,
        photoUrl: data.photoUrl,
        status: 'PENDING', // ✅ string instead of JobStatus.PENDING
      },
    });
  },

  async getUserJobs(userId: string) {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async getTechnicianJobs(technicianId: string) {
    return prisma.job.findMany({
      where: { technicianId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async getJobById(id: string) {
    return prisma.job.findUnique({
      where: { id },
      include: {
        user: true,
        technician: {
          include: { user: true },
        },
      },
    });
  },

  async updateJobStatus(id: string, status: 'PENDING' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED') {
    return prisma.job.update({
      where: { id },
      data: { status },
    });
  },

  async deleteJob(id: string) {
    return prisma.job.delete({
      where: { id },
    });
  },
};
