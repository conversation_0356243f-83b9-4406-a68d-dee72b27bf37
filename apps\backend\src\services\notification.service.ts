// CTRON Home - Notification Service
// Handles push notifications, email notifications, and device token management

import nodemailer from 'nodemailer';
import axios from 'axios';
import { prisma } from '../config/db';
import { logger } from '../utils/logger';

interface PushNotificationData {
  title: string;
  body: string;
  data?: any;
  sound?: string;
  badge?: number;
}

export const NotificationService = {
  /**
   * Register a push token for a user
   */
  async registerPushToken(userId: string, pushToken: string, deviceInfo?: any): Promise<void> {
    try {
      // For now, just log the registration
      // In production, you'd store this in a device_tokens table
      logger.info(`Push token registered for user ${userId}:`, {
        pushToken: pushToken.substring(0, 20) + '...',
        deviceInfo
      });

      // TODO: Implement actual push token storage when device tokens table is created
    } catch (error) {
      logger.error('Failed to register push token:', error);
      throw error;
    }
  },

  /**
   * Send push notification to specific users
   */
  async sendPushNotification(
    userIds: string[],
    notification: PushNotificationData
  ): Promise<void> {
    try {
      // For now, we'll use a simple approach without device tokens table
      // In production, you'd want to store device tokens in the database
      logger.info(`Would send push notification to users: ${userIds.join(', ')}`);
      logger.info(`Notification: ${notification.title} - ${notification.body}`);

      // TODO: Implement actual push notification sending when device tokens are stored
    } catch (error) {
      logger.error('Failed to send push notifications:', error);
      throw error;
    }
  },

  /**
   * Send job status update notification
   */
  async sendJobStatusNotification(
    jobId: string,
    status: string,
    recipientUserIds: string[]
  ): Promise<void> {
    try {
      const job = await prisma.job.findUnique({
        where: { id: jobId },
        include: {
          user: { select: { fullName: true } },
          technician: {
            include: {
              user: { select: { fullName: true } }
            }
          }
        }
      });

      if (!job) {
        logger.error(`Job not found: ${jobId}`);
        return;
      }

      let title = '';
      let body = '';

      switch (status) {
        case 'ACCEPTED':
          title = 'Job Accepted';
          body = `Your job "${job.issue}" has been accepted by ${job.technician?.user.fullName}`;
          break;
        case 'IN_PROGRESS':
          title = 'Job Started';
          body = `Work has started on your job "${job.issue}"`;
          break;
        case 'COMPLETED':
          title = 'Job Completed';
          body = `Your job "${job.issue}" has been completed`;
          break;
        case 'CANCELLED':
          title = 'Job Cancelled';
          body = `Your job "${job.issue}" has been cancelled`;
          break;
        default:
          title = 'Job Update';
          body = `Your job "${job.issue}" status has been updated`;
      }

      await this.sendPushNotification(recipientUserIds, {
        title,
        body,
        data: {
          type: 'job_status',
          jobId,
          status
        }
      });
    } catch (error) {
      logger.error('Failed to send job status notification:', error);
    }
  },

  /**
   * Send new message notification
   */
  async sendNewMessageNotification(
    chatId: string,
    senderId: string,
    content: string,
    recipientUserIds: string[]
  ): Promise<void> {
    try {
      const sender = await prisma.user.findUnique({
        where: { id: senderId },
        select: { fullName: true }
      });

      if (!sender) {
        logger.error(`Sender not found: ${senderId}`);
        return;
      }

      await this.sendPushNotification(recipientUserIds, {
        title: `New message from ${sender.fullName}`,
        body: content.length > 50 ? `${content.substring(0, 50)}...` : content,
        data: {
          type: 'new_message',
          chatId,
          senderId
        }
      });
    } catch (error) {
      logger.error('Failed to send new message notification:', error);
    }
  },

  /**
   * Send email notification
   */
  async sendEmail(
    to: string,
    subject: string,
    text: string,
    html?: string
  ): Promise<void> {
    try {
      const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      });

      await transporter.sendMail({
        from: `"CTRON Home" <${process.env.EMAIL_USER}>`,
        to,
        subject,
        text,
        html,
      });

      logger.info(`Email sent to ${to}: ${subject}`);
    } catch (error) {
      logger.error('Failed to send email:', error);
      throw error;
    }
  }
};

/**
 * Legacy function for backward compatibility
 */
export async function sendPush(to: string, title: string, body: string) {
  await axios.post('https://exp.host/--/api/v2/push/send', {
    to,
    title,
    body,
  });
}
