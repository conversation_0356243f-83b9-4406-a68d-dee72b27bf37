import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Marks a job as paid and released.
 * Called by <PERSON>e webhook after payment_intent.succeeded.
 */
export async function completeJobPayment(jobId: string) {
  // 1. Mark payment released
  const updated = await prisma.payment.update({
    where: { jobId },
    data: {
      isReleased: true,
      releasedAt: new Date(),
    },
  });

  // 2. Optionally mark job completed (if not already done)
  await prisma.job.update({
    where: { id: jobId },
    data: {
      status: 'COMPLETED',
      completedAt: new Date(),
    },
  });

  return updated;
}
