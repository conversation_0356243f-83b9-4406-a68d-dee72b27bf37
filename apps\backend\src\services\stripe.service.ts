// src/services/stripe.service.ts

import { stripe } from '../config/stripe';

export const StripeService = {
  createPaymentIntent: async (amount: number, currency: string = 'GBP') => {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to smallest currency unit
      currency,
    });

    return paymentIntent;
  },

  retrievePaymentIntent: async (id: string) => {
    return stripe.paymentIntents.retrieve(id);
  },
};
