// src/services/technician.service.ts

import { prisma } from '../config/db';

export const TechnicianService = {
  /**
   * Create a technician profile (onboarding)
   */
  async createTechnician(userId: string, specialization: string) {
    return prisma.technician.create({
      data: {
        userId,
        specialization,
        isAvailable: true,
      },
    });
  },

  /**
   * Get technician profile by user ID
   */
  async getByUserId(userId: string) {
    return prisma.technician.findUnique({
      where: { userId },
      include: {
        user: true,
        jobs: true,
        reviews: true,
      },
    });
  },

  /**
   * Update technician availability (go online/offline)
   */
  async setAvailability(userId: string, isAvailable: boolean) {
    return prisma.technician.update({
      where: { userId },
      data: { isAvailable },
    });
  },

  /**
   * List all available technicians (for job assignment)
   */
  async listAvailable() {
    return prisma.technician.findMany({
      where: { isAvailable: true },
      include: { user: true },
      orderBy: { rating: 'desc' },
    });
  },

  /**
   * Update technician specialization
   */
  async updateSpecialization(userId: string, specialization: string) {
    return prisma.technician.update({
      where: { userId },
      data: { specialization },
    });
  },
};
