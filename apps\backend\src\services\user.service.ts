// src/services/user.service.ts

import { prisma } from '../config/db';

export const UserService = {
  /**
   * Get user by ID (without password)
   */
  async getById(id: string) {
    return prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        fullName: true,
        email: true,
        phone: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  },

  /**
   * Update user profile
   */
  async updateProfile(id: string, data: { fullName?: string; phone?: string }) {
    return prisma.user.update({
      where: { id },
      data,
      select: {
        id: true,
        fullName: true,
        email: true,
        phone: true,
        role: true,
        updatedAt: true,
      },
    });
  },

  /**
   * Get all users (admin-only)
   */
  async listUsers() {
    return prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        fullName: true,
        email: true,
        phone: true,
        role: true,
        createdAt: true,
      },
    });
  },
};
