// CTRON Home - Chat Socket Handlers
// Real-time chat functionality via Socket.IO

import { Server, Socket } from 'socket.io';
import { ChatService } from '../services/chat.service';

interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    userId: string;
    role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
    email: string;
  };
}

interface ChatEventData {
  chatId: string;
  content?: string;
  attachments?: any[];
  isTyping?: boolean;
}

export const registerChatSockets = (io: Server, socket: AuthenticatedSocket) => {
  const userId = socket.user?.userId || socket.user?.id;
  const joinedRooms = new Set<string>();

  if (!userId) {
    // User not authenticated, disconnect
    socket.disconnect(true);
    return;
  }

  /**
   * Join a chat room
   */
  socket.on('chat:join', async (data: { chatId: string }) => {
    try {
      const { chatId } = data;

      // Verify user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(chatId, userId);
      if (!isParticipant) {
        socket.emit('chat:error', { message: 'Access denied to this chat' });
        return;
      }

      // Join the chat room
      socket.join(`chat:${chatId}`);
      joinedRooms.add(chatId);

      // Notify other participants that user joined
      socket.to(`chat:${chatId}`).emit('chat:userJoined', {
        userId,
        chatId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Error joining chat:', error);
      socket.emit('chat:error', { message: 'Failed to join chat' });
    }
  });

  /**
   * Leave a chat room
   */
  socket.on('chat:leave', async (data: { chatId: string }) => {
    try {
      const { chatId } = data;

      // Leave the chat room
      socket.leave(`chat:${chatId}`);
      joinedRooms.delete(chatId);

      // Notify other participants that user left
      socket.to(`chat:${chatId}`).emit('chat:userLeft', {
        userId,
        chatId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Error leaving chat:', error);
    }
  });

  /**
   * Send a message via Socket.IO
   */
  socket.on('chat:sendMessage', async (data: ChatEventData) => {
    try {
      const { chatId, content, attachments = [] } = data;

      if (!content?.trim()) {
        socket.emit('chat:error', { message: 'Message content is required' });
        return;
      }

      // Verify user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(chatId, userId);
      if (!isParticipant) {
        socket.emit('chat:error', { message: 'Access denied to this chat' });
        return;
      }

      // Send message via service
      const message = await ChatService.sendMessage(chatId, userId, content, attachments);

      // Emit to all participants in the chat room
      io.to(`chat:${chatId}`).emit('chat:newMessage', {
        message,
        chatId
      });

      // Message sent successfully
    } catch (error) {
      console.error('❌ Error sending message:', error);
      socket.emit('chat:error', { message: 'Failed to send message' });
    }
  });

  /**
   * Typing indicator
   */
  socket.on('chat:typing', async (data: ChatEventData) => {
    try {
      const { chatId, isTyping } = data;

      // Verify user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(chatId, userId);
      if (!isParticipant) {
        return;
      }

      // Emit typing status to other participants (not to sender)
      socket.to(`chat:${chatId}`).emit('chat:userTyping', {
        userId,
        chatId,
        isTyping,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Error handling typing indicator:', error);
    }
  });

  /**
   * Mark message as read
   */
  socket.on('chat:markRead', async (data: { messageId: string }) => {
    try {
      const { messageId } = data;

      // Verify user can mark this message as read
      const canMarkRead = await ChatService.verifyMessageAccess(messageId, userId);
      if (!canMarkRead) {
        socket.emit('chat:error', { message: 'Access denied to this message' });
        return;
      }

      // Mark message as read
      await ChatService.markMessageRead(messageId, userId);

      // Emit read status
      socket.emit('chat:messageRead', {
        messageId,
        readBy: userId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Error marking message as read:', error);
      socket.emit('chat:error', { message: 'Failed to mark message as read' });
    }
  });

  /**
   * Get online users in a chat
   */
  socket.on('chat:getOnlineUsers', async (data: { chatId: string }) => {
    try {
      const { chatId } = data;

      // Verify user is participant in this chat
      const isParticipant = await ChatService.verifyParticipant(chatId, userId);
      if (!isParticipant) {
        socket.emit('chat:error', { message: 'Access denied to this chat' });
        return;
      }

      // Get all sockets in the chat room
      const socketsInRoom = await io.in(`chat:${chatId}`).fetchSockets();
      const onlineUserIds = socketsInRoom
        .map(s => (s as any).user?.userId || (s as any).user?.id)
        .filter(Boolean);

      socket.emit('chat:onlineUsers', {
        chatId,
        onlineUsers: onlineUserIds,
        count: onlineUserIds.length
      });
    } catch (error) {
      console.error('❌ Error getting online users:', error);
      socket.emit('chat:error', { message: 'Failed to get online users' });
    }
  });

  /**
   * Handle disconnection - cleanup all joined rooms
   */
  socket.on('disconnect', () => {
    // Leave all joined chat rooms and notify participants
    joinedRooms.forEach(chatId => {
      socket.to(`chat:${chatId}`).emit('chat:userLeft', {
        userId,
        chatId,
        timestamp: new Date().toISOString()
      });
    });

    // Clear the joined rooms set
    joinedRooms.clear();
  });
};