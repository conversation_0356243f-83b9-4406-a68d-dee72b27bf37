// src/sockets/index.ts

import { Server, Socket } from 'socket.io';
import { registerTechnicianSockets } from './technician.socket';
import { registerJobSockets } from './job.socket';
import { registerChatSockets } from './chat.socket';
import { logger } from '../utils/logger';
const jwt = require('jsonwebtoken');
import { env } from '../config/env';
import { AuthController } from '../controllers/auth.controller';

interface AuthenticatedSocket extends Socket {
  userId: string;
  userRole: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  userEmail: string;
  fullName: string;
}

interface SocketAuthPayload {
  userId: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  email: string;
  fullName: string;
}

export const registerSockets = (io: Server) => {
  // Enhanced authentication middleware for Socket.IO
  io.use(async (socket: any, next) => {
    try {
      // Extract token from multiple possible sources
      const token =
        socket.handshake.auth.token ||
        socket.handshake.headers.authorization?.replace('Bearer ', '') ||
        socket.handshake.query.token;

      if (!token) {
        logger.warn('Socket connection attempted without token', {
          socketId: socket.id,
          ip: socket.handshake.address,
        });
        return next(new Error('Authentication token required'));
      }

      // Check if token is blacklisted
      if (AuthController.isTokenBlacklisted(token)) {
        logger.warn('Socket connection attempted with blacklisted token', {
          socketId: socket.id,
          ip: socket.handshake.address,
        });
        return next(new Error('Token has been revoked'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, env.JWT_SECRET as string) as SocketAuthPayload;

      // Validate required fields
      if (!decoded.userId || !decoded.role || !decoded.email) {
        logger.warn('Socket authentication failed: Invalid token payload', {
          socketId: socket.id,
          hasUserId: !!decoded.userId,
          hasRole: !!decoded.role,
          hasEmail: !!decoded.email,
        });
        return next(new Error('Invalid token payload'));
      }

      // Attach standardized user info to socket
      socket.userId = decoded.userId;
      socket.userRole = decoded.role;
      socket.userEmail = decoded.email;
      socket.fullName = decoded.fullName;

      logger.info('Socket authenticated successfully', {
        socketId: socket.id,
        userId: decoded.userId,
        role: decoded.role,
        ip: socket.handshake.address,
      });

      next();
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        logger.warn('Socket authentication failed: Token expired', {
          socketId: socket.id,
          ip: socket.handshake.address,
        });
        return next(new Error('Token expired'));
      }

      if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Socket authentication failed: Invalid token', {
          socketId: socket.id,
          error: error instanceof Error ? error.message : String(error),
          ip: socket.handshake.address,
        });
        return next(new Error('Invalid authentication token'));
      }

      logger.error('Socket authentication error', {
        socketId: socket.id,
        error: error instanceof Error ? error.message : String(error),
        ip: socket.handshake.address,
      });

      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', (socket: Socket) => {
    const authenticatedSocket = socket as AuthenticatedSocket;
    const { userId, userRole, userEmail, fullName } = authenticatedSocket;

    logger.info('Socket connected successfully', {
      socketId: socket.id,
      userId,
      role: userRole,
      email: userEmail,
      ip: socket.handshake.address,
    });

    // Join user-specific room for notifications
    socket.join(`user:${userId}`);
    logger.debug(`User ${userId} joined personal room`);

    // Join role-specific room
    socket.join(`role:${userRole}`);
    logger.debug(`User ${userId} joined role room: ${userRole}`);

    // Register socket handlers with error handling
    try {
      registerTechnicianSockets(io, authenticatedSocket);
      registerJobSockets(io, authenticatedSocket);
      registerChatSockets(io, authenticatedSocket);

      logger.debug('Socket handlers registered successfully', { userId });
    } catch (error) {
      logger.error('Error registering socket handlers', {
        userId,
        socketId: socket.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      socket.emit('error', {
        message: 'Failed to initialize socket handlers',
        code: 'HANDLER_REGISTRATION_FAILED'
      });
    }

    // Handle user status updates with validation
    socket.on('updateStatus', (data: { status: 'online' | 'offline' | 'busy' }) => {
      try {
        const { status } = data;

        if (!['online', 'offline', 'busy'].includes(status)) {
          socket.emit('error', {
            message: 'Invalid status value',
            code: 'INVALID_STATUS'
          });
          return;
        }

        socket.broadcast.emit('userStatusUpdate', {
          userId,
          status,
          fullName,
          timestamp: new Date().toISOString()
        });

        logger.debug('User status updated', { userId, status });
      } catch (error) {
        logger.error('Error updating user status', {
          userId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        socket.emit('error', {
          message: 'Failed to update status',
          code: 'STATUS_UPDATE_FAILED'
        });
      }
    });

    // Handle ping/pong for connection health monitoring
    socket.on('ping', (data?: { timestamp?: string }) => {
      try {
        socket.emit('pong', {
          timestamp: new Date().toISOString(),
          clientTimestamp: data?.timestamp,
        });
      } catch (error) {
        logger.error('Error handling ping', { userId, error });
      }
    });

    // Handle socket errors
    socket.on('error', (error) => {
      logger.error('Socket error occurred', {
        userId,
        socketId: socket.id,
        error: error instanceof Error ? error.message : error,
      });
    });

    // Handle disconnection with cleanup
    socket.on('disconnect', (reason) => {
      logger.info('Socket disconnected', {
        socketId: socket.id,
        userId,
        reason,
        duration: Date.now() - new Date(socket.handshake.time).getTime(),
      });

      // Broadcast user offline status
      socket.broadcast.emit('userStatusUpdate', {
        userId,
        status: 'offline',
        fullName,
        timestamp: new Date().toISOString()
      });

      // Clean up any user-specific data
      // Note: Socket.IO automatically handles room cleanup
    });

    // Send welcome message with user info
    socket.emit('connected', {
      success: true,
      message: 'Successfully connected to CTRON Home',
      user: {
        id: userId,
        role: userRole,
        email: userEmail,
        fullName,
      },
      timestamp: new Date().toISOString(),
      socketId: socket.id,
    });
  });

  // Handle server-level events
  io.engine.on('connection_error', (err) => {
    logger.error('Socket.IO connection error:', err);
  });

  logger.info('Socket.IO server initialized with authentication and handlers');
};
