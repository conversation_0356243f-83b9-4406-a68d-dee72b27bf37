// src/sockets/job.socket.ts

import { Server, Socket } from 'socket.io';
import { prisma } from '../config/db';
import { logger } from '../utils/logger';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

export const registerJobSockets = (io: Server, socket: AuthenticatedSocket) => {
  const { userId } = socket;

  // Join job room for real-time updates
  socket.on('joinJob', (jobId: string) => {
    socket.join(`job:${jobId}`);
    logger.info(`User ${userId} joined job room: ${jobId}`);
  });

  // Leave job room
  socket.on('leaveJob', (jobId: string) => {
    socket.leave(`job:${jobId}`);
    logger.info(`User ${userId} left job room: ${jobId}`);
  });

  // Assign job to technician
  socket.on('assignJob', async (data: { jobId: string; technicianId: string }) => {
    try {
      const { jobId, technicianId } = data;

      // Update job assignment in database
      const updatedJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          technicianId,
          status: 'ACCEPTED'
        },
        include: {
          user: true,
          technician: { include: { user: true } }
        }
      });

      // Notify job room about assignment
      io.to(`job:${jobId}`).emit('jobAssigned', {
        jobId,
        technicianId,
        job: updatedJob,
        timestamp: new Date().toISOString()
      });

      // Notify technician about new job
      io.to(`tech:${technicianId}`).emit('newJobAssignment', {
        jobId,
        job: updatedJob,
        timestamp: new Date().toISOString()
      });

      // Notify homeowner about assignment
      if (updatedJob.user?.id) {
        io.to(`user:${updatedJob.user.id}`).emit('jobAssigned', {
          jobId,
          technician: updatedJob.technician,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Job ${jobId} assigned to technician ${technicianId}`);
    } catch (error) {
      logger.error('Error assigning job:', error);
      socket.emit('error', { message: 'Failed to assign job' });
    }
  });

  // Update job status
  socket.on('updateJobStatus', async (data: { jobId: string; status: string; notes?: string }) => {
    try {
      const { jobId, status, notes } = data;

      // Update job status in database
      const updatedJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          status: status as any
        },
        include: {
          user: true,
          technician: { include: { user: true } }
        }
      });

      // Notify job room about status update
      io.to(`job:${jobId}`).emit('jobStatusUpdate', {
        jobId,
        status,
        notes,
        job: updatedJob,
        timestamp: new Date().toISOString()
      });

      // Notify homeowner about status change
      if (updatedJob.user?.id) {
        io.to(`user:${updatedJob.user.id}`).emit('jobUpdate', {
          jobId,
          status,
          notes,
          timestamp: new Date().toISOString()
        });
      }

      // Notify technician about status change
      if (updatedJob.technician?.user?.id) {
        io.to(`user:${updatedJob.technician.user.id}`).emit('jobUpdate', {
          jobId,
          status,
          notes,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Job ${jobId} status updated to ${status}`);
    } catch (error) {
      logger.error('Error updating job status:', error);
      socket.emit('error', { message: 'Failed to update job status' });
    }
  });

  // Complete job
  socket.on('completeJob', async (data: { jobId: string; proofImages?: string[]; notes?: string }) => {
    try {
      const { jobId, proofImages, notes } = data;

      // Update job completion in database
      const completedJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          proofImageKey: proofImages?.[0] || null // Store first image as proof
        },
        include: {
          user: true,
          technician: { include: { user: true } }
        }
      });

      // Notify job room about completion
      io.to(`job:${jobId}`).emit('jobCompleted', {
        jobId,
        job: completedJob,
        proofImages,
        notes,
        timestamp: new Date().toISOString()
      });

      // Notify homeowner about completion
      if (completedJob.user?.id) {
        io.to(`user:${completedJob.user.id}`).emit('jobCompleted', {
          jobId,
          proofImages,
          notes,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Job ${jobId} completed`);
    } catch (error) {
      logger.error('Error completing job:', error);
      socket.emit('error', { message: 'Failed to complete job' });
    }
  });

  // Cancel job
  socket.on('cancelJob', async (data: { jobId: string; reason?: string }) => {
    try {
      const { jobId, reason } = data;

      // Update job cancellation in database
      const cancelledJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          status: 'CANCELLED'
        },
        include: {
          user: true,
          technician: { include: { user: true } }
        }
      });

      // Notify job room about cancellation
      io.to(`job:${jobId}`).emit('jobCancelled', {
        jobId,
        reason,
        job: cancelledJob,
        timestamp: new Date().toISOString()
      });

      // Notify all relevant parties
      if (cancelledJob.user?.id) {
        io.to(`user:${cancelledJob.user.id}`).emit('jobCancelled', {
          jobId,
          reason,
          timestamp: new Date().toISOString()
        });
      }

      if (cancelledJob.technician?.user?.id) {
        io.to(`user:${cancelledJob.technician.user.id}`).emit('jobCancelled', {
          jobId,
          reason,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Job ${jobId} cancelled. Reason: ${reason}`);
    } catch (error) {
      logger.error('Error cancelling job:', error);
      socket.emit('error', { message: 'Failed to cancel job' });
    }
  });

  // Request job details
  socket.on('getJobDetails', async (jobId: string) => {
    try {
      const job = await prisma.job.findUnique({
        where: { id: jobId },
        include: {
          user: true,
          technician: { include: { user: true } },
          review: true,
          payment: true
        }
      });

      if (job) {
        socket.emit('jobDetails', {
          jobId,
          job,
          timestamp: new Date().toISOString()
        });
      } else {
        socket.emit('error', { message: 'Job not found' });
      }
    } catch (error) {
      logger.error('Error fetching job details:', error);
      socket.emit('error', { message: 'Failed to fetch job details' });
    }
  });
};
