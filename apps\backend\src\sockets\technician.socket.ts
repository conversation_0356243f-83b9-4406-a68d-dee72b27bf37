// src/sockets/technician.socket.ts

import { Server, Socket } from 'socket.io';
import { prisma } from '../config/db';
import { logger } from '../utils/logger';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

export const registerTechnicianSockets = (io: Server, socket: AuthenticatedSocket) => {
  const { userId } = socket;

  // Technician goes online
  socket.on('goOnline', async (data: { technicianId: string; location?: { lat: number; lng: number } }) => {
    try {
      const { technicianId, location } = data;

      // Join technician room
      socket.join(`tech:${technicianId}`);

      // Update technician availability in database
      await prisma.technician.update({
        where: { id: technicianId },
        data: {
          isAvailable: true,
          latitude: location?.lat,
          longitude: location?.lng
        }
      });

      // Notify technician about online status
      io.to(`tech:${technicianId}`).emit('statusUpdate', {
        online: true,
        timestamp: new Date().toISOString()
      });

      // Broadcast availability to nearby homeowners if location provided
      if (location) {
        socket.broadcast.emit('technicianOnline', {
          technicianId,
          location,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Technician ${technicianId} went online`);
    } catch (error) {
      logger.error('Error setting technician online:', error);
      socket.emit('error', { message: 'Failed to go online' });
    }
  });

  // Technician goes offline
  socket.on('goOffline', async (technicianId: string) => {
    try {
      // Leave technician room
      socket.leave(`tech:${technicianId}`);

      // Update technician availability in database
      await prisma.technician.update({
        where: { id: technicianId },
        data: {
          isAvailable: false
        }
      });

      // Notify technician about offline status
      io.to(`tech:${technicianId}`).emit('statusUpdate', {
        online: false,
        timestamp: new Date().toISOString()
      });

      // Broadcast offline status
      socket.broadcast.emit('technicianOffline', {
        technicianId,
        timestamp: new Date().toISOString()
      });

      logger.info(`Technician ${technicianId} went offline`);
    } catch (error) {
      logger.error('Error setting technician offline:', error);
      socket.emit('error', { message: 'Failed to go offline' });
    }
  });

  // Accept job assignment
  socket.on('acceptJob', async (data: { technicianId: string; jobId: string }) => {
    try {
      const { technicianId, jobId } = data;

      // Update job assignment in database
      const updatedJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          technicianId,
          status: 'ACCEPTED'
        },
        include: {
          user: true,
          technician: { include: { user: true } }
        }
      });

      // Notify job room about acceptance
      io.to(`job:${jobId}`).emit('jobAccepted', {
        jobId,
        technicianId,
        job: updatedJob,
        timestamp: new Date().toISOString()
      });

      // Notify homeowner about job acceptance
      if (updatedJob.user?.id) {
        io.to(`user:${updatedJob.user.id}`).emit('jobAccepted', {
          jobId,
          technician: updatedJob.technician,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Technician ${technicianId} accepted job ${jobId}`);
    } catch (error) {
      logger.error('Error accepting job:', error);
      socket.emit('error', { message: 'Failed to accept job' });
    }
  });

  // Decline job assignment
  socket.on('declineJob', async (data: { technicianId: string; jobId: string; reason?: string }) => {
    try {
      const { technicianId, jobId, reason } = data;

      // Update job status back to pending
      const updatedJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          technicianId: null,
          status: 'PENDING'
        },
        include: {
          user: true
        }
      });

      // Notify job room about decline
      io.to(`job:${jobId}`).emit('jobDeclined', {
        jobId,
        technicianId,
        reason,
        timestamp: new Date().toISOString()
      });

      // Notify homeowner about job decline
      if (updatedJob.user?.id) {
        io.to(`user:${updatedJob.user.id}`).emit('jobDeclined', {
          jobId,
          reason,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Technician ${technicianId} declined job ${jobId}. Reason: ${reason}`);
    } catch (error) {
      logger.error('Error declining job:', error);
      socket.emit('error', { message: 'Failed to decline job' });
    }
  });

  // Update technician location
  socket.on('updateLocation', async (data: { technicianId: string; location: { lat: number; lng: number } }) => {
    try {
      const { technicianId, location } = data;

      // Update location in database
      await prisma.technician.update({
        where: { id: technicianId },
        data: {
          latitude: location.lat,
          longitude: location.lng
        }
      });

      // Broadcast location update to relevant users
      socket.broadcast.emit('technicianLocationUpdate', {
        technicianId,
        location,
        timestamp: new Date().toISOString()
      });

      logger.info(`Technician ${technicianId} location updated`);
    } catch (error) {
      logger.error('Error updating technician location:', error);
      socket.emit('error', { message: 'Failed to update location' });
    }
  });

  // Start job
  socket.on('startJob', async (data: { technicianId: string; jobId: string }) => {
    try {
      const { technicianId, jobId } = data;

      // Update job status to in progress
      const updatedJob = await prisma.job.update({
        where: { id: jobId },
        data: {
          status: 'IN_PROGRESS'
        },
        include: {
          user: true,
          technician: { include: { user: true } }
        }
      });

      // Notify job room about start
      io.to(`job:${jobId}`).emit('jobStarted', {
        jobId,
        technicianId,
        job: updatedJob,
        timestamp: new Date().toISOString()
      });

      // Notify homeowner about job start
      if (updatedJob.user?.id) {
        io.to(`user:${updatedJob.user.id}`).emit('jobStarted', {
          jobId,
          technician: updatedJob.technician,
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Technician ${technicianId} started job ${jobId}`);
    } catch (error) {
      logger.error('Error starting job:', error);
      socket.emit('error', { message: 'Failed to start job' });
    }
  });
};
