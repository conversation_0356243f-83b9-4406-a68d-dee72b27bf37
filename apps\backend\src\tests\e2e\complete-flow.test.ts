// apps/backend/src/tests/e2e/complete-flow.test.ts

import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { app } from '../../server';
import {
  setupTestDatabase,
  teardownTestDatabase,
  createTestUser,
  createTestTechnician,
  createTestJob,
  createTestChat,
  createTestPushToken,
  createAuthToken,
  cleanupTestData,
} from '../setup';

describe('Complete MVP Flow End-to-End Tests', () => {
  let prisma: PrismaClient;
  let testDatabaseUrl: string;
  let homeowner: any;
  let technician: any;
  let technicianUser: any;
  let job: any;
  let chat: any;
  let homeownerToken: string;
  let technicianToken: string;
  let adminToken: string;

  beforeAll(async () => {
    testDatabaseUrl = await setupTestDatabase();
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: testDatabaseUrl,
        },
      },
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await teardownTestDatabase(testDatabaseUrl);
  });

  beforeEach(async () => {
    await cleanupTestData(prisma);
    
    // Create test users
    homeowner = await createTestUser(prisma, { role: 'HOMEOWNER' });
    technicianUser = await createTestUser(prisma, { role: 'TECHNICIAN' });
    const adminUser = await createTestUser(prisma, { role: 'ADMIN' });
    
    // Create technician profile
    technician = await createTestTechnician(prisma, technicianUser.id);
    
    // Create test job
    job = await createTestJob(prisma, homeowner.id, technician.id);
    
    // Create test chat
    chat = await createTestChat(prisma, job.id);
    
    // Create auth tokens
    homeownerToken = createAuthToken(homeowner.id, 'HOMEOWNER');
    technicianToken = createAuthToken(technicianUser.id, 'TECHNICIAN');
    adminToken = createAuthToken(adminUser.id, 'ADMIN');
  });

  describe('🏠 Complete Homeowner Flow', () => {
    it('should complete full homeowner journey: job creation → chat → payment', async () => {
      // 1. Create a new job
      const jobResponse = await request(app)
        .post('/api/jobs')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          issue: 'Leaky faucet repair',
          description: 'Kitchen faucet is dripping constantly',
          priority: 'medium',
          latitude: 51.5074,
          longitude: -0.1278,
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        });

      expect(jobResponse.status).toBe(201);
      const newJob = jobResponse.body.data.job;

      // 2. Register push token for notifications
      const tokenResponse = await request(app)
        .post('/api/notifications/register-token')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          pushToken: 'ExponentPushToken[test123456789]',
          platform: 'ios',
          deviceInfo: {
            deviceId: 'test-device-id',
            appVersion: '1.0.0',
          },
        });

      expect(tokenResponse.status).toBe(200);

      // 3. Get or create chat for the job
      const chatResponse = await request(app)
        .post('/api/chat/job-chat')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          jobId: newJob.id,
        });

      expect(chatResponse.status).toBe(200);
      const jobChat = chatResponse.body.data.chat;

      // 4. Send a message in the chat
      const messageResponse = await request(app)
        .post('/api/chat/messages')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          chatId: jobChat.id,
          content: 'When can you start the repair?',
        });

      expect(messageResponse.status).toBe(201);

      // 5. Get chat messages
      const messagesResponse = await request(app)
        .get(`/api/chat/${jobChat.id}/messages`)
        .set('Authorization', `Bearer ${homeownerToken}`);

      expect(messagesResponse.status).toBe(200);
      expect(messagesResponse.body.data.messages).toHaveLength(1);

      // 6. Create payment for the job
      const paymentResponse = await request(app)
        .post('/api/payments/create-intent')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          jobId: newJob.id,
          amount: 150.00,
        });

      expect(paymentResponse.status).toBe(200);
      expect(paymentResponse.body.data.clientSecret).toBeDefined();
    });
  });

  describe('🔧 Complete Technician Flow', () => {
    it('should complete full technician journey: accept job → chat → complete job', async () => {
      // 1. Get assigned jobs
      const jobsResponse = await request(app)
        .get('/api/jobs/technician')
        .set('Authorization', `Bearer ${technicianToken}`);

      expect(jobsResponse.status).toBe(200);

      // 2. Accept the job
      const acceptResponse = await request(app)
        .patch(`/api/jobs/${job.id}/accept`)
        .set('Authorization', `Bearer ${technicianToken}`);

      expect(acceptResponse.status).toBe(200);

      // 3. Register push token
      await request(app)
        .post('/api/notifications/register-token')
        .set('Authorization', `Bearer ${technicianToken}`)
        .send({
          pushToken: 'ExponentPushToken[tech123456789]',
          platform: 'android',
        });

      // 4. Get chat for the job
      const chatResponse = await request(app)
        .get(`/api/chat/job/${job.id}`)
        .set('Authorization', `Bearer ${technicianToken}`);

      expect(chatResponse.status).toBe(200);

      // 5. Send response message
      const messageResponse = await request(app)
        .post('/api/chat/messages')
        .set('Authorization', `Bearer ${technicianToken}`)
        .send({
          chatId: chat.id,
          content: 'I can start tomorrow morning at 9 AM',
        });

      expect(messageResponse.status).toBe(201);

      // 6. Update job status to in progress
      const progressResponse = await request(app)
        .patch(`/api/jobs/${job.id}/status`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .send({
          status: 'IN_PROGRESS',
        });

      expect(progressResponse.status).toBe(200);

      // 7. Complete the job
      const completeResponse = await request(app)
        .patch(`/api/jobs/${job.id}/complete`)
        .set('Authorization', `Bearer ${technicianToken}`)
        .send({
          completionNotes: 'Faucet repaired successfully. Replaced worn gasket.',
          proofPhotos: ['https://example.com/photo1.jpg'],
        });

      expect(completeResponse.status).toBe(200);
    });
  });

  describe('🤖 AI Assistant Flow', () => {
    it('should handle AI queries for different user roles', async () => {
      // 1. Admin AI query
      const adminQueryResponse = await request(app)
        .post('/api/ai/query')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          query: 'What are the current system performance metrics?',
        });

      expect(adminQueryResponse.status).toBe(200);
      expect(adminQueryResponse.body.data.response).toBeDefined();
      expect(adminQueryResponse.body.data.confidence).toBeGreaterThan(0);

      // 2. Technician AI query
      const techQueryResponse = await request(app)
        .post('/api/ai/query')
        .set('Authorization', `Bearer ${technicianToken}`)
        .send({
          query: 'How can I improve my customer ratings?',
        });

      expect(techQueryResponse.status).toBe(200);
      expect(techQueryResponse.body.data.response).toBeDefined();

      // 3. Homeowner AI query
      const homeownerQueryResponse = await request(app)
        .post('/api/ai/query')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          query: 'How do I create a service request?',
        });

      expect(homeownerQueryResponse.status).toBe(200);
      expect(homeownerQueryResponse.body.data.response).toBeDefined();

      // 4. Get AI templates
      const templatesResponse = await request(app)
        .get('/api/ai/templates')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(templatesResponse.status).toBe(200);
      expect(templatesResponse.body.data.templates).toBeInstanceOf(Array);
    });
  });

  describe('📱 Push Notification Flow', () => {
    it('should handle complete notification lifecycle', async () => {
      // 1. Register push tokens for both users
      await createTestPushToken(prisma, homeowner.id);
      await createTestPushToken(prisma, technicianUser.id);

      // 2. Admin sends notification to users
      const notificationResponse = await request(app)
        .post('/api/notifications/send')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          userIds: [homeowner.id, technicianUser.id],
          title: 'System Maintenance',
          body: 'Scheduled maintenance will occur tonight at 2 AM',
          data: { type: 'maintenance', priority: 'high' },
        });

      expect(notificationResponse.status).toBe(200);

      // 3. Get notification history
      const historyResponse = await request(app)
        .get('/api/notifications')
        .set('Authorization', `Bearer ${homeownerToken}`);

      expect(historyResponse.status).toBe(200);
      expect(historyResponse.body.data.notifications).toBeInstanceOf(Array);

      // 4. Get unread count
      const unreadResponse = await request(app)
        .get('/api/notifications/unread-count')
        .set('Authorization', `Bearer ${homeownerToken}`);

      expect(unreadResponse.status).toBe(200);
      expect(typeof unreadResponse.body.data.count).toBe('number');

      // 5. Mark notification as read
      const notifications = historyResponse.body.data.notifications;
      if (notifications.length > 0) {
        const markReadResponse = await request(app)
          .patch(`/api/notifications/${notifications[0].id}/read`)
          .set('Authorization', `Bearer ${homeownerToken}`);

        expect(markReadResponse.status).toBe(200);
      }
    });
  });

  describe('💬 Real-time Chat Integration', () => {
    it('should handle chat operations and message flow', async () => {
      // 1. Get chat participants
      const participantsResponse = await request(app)
        .get(`/api/chat/${chat.id}/participants`)
        .set('Authorization', `Bearer ${homeownerToken}`);

      expect(participantsResponse.status).toBe(200);

      // 2. Send multiple messages
      const messages = [
        'Hello, I need help with the repair',
        'What time works best for you?',
        'I can be available all day tomorrow',
      ];

      for (const content of messages) {
        const messageResponse = await request(app)
          .post('/api/chat/messages')
          .set('Authorization', `Bearer ${homeownerToken}`)
          .send({
            chatId: chat.id,
            content,
          });

        expect(messageResponse.status).toBe(201);
      }

      // 3. Get all messages
      const allMessagesResponse = await request(app)
        .get(`/api/chat/${chat.id}/messages`)
        .set('Authorization', `Bearer ${homeownerToken}`);

      expect(allMessagesResponse.status).toBe(200);
      expect(allMessagesResponse.body.data.messages.length).toBeGreaterThanOrEqual(3);

      // 4. Mark messages as read
      const markReadResponse = await request(app)
        .patch(`/api/chat/${chat.id}/read`)
        .set('Authorization', `Bearer ${technicianToken}`);

      expect(markReadResponse.status).toBe(200);

      // 5. Get chat list
      const chatListResponse = await request(app)
        .get('/api/chat/list')
        .set('Authorization', `Bearer ${homeownerToken}`);

      expect(chatListResponse.status).toBe(200);
      expect(chatListResponse.body.data.chats).toBeInstanceOf(Array);
    });
  });

  describe('🔒 Security and Validation', () => {
    it('should enforce proper authentication and authorization', async () => {
      // 1. Unauthorized access should fail
      const unauthorizedResponse = await request(app)
        .get('/api/jobs/technician');

      expect(unauthorizedResponse.status).toBe(401);

      // 2. Wrong role access should fail
      const wrongRoleResponse = await request(app)
        .post('/api/notifications/send')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          userIds: [technician.id],
          title: 'Test',
          body: 'Test notification',
        });

      expect(wrongRoleResponse.status).toBe(403);

      // 3. Invalid data should be rejected
      const invalidDataResponse = await request(app)
        .post('/api/jobs')
        .set('Authorization', `Bearer ${homeownerToken}`)
        .send({
          issue: '', // Empty issue should fail validation
          description: 'Test description',
        });

      expect(invalidDataResponse.status).toBe(400);
    });
  });
});
