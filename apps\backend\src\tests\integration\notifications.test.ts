// apps/backend/src/tests/integration/notifications.test.ts

import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { app } from '../../server';
import {
  setupTestDatabase,
  teardownTestDatabase,
  createTestUser,
  createTestPushToken,
  createTestNotification,
  createAuthToken,
  cleanupTestData,
  mockNotificationService,
} from '../setup';

describe('Notifications API Integration Tests', () => {
  let prisma: PrismaClient;
  let testDatabaseUrl: string;
  let testUser: any;
  let authToken: string;

  beforeAll(async () => {
    testDatabaseUrl = await setupTestDatabase();
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: testDatabaseUrl,
        },
      },
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await teardownTestDatabase(testDatabaseUrl);
  });

  beforeEach(async () => {
    await cleanupTestData(prisma);
    testUser = await createTestUser(prisma);
    authToken = createAuthToken(testUser.id, testUser.role);
  });

  describe('POST /api/notifications/register-token', () => {
    it('should register a push token successfully', async () => {
      const pushToken = 'ExponentPushToken[test123456789]';
      const deviceInfo = {
        platform: 'ios',
        deviceId: 'test-device-id',
        appVersion: '1.0.0',
      };

      const response = await request(app)
        .post('/api/notifications/register-token')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          pushToken,
          platform: 'ios',
          deviceInfo,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Push token registered successfully');

      // Verify token was saved to database
      const savedToken = await prisma.pushToken.findFirst({
        where: { userId: testUser.id, token: pushToken },
      });
      expect(savedToken).toBeTruthy();
      expect(savedToken?.isActive).toBe(true);
    });

    it('should reject invalid push token', async () => {
      const response = await request(app)
        .post('/api/notifications/register-token')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          pushToken: 'invalid-token',
          platform: 'ios',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/notifications/register-token')
        .send({
          pushToken: 'ExponentPushToken[test123456789]',
          platform: 'ios',
        });

      expect(response.status).toBe(401);
    });
  });

  describe('DELETE /api/notifications/unregister-token', () => {
    it('should unregister a push token successfully', async () => {
      // First create a push token
      const pushToken = await createTestPushToken(prisma, testUser.id);

      const response = await request(app)
        .delete('/api/notifications/unregister-token')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          pushToken: pushToken.token,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify token was deactivated
      const updatedToken = await prisma.pushToken.findUnique({
        where: { id: pushToken.id },
      });
      expect(updatedToken?.isActive).toBe(false);
    });

    it('should require push token in request body', async () => {
      const response = await request(app)
        .delete('/api/notifications/unregister-token')
        .set('Authorization', `Bearer ${authToken}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/notifications/send', () => {
    let adminUser: any;
    let adminToken: string;

    beforeEach(async () => {
      adminUser = await createTestUser(prisma, { role: 'ADMIN' });
      adminToken = createAuthToken(adminUser.id, 'ADMIN');
    });

    it('should send notification to users (admin only)', async () => {
      // Create push token for test user
      await createTestPushToken(prisma, testUser.id);

      const response = await request(app)
        .post('/api/notifications/send')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          userIds: [testUser.id],
          title: 'Test Notification',
          body: 'This is a test notification',
          data: { type: 'test' },
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should reject non-admin users', async () => {
      const response = await request(app)
        .post('/api/notifications/send')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          userIds: [testUser.id],
          title: 'Test Notification',
          body: 'This is a test notification',
        });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/notifications', () => {
    it('should get user notification history', async () => {
      // Create test notifications
      await createTestNotification(prisma, testUser.id);
      await createTestNotification(prisma, testUser.id);

      const response = await request(app)
        .get('/api/notifications')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.notifications).toHaveLength(2);
      expect(response.body.data.pagination).toBeDefined();
    });

    it('should support pagination', async () => {
      // Create multiple notifications
      for (let i = 0; i < 5; i++) {
        await createTestNotification(prisma, testUser.id);
      }

      const response = await request(app)
        .get('/api/notifications?page=1&limit=3')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.notifications).toHaveLength(3);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(3);
      expect(response.body.data.pagination.total).toBe(5);
    });
  });

  describe('PATCH /api/notifications/:id/read', () => {
    it('should mark notification as read', async () => {
      const notification = await createTestNotification(prisma, testUser.id);

      const response = await request(app)
        .patch(`/api/notifications/${notification.id}/read`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify notification was marked as read
      const updatedNotification = await prisma.notification.findUnique({
        where: { id: notification.id },
      });
      expect(updatedNotification?.isRead).toBe(true);
      expect(updatedNotification?.readAt).toBeTruthy();
    });

    it('should return 404 for non-existent notification', async () => {
      const response = await request(app)
        .patch('/api/notifications/non-existent-id/read')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PATCH /api/notifications/read-all', () => {
    it('should mark all notifications as read', async () => {
      // Create multiple unread notifications
      await createTestNotification(prisma, testUser.id);
      await createTestNotification(prisma, testUser.id);
      await createTestNotification(prisma, testUser.id);

      const response = await request(app)
        .patch('/api/notifications/read-all')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify all notifications were marked as read
      const unreadCount = await prisma.notification.count({
        where: { userId: testUser.id, isRead: false },
      });
      expect(unreadCount).toBe(0);
    });
  });

  describe('GET /api/notifications/unread-count', () => {
    it('should return correct unread count', async () => {
      // Create notifications (some read, some unread)
      const notification1 = await createTestNotification(prisma, testUser.id);
      await createTestNotification(prisma, testUser.id);
      await createTestNotification(prisma, testUser.id);

      // Mark one as read
      await prisma.notification.update({
        where: { id: notification1.id },
        data: { isRead: true, readAt: new Date() },
      });

      const response = await request(app)
        .get('/api/notifications/unread-count')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.count).toBe(2);
    });
  });
});
