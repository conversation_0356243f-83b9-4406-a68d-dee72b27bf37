// apps/backend/src/tests/setup.ts
import { jest } from '@jest/globals';
import { PrismaClient } from '@prisma/client';

declare global {
  var prisma: PrismaClient;
}
import { execSync } from 'child_process';
import { randomBytes } from 'crypto';



// Configure Jest environment
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';

  // Suppress console logs during tests unless explicitly needed
  if (!process.env.VERBOSE_TESTS) {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }
});

afterAll(async () => {
  // Enhanced cleanup with timeout and multiple safety checks
  const cleanup = async () => {
    if (global.prisma) {
      try {
        await global.prisma.$disconnect();
      } catch (e) {
        console.error('Prisma disconnect error:', e);
      }
    }

    // Cleanup test databases
    const dbName = process.env.DATABASE_URL?.split('/').pop();
    if (dbName?.startsWith('test_')) {
      try {
        execSync(`dropdb ${dbName}`, { stdio: 'ignore' });
      } catch (error) {
        console.error('Database cleanup error:', error);
      }
    }

    // Clear any remaining mocks
    jest.clearAllMocks();
    
    // Clear Node.js module cache
    jest.resetModules();
  };

  // Run cleanup with timeout protection
  await Promise.race([
    cleanup(),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Cleanup timeout')), 10000)
    )
  ]).catch(e => console.error('Cleanup failed:', e));
});

// Test database setup
const generateDatabaseUrl = () => {
  const testId = randomBytes(8).toString('hex');
  const baseUrl = process.env.TEST_DATABASE_URL ||
    process.env.DATABASE_URL?.replace(/\/ctron/, '/postgres') ||
    'postgresql://postgres:admin1234@localhost:5432/postgres';

  if (!baseUrl) {
    throw new Error('DATABASE_URL or TEST_DATABASE_URL must be set for testing');
  }

  // Validate and parse database URL using URL object
  const url = new URL(baseUrl);
  const dbName = url.pathname.slice(1); // Remove leading slash
  url.pathname = `/test_${dbName}_${testId}`;
  
  return url.toString();
};

export const setupTestDatabase = async () => {
  const testDatabaseUrl = generateDatabaseUrl();
  process.env.DATABASE_URL = testDatabaseUrl;

  try {
    // Create test database
    const dbName = new URL(testDatabaseUrl).pathname.split('/').pop();
    // Use psql to create database with proper connection string
    // Verify PostgreSQL connection and create database with PowerShell
    try {
      // Check PostgreSQL connection
      execSync('psql -U postgres -c "SELECT 1"', {
        stdio: 'inherit',
        env: { ...process.env, PGPASSWORD: 'admin1234' }
      });

      // Create database with explicit PowerShell command
      const createDbCmd = `psql -U postgres -c "CREATE DATABASE \\"${dbName}\\""`;
      console.log(`Creating test database: ${createDbCmd}`);
      
      execSync(createDbCmd, {
        stdio: 'inherit',
        env: { ...process.env, PGPASSWORD: 'admin1234' },
        shell: 'powershell.exe'
      });

      // Verify database exists
      execSync(`psql -U postgres -c "\\l ${dbName}"`, {
        stdio: 'inherit',
        env: { ...process.env, PGPASSWORD: 'admin1234' }
      });
    } catch (error) {
      console.error('Database setup failed:', error instanceof Error ? error.message : String(error));
      throw new Error('Test database setup failed. Verify PostgreSQL is running and credentials are correct.');
    }
  } catch (error) {
    console.error('Database creation error:', error instanceof Error ? error.message : String(error));
  }

  // Run migrations with retry logic
  // Verify database connection before migrations
  const testConnection = async () => {
    const prisma = new PrismaClient({
      datasources: { db: { url: testDatabaseUrl } }
    });
    await prisma.$connect();
    await prisma.$disconnect();
  };

  let attempts = 0;
  const maxAttempts = 5;
  const retryDelay = 2000;
  
  while (attempts < maxAttempts) {
    try {
      // First verify basic connection
      await testConnection();
      
      // Then run migrations
      execSync('npx prisma db push --force-reset', {
        env: { ...process.env, DATABASE_URL: testDatabaseUrl },
        stdio: 'inherit',
        timeout: 30000
      });
      break;
    } catch (error) {
      attempts++;
      if (attempts === maxAttempts) {
        console.error('Final migration error:', error);
        throw new Error(`Failed migrations after ${maxAttempts} attempts. Last error: ${error instanceof Error ? error.message : String(error)}`);
      }
      console.log(`Migration attempt ${attempts} failed, retrying in ${retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  // Initialize Prisma client
  global.prisma = new PrismaClient({
    datasources: {
      db: { url: testDatabaseUrl }
    }
  });

  return testDatabaseUrl;
};

export const teardownTestDatabase = async (databaseUrl: string) => {
  const dbName = databaseUrl.split('/').pop();
  try {
    execSync(
      `psql postgresql://postgres:admin1234@localhost:5432/postgres -c "DROP DATABASE IF EXISTS \\"${dbName}\\""`,
      { stdio: 'ignore' }
    );
  } catch (error) {
    // Database might not exist
  }
};

// Test data factories
export const createTestUser = async (prisma: PrismaClient, overrides = {}) => {
  const testId = randomBytes(4).toString('hex');
  const defaultUser = {
    email: `test-${testId}@test.local`,
    fullName: `Test User ${testId}`,
    password: '$2b$10$hashedpassword', // Pre-hashed password for testing
    phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    role: 'HOMEOWNER' as const,
  };

  return await prisma.user.create({
    data: { ...defaultUser, ...overrides },
  });
};

export const createTestTechnician = async (prisma: PrismaClient, userId?: string) => {
  let user;
  if (userId) {
    user = await prisma.user.findUnique({ where: { id: userId } });
  } else {
    user = await createTestUser(prisma, { role: 'TECHNICIAN' });
  }

  if (!user) throw new Error('User not found for technician creation');

  return await prisma.technician.create({
    data: {
      userId: user.id,
      specialization: 'Plumbing',
      isAvailable: true,
      rating: 4.5,
      latitude: 51.5074,
      longitude: -0.1278,
    },
  });
};

export const createTestJob = async (prisma: PrismaClient, userId?: string, technicianId?: string) => {
  let user;
  if (userId) {
    user = await prisma.user.findUnique({ where: { id: userId } });
  } else {
    user = await createTestUser(prisma);
  }

  if (!user) throw new Error('User not found for job creation');

  return await prisma.job.create({
    data: {
      issue: 'Test plumbing issue',
      description: 'Test description for plumbing repair',
      priority: 'medium',
      latitude: 51.5074,
      longitude: -0.1278,
      status: 'PENDING',
      scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      userId: user.id,
      technicianId,
    },
  });
};

export const createTestChat = async (prisma: PrismaClient, jobId?: string) => {
  let job;
  if (jobId) {
    job = await prisma.job.findUnique({ where: { id: jobId } });
  } else {
    job = await createTestJob(prisma);
  }

  if (!job) throw new Error('Job not found for chat creation');

  return await prisma.chat.create({
    data: {
      jobId: job.id,
      status: 'ACTIVE',
    },
  });
};

export const createTestMessage = async (prisma: PrismaClient, chatId: string, senderId: string) => {
  return await prisma.message.create({
    data: {
      chatId,
      senderId,
      content: 'Test message content',
      attachments: null,
      read: false,
    },
  });
};

export const createTestPayment = async (prisma: PrismaClient, jobId: string, userId: string) => {
  return await prisma.payment.create({
    data: {
      jobId,
      userId,
      amount: 150.00,
      currency: 'GBP',
      stripePaymentIntentId: `pi_test_${randomBytes(8).toString('hex')}`,
      isReleased: false,
      isFrozen: false,
    },
  });
};

export const createTestReview = async (prisma: PrismaClient, jobId: string, userId: string, technicianId?: string) => {
  return await prisma.review.create({
    data: {
      jobId,
      userId,
      technicianId,
      rating: 5,
      comment: 'Excellent service!',
    },
  });
};

export const createTestPushToken = async (prisma: PrismaClient, userId: string) => {
  return await prisma.pushToken.create({
    data: {
      userId,
      token: `ExponentPushToken[${randomBytes(16).toString('hex')}]`,
      isActive: true,
      deviceInfo: JSON.stringify({
        platform: 'ios',
        deviceId: randomBytes(8).toString('hex'),
        appVersion: '1.0.0',
      }),
    },
  });
};

export const createTestNotification = async (prisma: PrismaClient, userId: string) => {
  return await prisma.notification.create({
    data: {
      userId,
      title: 'Test Notification',
      body: 'This is a test notification',
      type: 'PUSH',
      status: 'SENT',
      isRead: false,
      sentAt: new Date(),
    },
  });
};

// Helper functions for testing
export const cleanupTestData = async (prisma: PrismaClient) => {
  // Delete in reverse order of dependencies
  await prisma.notification.deleteMany();
  await prisma.pushToken.deleteMany();
  await prisma.message.deleteMany();
  await prisma.chatParticipant.deleteMany();
  await prisma.chat.deleteMany();
  await prisma.review.deleteMany();
  await prisma.payment.deleteMany();
  await prisma.job.deleteMany();
  await prisma.technician.deleteMany();
  await prisma.user.deleteMany();
  await prisma.setting.deleteMany();
};

export const createAuthToken = (userId: string, role: string = 'HOMEOWNER') => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    { userId, role },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
};

// Mock external services
export const mockStripeService = {
  createPaymentIntent: jest.fn<() => Promise<any>>().mockResolvedValue({
    id: 'pi_test_123',
    client_secret: 'seti_test_123_secret_test',
    status: 'requires_payment_method',
  }),
  confirmPaymentIntent: jest.fn<() => Promise<any>>().mockResolvedValue({
    id: 'pi_test_123',
    status: 'succeeded',
  }),
  createRefund: jest.fn<() => Promise<any>>().mockResolvedValue({
    id: 're_test_123',
    status: 'succeeded',
  }),
};

export const mockNotificationService = {
  sendPushNotification: jest.fn<(...args: any[]) => Promise<void>>().mockResolvedValue(undefined),
  registerPushToken: jest.fn<(...args: any[]) => Promise<void>>().mockResolvedValue(undefined),
  sendJobStatusNotification: jest.fn<(...args: any[]) => Promise<void>>().mockResolvedValue(undefined),
  sendNewMessageNotification: jest.fn<(...args: any[]) => Promise<void>>().mockResolvedValue(undefined),
};

import * as notificationService from '../services/notification.service';

export const mockEmailService = {
  sendEmail: jest.fn() as jest.Mock<(...args: any[]) => Promise<void>>,
};

jest.mock('../services/notification.service', () => ({
  NotificationService: {
    ...(jest.requireActual('../services/notification.service') as any).NotificationService,
    sendEmail: mockEmailService.sendEmail,
  },
}));


export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const expectToThrow = async (fn: () => Promise<any>, expectedError?: string) => {
  try {
    await fn();
    throw new Error('Expected function to throw');
  } catch (error: any) {
    if (expectedError && !error.message.includes(expectedError)) {
      throw new Error(`Expected error to contain "${expectedError}", got "${error.message}"`);
    }
  }
};
