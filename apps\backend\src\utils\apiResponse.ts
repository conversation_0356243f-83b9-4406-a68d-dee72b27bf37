import { Response } from 'express';

export interface ApiSuccessResponse<T = any> {
  success: true;
  message: string;
  data?: T;
  meta?: {
    timestamp: string;
    requestId?: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiErrorResponse {
  success: false;
  message: string;
  error?: {
    code: string;
    details?: string;
    field?: string;
    stack?: string; // Only in development
  };
  errors?: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  meta?: {
    timestamp: string;
    requestId?: string;
  };
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * Standardized success response helper
 */
export function sendSuccess<T = any>(
  res: Response,
  message: string,
  data?: T,
  statusCode: number = 200,
  meta?: ApiSuccessResponse<T>['meta']
): void {
  const response: ApiSuccessResponse<T> = {
    success: true,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta
    }
  };

  res.status(statusCode).json(response);
}

/**
 * Standardized error response helper
 */
export function sendError(
  res: Response,
  message: string,
  statusCode: number = 500,
  error?: {
    code?: string;
    details?: string;
    field?: string;
    stack?: string;
  },
  requestId?: string
): void {
  const response: ApiErrorResponse = {
    success: false,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      requestId
    }
  };

  if (error) {
    response.error = {
      code: error.code || 'INTERNAL_ERROR',
      details: error.details,
      field: error.field,
      // Only include stack trace in development
      ...(process.env.NODE_ENV === 'development' && error.stack && { stack: error.stack })
    };
  }

  res.status(statusCode).json(response);
}

/**
 * Standardized validation error response helper
 */
export function sendValidationError(
  res: Response,
  message: string = 'Validation failed',
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>,
  requestId?: string
): void {
  const response: ApiErrorResponse = {
    success: false,
    message,
    errors,
    meta: {
      timestamp: new Date().toISOString(),
      requestId
    }
  };

  res.status(400).json(response);
}

/**
 * Common error responses
 */
export const CommonErrors = {
  UNAUTHORIZED: (res: Response, message: string = 'Unauthorized access') => 
    sendError(res, message, 401, { code: 'UNAUTHORIZED' }),
    
  FORBIDDEN: (res: Response, message: string = 'Access forbidden') => 
    sendError(res, message, 403, { code: 'FORBIDDEN' }),
    
  NOT_FOUND: (res: Response, message: string = 'Resource not found') => 
    sendError(res, message, 404, { code: 'NOT_FOUND' }),
    
  CONFLICT: (res: Response, message: string = 'Resource conflict') => 
    sendError(res, message, 409, { code: 'CONFLICT' }),
    
  RATE_LIMITED: (res: Response, message: string = 'Rate limit exceeded') => 
    sendError(res, message, 429, { code: 'RATE_LIMITED' }),
    
  INTERNAL_ERROR: (res: Response, message: string = 'Internal server error') => 
    sendError(res, message, 500, { code: 'INTERNAL_ERROR' }),
    
  BAD_REQUEST: (res: Response, message: string = 'Bad request') => 
    sendError(res, message, 400, { code: 'BAD_REQUEST' }),
    
  PAYMENT_ERROR: (res: Response, message: string = 'Payment processing error') => 
    sendError(res, message, 402, { code: 'PAYMENT_ERROR' }),
    
  SERVICE_UNAVAILABLE: (res: Response, message: string = 'Service temporarily unavailable') => 
    sendError(res, message, 503, { code: 'SERVICE_UNAVAILABLE' })
};

/**
 * Success response shortcuts
 */
export const CommonSuccess = {
  OK: <T>(res: Response, message: string, data?: T) => 
    sendSuccess(res, message, data, 200),
    
  CREATED: <T>(res: Response, message: string, data?: T) => 
    sendSuccess(res, message, data, 201),
    
  ACCEPTED: <T>(res: Response, message: string, data?: T) => 
    sendSuccess(res, message, data, 202),
    
  NO_CONTENT: (res: Response, message: string = 'Operation completed successfully') => 
    sendSuccess(res, message, undefined, 204)
};

/**
 * Pagination helper
 */
export function createPaginationMeta(
  page: number,
  limit: number,
  total: number
): NonNullable<ApiSuccessResponse['meta']>['pagination'] {
  return {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit)
  };
}

/**
 * Express middleware to add request ID to responses
 */
export function addRequestId(req: any, res: Response, next: any) {
  req.requestId = req.headers['x-request-id'] || 
    `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  next();
}
