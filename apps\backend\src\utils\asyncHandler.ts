// src/utils/asyncHandler.ts

// 📁 File: src/utils/asyncHandler.ts

import type { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wraps an async route handler so errors are passed to Express error middleware
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
): RequestHandler => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
