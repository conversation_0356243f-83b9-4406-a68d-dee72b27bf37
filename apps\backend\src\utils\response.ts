// src/utils/response.ts

export const sendResponse = <T>(
    res: any,
    statusCode: number,
    message: string,
    data?: T
  ) => {
    return res.status(statusCode).json({
      success: true,
      message,
      data,
    });
  };
  
  export const sendError = (
    res: any,
    statusCode: number,
    message: string,
    error?: any
  ) => {
    return res.status(statusCode).json({
      success: false,
      message,
      error,
    });
  };
  