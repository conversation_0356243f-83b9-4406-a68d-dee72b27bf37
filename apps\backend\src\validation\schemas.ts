// CTRON Home - Comprehensive Zod Validation Schemas
// Provides input validation and sanitization for all API endpoints

import { z } from 'zod';

// ===== COMMON VALIDATION PATTERNS =====

// Email validation with proper regex
const emailSchema = z.string()
  .email('Invalid email format')
  .min(5, 'Email must be at least 5 characters')
  .max(254, 'Email must not exceed 254 characters')
  .toLowerCase()
  .trim();

// Password validation with security requirements
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must not exceed 128 characters')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');

// Phone number validation (international format)
const phoneSchema = z.string()
  .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
  .optional();

// UUID validation
const uuidSchema = z.string().uuid('Invalid UUID format');

// Sanitized text input (removes HTML tags and dangerous characters)
const createSanitizedTextSchema = (minLength?: number, maxLength?: number) => {
  let schema = z.string().trim();

  if (minLength !== undefined) {
    schema = schema.min(minLength, `Text must be at least ${minLength} characters`);
  }

  if (maxLength !== undefined) {
    schema = schema.max(maxLength, `Text must not exceed ${maxLength} characters`);
  }

  return schema
    .transform((val) => val.replace(/<[^>]*>/g, '')) // Remove HTML tags
    .transform((val) => val.replace(/[<>'"&]/g, '')) // Remove dangerous characters
    .refine((val) => val.length > 0, 'Text cannot be empty after sanitization');
};

// ===== USER AUTHENTICATION SCHEMAS =====

export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  fullName: createSanitizedTextSchema(2, 100),
  role: z.enum(['HOMEOWNER', 'TECHNICIAN'], {
    errorMap: () => ({ message: 'Role must be either HOMEOWNER or TECHNICIAN' })
  }),
  phone: phoneSchema,
  address: z.object({
    street: createSanitizedTextSchema(1, 200),
    city: createSanitizedTextSchema(1, 100),
    state: createSanitizedTextSchema(1, 100),
    zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
    country: createSanitizedTextSchema(1, 100).default('United States')
  }).optional()
});

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required').max(128, 'Password too long')
});

export const forgotPasswordSchema = z.object({
  email: emailSchema
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema
});

// ===== CHAT SCHEMAS =====

export const sendMessageSchema = z.object({
  content: createSanitizedTextSchema(1, 2000),
  type: z.enum(['TEXT', 'IMAGE', 'FILE'], {
    errorMap: () => ({ message: 'Message type must be TEXT, IMAGE, or FILE' })
  }).default('TEXT'),
  metadata: z.object({
    fileName: z.string().optional(),
    fileSize: z.number().positive().optional(),
    mimeType: z.string().optional()
  }).optional()
});

export const createChatSchema = z.object({
  jobId: uuidSchema,
  participants: z.array(uuidSchema).min(2, 'Chat must have at least 2 participants')
});

export const updateChatStatusSchema = z.object({
  status: z.enum(['ACTIVE', 'CLOSED', 'ARCHIVED'], {
    errorMap: () => ({ message: 'Status must be ACTIVE, CLOSED, or ARCHIVED' })
  })
});

// ===== JOB SCHEMAS =====

export const createJobSchema = z.object({
  title: createSanitizedTextSchema(5, 200),
  description: createSanitizedTextSchema(20, 5000),
  category: z.enum(['PLUMBING', 'ELECTRICAL', 'HVAC', 'GENERAL_REPAIR', 'MAINTENANCE'], {
    errorMap: () => ({ message: 'Invalid job category' })
  }),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT'], {
    errorMap: () => ({ message: 'Priority must be LOW, MEDIUM, HIGH, or URGENT' })
  }).default('MEDIUM'),
  budget: z.object({
    min: z.number().positive('Minimum budget must be positive'),
    max: z.number().positive('Maximum budget must be positive'),
    currency: z.string().length(3, 'Currency must be 3 characters').default('USD')
  }).refine((data) => data.max >= data.min, {
    message: 'Maximum budget must be greater than or equal to minimum budget'
  }),
  location: z.object({
    address: createSanitizedTextSchema(1, 500),
    latitude: z.number().min(-90).max(90, 'Invalid latitude'),
    longitude: z.number().min(-180).max(180, 'Invalid longitude'),
    accessInstructions: createSanitizedTextSchema(1, 1000).optional()
  }),
  scheduledDate: z.string().datetime('Invalid date format').optional(),
  images: z.array(z.string().url('Invalid image URL')).max(10, 'Maximum 10 images allowed').optional()
});

export const updateJobStatusSchema = z.object({
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'], {
    errorMap: () => ({ message: 'Invalid job status' })
  }),
  notes: createSanitizedTextSchema(1, 1000).optional()
});

// ===== PAYMENT SCHEMAS =====

export const createPaymentIntentSchema = z.object({
  jobId: uuidSchema,
  amount: z.number().positive('Amount must be positive').max(100000, 'Amount too large'),
  currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),
  description: createSanitizedTextSchema(1, 500).optional()
});

export const confirmPaymentSchema = z.object({
  paymentIntentId: z.string().min(1, 'Payment intent ID is required'),
  paymentMethodId: z.string().min(1, 'Payment method ID is required')
});

// ===== FILE UPLOAD SCHEMAS =====

export const fileUploadSchema = z.object({
  fileName: createSanitizedTextSchema(1, 255),
  fileSize: z.number().positive('File size must be positive').max(10485760, 'File size must not exceed 10MB'),
  mimeType: z.enum([
    'image/jpeg', 'image/png', 'image/webp', 'image/gif',
    'application/pdf', 'text/plain', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ], {
    errorMap: () => ({ message: 'Unsupported file type' })
  }),
  jobId: uuidSchema.optional()
});

// ===== QUERY PARAMETER SCHEMAS =====

export const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/, 'Page must be a number').transform(Number).refine(n => n > 0, 'Page must be positive').default('1'),
  limit: z.string().regex(/^\d+$/, 'Limit must be a number').transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100').default('20'),
  sortBy: z.string().max(50, 'Sort field too long').optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

export const searchSchema = z.object({
  query: createSanitizedTextSchema(1, 200),
  category: z.string().max(50, 'Category too long').optional(),
  location: z.string().max(200, 'Location too long').optional()
});

// ===== MIDDLEWARE VALIDATION HELPER =====

export type ValidationSchema = z.ZodSchema<any>;

export const validateRequest = (schema: ValidationSchema) => {
  return (req: any, res: any, next: any): void => {
    try {
      const validatedData = schema.parse(req.body);
      req.validatedData = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
        return;
      }
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  };
};

export const validateQuery = (schema: ValidationSchema) => {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.query);
      req.validatedQuery = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Query validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
};

export const validateParams = (schema: ValidationSchema) => {
  return (req: any, res: any, next: any): void => {
    try {
      const validatedData = schema.parse(req.params);
      req.validatedParams = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          error: 'Parameter validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
        return;
      }
      res.status(500).json({ error: 'Internal server error' });
      return;
    }
  };
};
