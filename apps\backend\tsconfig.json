{"compilerOptions": {"target": "ES2021", "module": "commonjs", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "outDir": "dist", "baseUrl": "./src", "resolveJsonModule": true, "types": ["node", "jest"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*.ts", "prisma/**/*.ts"], "exclude": ["node_modules", "dist", "src/tests/**/*", "**/*.test.ts", "**/*.spec.ts"]}