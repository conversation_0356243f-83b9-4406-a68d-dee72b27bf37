// mobile-app/App.tsx
import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/context/AuthContext';
import { JobProvider } from './src/context/JobContext';
import { ThemeProvider } from './src/context/ThemeContext';
import RootNavigator from './src/navigation/RootNavigator';
import { ErrorBoundary } from './src/components/ErrorBoundary';
import { validateAndDisplayEnvironment } from './src/utils/environmentValidator';
import { NotificationService } from './src/services/notification.service';

import { StripeProvider } from '@stripe/stripe-react-native';


// Development warning suppression
if (__DEV__) {
  // Ignore specific warnings that are known issues or not critical for development
  const originalWarn = console.warn;
  console.warn = (...args) => {
    const message = args.join(' ');
    const ignoredWarnings = [
      'Non-serializable values were found in the navigation state',
      'Require cycle:',
      'Constants.manifest.extra has been deprecated',
      'ViewPropTypes will be removed from React Native',
      'new NativeEventEmitter() was called with a non-null argument without the native module',
      'EventEmitter.removeListener(',
      'Warning: componentWillMount has been renamed',
      'Warning: componentWillReceiveProps has been renamed',
      'Warning: componentWillUpdate has been renamed',
    ];

    if (!ignoredWarnings.some(warning => message.includes(warning))) {
      originalWarn(...args);
    }
  };
}

// Validate environment variables on app startup
validateAndDisplayEnvironment();

export default function App() {


  useEffect(() => {
    const initializeApp = async () => {
      console.log('App.tsx: Application starting up...');
      // Setup notifications
      try {
        await NotificationService.getInstance().initialize();
        console.log('App.tsx: Notifications setup complete.');
      } catch (error) {
        console.error('App.tsx: Error setting up notifications:', error);
        // Decide how to handle notification setup failure (e.g., show a user message)
      }
    };

    initializeApp();
  }, []);

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <StripeProvider
          publishableKey={process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY || ''}
          merchantIdentifier="merchant.com.ctron.home"
          urlScheme="ctronhome"
        >
          <AuthProvider>
            <JobProvider>
              <ThemeProvider>
                <RootNavigator />
              </ThemeProvider>
              <StatusBar style="dark" />
            </JobProvider>
          </AuthProvider>
        </StripeProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}
