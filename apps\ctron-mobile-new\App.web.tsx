import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
// Web version doesn't need StatusBar or LogBox
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/context/AuthContext';
import { JobProvider } from './src/context/JobContext';
import { ThemeProvider } from './src/context/ThemeContext';
import RootNavigator from './src/navigation/RootNavigator';
import { ErrorBoundary } from './src/components/ErrorBoundary';
import { validateAndDisplayEnvironment } from './src/utils/environmentValidator';
import { NotificationService } from './src/services/notification.service';
import { StripeWebProvider } from './src/components/StripeWebProvider';

// Web-specific warning suppression
if (process.env.NODE_ENV === 'development') {
  // Ignore specific warnings that are known issues or not critical for development
  const originalWarn = console.warn;
  console.warn = (...args) => {
    const message = args.join(' ');
    const ignoredWarnings = [
      'Non-serializable values were found in the navigation state',
      'Require cycle:',
      'Constants.manifest.extra has been deprecated',
      'ViewPropTypes will be removed from React Native',
      'new NativeEventEmitter() was called with a non-null argument without the native module',
      'EventEmitter.removeListener(',
      'Warning: componentWillMount has been renamed',
      'Warning: componentWillReceiveProps has been renamed',
      'Warning: componentWillUpdate has been renamed',
    ];

    if (!ignoredWarnings.some(warning => message.includes(warning))) {
      originalWarn(...args);
    }
  };
}

// Validate environment variables on app startup
validateAndDisplayEnvironment();

export default function App() {
  console.log('🌐 App.web.tsx: Component rendering...');

  // Add error boundary and simple test
  try {
    console.log('🌐 App.web.tsx: Starting render process');
  } catch (error) {
    console.error('🔴 App.web.tsx: Error in component:', error);
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }}>
        <Text style={{ fontSize: 18, color: '#333' }}>Error loading app</Text>
      </View>
    );
  }

  useEffect(() => {
    const initializeApp = async () => {
      console.log('App.web.tsx: Application starting up...');
      // Setup notifications
      try {
        await NotificationService.getInstance().initialize();
        console.log('App.web.tsx: Notifications setup complete.');
      } catch (error) {
        console.error('App.web.tsx: Error setting up notifications:', error);
        // Decide how to handle notification setup failure (e.g., show a user message)
      }
    };

    initializeApp();
  }, []);

  // Debug mode confirmed React is working - now testing actual app

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <StripeWebProvider>
          <AuthProvider>
            <JobProvider>
              <ThemeProvider> {/* Wrap with ThemeProvider */}
                <RootNavigator />
                {/* StatusBar not needed on web */}
              </ThemeProvider>
            </JobProvider>
          </AuthProvider>
        </StripeWebProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}