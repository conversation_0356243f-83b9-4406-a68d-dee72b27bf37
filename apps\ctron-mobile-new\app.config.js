import 'dotenv/config';

export default ({ config }) => ({
  ...config,
  name: "CTRON Home",
  slug: "ctron-mobile",
  version: "1.0.0",
  orientation: "portrait",
  android: {
    package: "com.ctron.home",
    adaptiveIcon: {
      foregroundImage: "./assets/adaptive-icon.png",
      backgroundColor: "#FFFFFF"
    }
  },
  ios: {
    bundleIdentifier: "com.ctron.home",
    supportsTablet: true
  },

  extra: {
    API_BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL,
    STRIPE_PUBLIC_KEY: process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY,
    AWS_REGION: process.env.EXPO_PUBLIC_AWS_REGION,
    AWS_BUCKET_NAME: process.env.EXPO_PUBLIC_AWS_BUCKET_NAME,
    BACKEND_IP: process.env.EXPO_PUBLIC_DEV_SERVER_IP || 'localhost',
    BACKEND_PORT: process.env.EXPO_PUBLIC_DEV_SERVER_PORT || '3001',
    SOCKET_URL: process.env.EXPO_PUBLIC_SOCKET_URL,
    MERCHANT_IDENTIFIER: process.env.EXPO_PUBLIC_MERCHANT_IDENTIFIER,
    API_TIMEOUT: process.env.EXPO_PUBLIC_API_TIMEOUT,
    SOCKET_TIMEOUT: process.env.EXPO_PUBLIC_SOCKET_TIMEOUT,
    SOCKET_RECONNECT_ATTEMPTS: process.env.EXPO_PUBLIC_SOCKET_RECONNECT_ATTEMPTS,
    SOCKET_RECONNECT_DELAY: process.env.EXPO_PUBLIC_SOCKET_RECONNECT_DELAY,
    SOCKET_RECONNECT_DELAY_MAX: process.env.EXPO_PUBLIC_SOCKET_RECONNECT_DELAY_MAX,
    ENABLE_DEBUG: process.env.EXPO_PUBLIC_ENABLE_DEBUG === 'true',
    eas: {
      projectId: process.env.EXPO_PUBLIC_PROJECT_ID
    }
  },
  plugins: [
    "expo-notifications",
    "expo-location",
    "expo-image-picker",
    "expo-document-picker"
  ]
});