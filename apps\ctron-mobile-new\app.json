{"expo": {"name": "CTRON Home", "slug": "ctron-home", "scheme": "ctronhome", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "extra": {"APP_ENVIRONMENT": "development"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.anonymous.ctronhome"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-secure-store", "expo-font"], "updates": {"enabled": false, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}, "runtimeVersion": {"policy": "appVersion"}}}