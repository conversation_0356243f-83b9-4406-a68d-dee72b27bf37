module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      ['@babel/plugin-transform-runtime', {
          helpers: true,
          regenerator: true,
        },
      ],
      '@babel/plugin-transform-async-to-generator',
      [
        'module:react-native-dotenv',
        {
          moduleName: '@env',
          path: '.env',
          blocklist: null,
          allowlist: null,
          safe: false,
          allowUndefined: true,
        },
      ],
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@components': './src/components',
            '@screens': './src/screens',
            '@api': './src/api',
            '@utils': './src/utils',
          },
          extensions: [
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.android.js',
            '.android.tsx',
            '.ios.js',
            '.ios.tsx',
            '.web.js',
            '.web.tsx',
          ],
        },
      ],
    ],
    env: {
      web: {
        plugins: [
          [
            'module-resolver',
            {
              alias: {
                'react-native': 'react-native-web',
              },
            },
          ],
        ],
      },
    },
  };
};
