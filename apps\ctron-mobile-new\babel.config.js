module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Bundle optimization plugins
      ['@babel/plugin-transform-runtime', {
        helpers: true,
        regenerator: true,
        useESModules: true,
      }],
      // Tree shaking optimization for lodash and other libraries
      ['babel-plugin-transform-imports', {
        'date-fns': {
          transform: 'date-fns/${member}',
          preventFullImport: true,
        },
        'react-native-vector-icons': {
          transform: 'react-native-vector-icons/dist/${member}',
          preventFullImport: true,
        },
      }],
      // Required for Expo Router
      'expo-router/babel',
      // Reanimated plugin has to be listed last.
      'react-native-reanimated/plugin',
    ],
  };
};
