import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function DebugApp() {
  console.log('🐛 DebugApp rendering');
  
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Debug App - If you see this, React Native Web is working!</Text>
      <Text style={styles.text}>Platform: {require('react-native').Platform.OS}</Text>
      <Text style={styles.text}>Time: {new Date().toISOString()}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    padding: 20,
  },
  text: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
    color: '#333',
  },
});
