# CTRON Home - Accessibility Guide

## Overview

This guide documents the accessibility features implemented in the CTRON Home mobile application to ensure compliance with WCAG 2.1 AA standards and provide an inclusive experience for all users.

## Accessibility Framework

### 1. Accessibility Manager

**Location**: `src/utils/accessibility.ts`

Centralized accessibility management system:

```typescript
import { accessibilityManager, announce, isScreenReaderEnabled } from '@/utils/accessibility';

// Announce to screen readers
announce('Form submitted successfully', 'medium');

// Check accessibility features
const screenReaderEnabled = isScreenReaderEnabled();
const reduceMotionEnabled = isReduceMotionEnabled();
```

**Features**:
- Screen reader detection and support
- Reduce motion preference detection
- Announcement queue management
- Cross-platform accessibility props generation

### 2. Accessibility Hooks

**Location**: `src/hooks/useAccessibility.ts`

React hooks for component-level accessibility:

```typescript
// Basic accessibility hook
const { 
  announceText, 
  getButtonAccessibilityProps,
  shouldAnimate 
} = useAccessibility({
  announceOnMount: 'Screen loaded successfully'
});

// Form-specific accessibility
const { 
  announceFormError, 
  announceFormStatus,
  getFieldAccessibilityProps 
} = useFormAccessibility();

// Navigation accessibility
const { 
  announceScreenChange,
  focusScreenHeader 
} = useNavigationAccessibility();
```

## Component Accessibility

### 1. Enhanced Button Component

**Location**: `src/components/ui/Button.tsx`

Comprehensive accessibility implementation:

```typescript
<Button
  title="Submit Form"
  onPress={handleSubmit}
  accessibilityLabel="Submit the contact form"
  accessibilityHint="Sends your message to our support team"
  announceOnPress="Form submitted successfully"
  testID="submit-button"
/>
```

**Features**:
- Automatic accessibility props generation
- Loading state announcements
- Disabled state handling
- Custom announcement support
- Reduce motion animation support

### 2. Form Input Accessibility

**Location**: `src/screens/Auth/LoginScreen.tsx`

Enhanced form inputs with comprehensive accessibility:

```typescript
<TextInput
  {...getFieldAccessibilityProps({
    label: 'Email address',
    value: email,
    placeholder: 'Email',
    error: errors.email,
    required: true,
    fieldName: 'Email',
    testID: 'login-email-input',
  })}
/>
```

**Features**:
- Automatic error announcements
- Required field indication
- Field validation feedback
- Screen reader optimized labels

### 3. Card Component Accessibility

Enhanced card components for better navigation:

```typescript
const cardProps = createCardA11yProps({
  title: technician.name,
  subtitle: technician.specialization,
  description: `Rating: ${technician.rating} stars`,
  actionHint: 'Double tap to view technician details',
  testID: `technician-card-${technician.id}`,
});

<TouchableOpacity {...cardProps}>
  <TechnicianCard technician={technician} />
</TouchableOpacity>
```

## Screen Reader Support

### 1. Announcement System

**Queue Management**:
```typescript
// High priority announcements (errors)
announce('Form submission failed', 'high');

// Medium priority (status updates)
announce('Loading complete', 'medium');

// Low priority (navigation)
announce('Navigated to settings', 'low');
```

**Platform-Specific Implementation**:
- **iOS/Android**: Uses `AccessibilityInfo.announceForAccessibility()`
- **Web**: Uses ARIA live regions for announcements

### 2. Focus Management

```typescript
// Set focus to important elements
const headerRef = useRef();
const { setAccessibilityFocus } = useAccessibility();

useEffect(() => {
  // Focus header after navigation
  setAccessibilityFocus(headerRef);
}, []);
```

### 3. Screen Navigation Announcements

```typescript
const { announceScreenChange } = useNavigationAccessibility();

// Announce screen changes
useEffect(() => {
  announceScreenChange('Login Screen', 'Please enter your credentials');
}, []);
```

## Form Accessibility

### 1. Error Handling

**Automatic Error Announcements**:
```typescript
const { announceFormError, announceFormStatus } = useFormAccessibility();

// Announce validation errors
if (errors.email) {
  announceFormError('Email', errors.email);
}

// Announce form submission status
announceFormStatus('error', 'Please fix the form errors');
announceFormStatus('submitting');
announceFormStatus('success', 'Account created successfully');
```

### 2. Field Validation

**Enhanced Field Props**:
```typescript
const fieldProps = getFieldAccessibilityProps({
  label: 'Password',
  value: password,
  placeholder: 'Enter your password',
  error: errors.password,
  required: true,
  fieldName: 'Password',
});

// Automatically includes:
// - accessibilityLabel: "Password"
// - accessibilityHint: "Enter your password, Required field, Error: Password too short"
// - accessibilityState: { disabled: false }
```

### 3. Form Status Communication

```typescript
// Loading states
<Button
  loading={isSubmitting}
  accessibilityLabel="Submit form"
  // Automatically announces: "Loading, please wait"
/>

// Success/Error states with announcements
const handleSubmit = async () => {
  try {
    announceFormStatus('submitting');
    await submitForm();
    announceFormStatus('success');
  } catch (error) {
    announceFormStatus('error', error.message);
  }
};
```

## Motion and Animation

### 1. Reduce Motion Support

**Automatic Detection**:
```typescript
const { shouldAnimate, getAnimationDuration } = useAccessibility();

// Conditional animations
const animationConfig = {
  duration: getAnimationDuration(300, 0), // 300ms normal, 0ms reduced
  useNativeDriver: true,
};

if (shouldAnimate()) {
  Animated.timing(value, animationConfig).start();
}
```

### 2. Animation Alternatives

**Static Alternatives for Reduced Motion**:
```typescript
// Instead of slide animation, use instant transition
const transitionStyle = shouldAnimate() 
  ? { transform: [{ translateX: slideValue }] }
  : { opacity: isVisible ? 1 : 0 };
```

## Testing Accessibility

### 1. Automated Testing

**Test Utilities**:
```typescript
import { render, fireEvent } from '@testing-library/react-native';

test('button has correct accessibility attributes', () => {
  const { getByRole } = render(
    <Button 
      title="Submit" 
      accessibilityLabel="Submit form"
      testID="submit-button"
    />
  );
  
  const button = getByRole('button');
  expect(button).toHaveAccessibilityLabel('Submit form');
  expect(button).toHaveAccessibilityRole('button');
});
```

### 2. Manual Testing

**Screen Reader Testing**:
1. **iOS**: Enable VoiceOver in Settings > Accessibility
2. **Android**: Enable TalkBack in Settings > Accessibility
3. **Web**: Test with NVDA, JAWS, or VoiceOver

**Testing Checklist**:
- [ ] All interactive elements are focusable
- [ ] Focus order is logical
- [ ] All images have appropriate alt text
- [ ] Form errors are announced
- [ ] Loading states are communicated
- [ ] Navigation changes are announced

### 3. Accessibility Audit Tools

**Web Platform**:
- Lighthouse accessibility audit
- axe-core browser extension
- WAVE accessibility evaluation tool

**Mobile Platform**:
- iOS Accessibility Inspector
- Android Accessibility Scanner

## WCAG 2.1 AA Compliance

### 1. Perceivable

**Color and Contrast**:
```typescript
// Design system ensures WCAG AA contrast ratios
export const accessibility = {
  contrastRatio: {
    normal: 4.5,  // WCAG AA standard
    large: 3.0,   // WCAG AA for large text
  },
};
```

**Text Alternatives**:
- All images have meaningful alt text
- Icons include accessible labels
- Complex graphics have detailed descriptions

### 2. Operable

**Keyboard Navigation**:
- All functionality available via keyboard/screen reader
- Focus indicators are visible
- No keyboard traps

**Touch Targets**:
```typescript
// Minimum touch target size
export const accessibility = {
  minTouchTarget: 44, // 44px minimum (WCAG AAA)
};
```

### 3. Understandable

**Clear Language**:
- Error messages are descriptive
- Instructions are clear and concise
- Form labels are meaningful

**Predictable Navigation**:
- Consistent navigation patterns
- Clear page titles and headings
- Logical tab order

### 4. Robust

**Assistive Technology Support**:
- Proper semantic markup
- ARIA attributes where needed
- Cross-platform compatibility

## Best Practices

### 1. Component Development

```typescript
// Always include accessibility props
const MyComponent = ({ title, onPress, disabled }) => {
  const accessibilityProps = getButtonAccessibilityProps({
    label: title,
    disabled,
    testID: 'my-component',
  });

  return (
    <TouchableOpacity 
      onPress={onPress}
      disabled={disabled}
      {...accessibilityProps}
    >
      <Text>{title}</Text>
    </TouchableOpacity>
  );
};
```

### 2. Error Communication

```typescript
// Immediate error announcement
const handleError = (error) => {
  announce(error.message, 'high');
  setError(error.message);
};
```

### 3. Loading States

```typescript
// Clear loading communication
const handleSubmit = async () => {
  setLoading(true);
  announce('Submitting form, please wait', 'medium');
  
  try {
    await submitForm();
    announce('Form submitted successfully', 'medium');
  } catch (error) {
    announce(`Error: ${error.message}`, 'high');
  } finally {
    setLoading(false);
  }
};
```

## Troubleshooting

### Common Issues

1. **Announcements Not Working**
   - Check if screen reader is enabled
   - Verify announcement queue is not overloaded
   - Test on different platforms

2. **Focus Issues**
   - Ensure elements are focusable
   - Check focus order logic
   - Verify focus management after navigation

3. **Missing Labels**
   - All interactive elements need labels
   - Images need alt text
   - Form fields need proper labels

### Debugging Tools

```typescript
// Development accessibility debugging
if (__DEV__) {
  console.log('Screen reader enabled:', isScreenReaderEnabled());
  console.log('Reduce motion enabled:', isReduceMotionEnabled());
  
  // Log all announcements
  accessibilityManager.setDebugMode(true);
}
```

## Future Enhancements

### Planned Improvements

1. **Voice Control**: Enhanced voice navigation support
2. **High Contrast**: Automatic high contrast mode detection
3. **Text Scaling**: Dynamic text size support
4. **Cognitive Accessibility**: Simplified UI modes
5. **Multi-language**: RTL language support

## Conclusion

The accessibility implementation in CTRON Home ensures that all users, regardless of their abilities, can effectively use the application. Regular testing with real users and assistive technologies is essential for maintaining and improving accessibility standards.
