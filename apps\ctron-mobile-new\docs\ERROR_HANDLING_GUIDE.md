# CTRON Home - Error Handling Guide

## Overview

This guide documents the comprehensive error handling system implemented in the CTRON Home mobile application to provide robust error recovery and excellent user experience.

## Error Handling Architecture

### 1. Error Handler Utility

**Location**: `src/utils/errorHandler.ts`

Centralized error handling with automatic categorization:

```typescript
import { errorHandler, handleApiError, handleNetworkError } from '@/utils/errorHandler';

// Automatic error type detection
const appError = errorHandler.handleError(error, 'LoginScreen');

// Specific error handling
const apiError = handleApiError(error, 'UserAPI');
const networkError = handleNetworkError(error, 'AuthService');
```

**Features**:
- Automatic error categorization
- User-friendly error messages
- Retry mechanism support
- Context-aware error handling
- Toast and alert notifications

### 2. Error Recovery Component

**Location**: `src/components/ErrorRecovery.tsx`

Enhanced error recovery with retry functionality:

```typescript
<ErrorRecovery
  error={error}
  onRetry={handleRetry}
  onReset={handleReset}
  context="UserProfile"
  showDetails={__DEV__}
  customMessage="Failed to load user profile"
/>
```

**Features**:
- Automatic retry with exponential backoff
- Maximum retry limits
- Custom error messages
- Development error details
- Accessibility support

### 3. Error Boundary Enhancement

**Location**: `src/components/ErrorBoundary.tsx`

React Error Boundary with enhanced recovery:

```typescript
<ErrorBoundary fallback={<CustomErrorScreen />}>
  <MyComponent />
</ErrorBoundary>
```

**Features**:
- Automatic error logging
- Component tree recovery
- Custom fallback components
- Error reporting integration
- Development debugging tools

## Error Types and Handling

### 1. Network Errors

**Automatic Detection and Handling**:
```typescript
// Network error with retry callback
errorHandler.registerRetryCallback('UserAPI', () => {
  refetchUserData();
});

// Automatic retry suggestion
const networkError = handleNetworkError(error, 'UserAPI');
// Shows: "Network connection failed. Please check your internet connection and try again."
// With retry button that calls the registered callback
```

**Features**:
- Connection status detection
- Automatic retry mechanisms
- Offline mode handling
- Network quality adaptation

### 2. API Errors

**Status Code Handling**:
```typescript
// 401 Unauthorized
const authError = handleAuthError(error, 'LoginAPI');
// Automatically clears auth tokens and redirects to login

// 400 Bad Request
const validationError = handleValidationError(error, 'FormSubmission');
// Shows field-specific validation errors

// 500 Server Error
const serverError = handleApiError(error, 'DataFetch');
// Shows generic server error message with retry option
```

### 3. Validation Errors

**Form Validation Handling**:
```typescript
const { announceFormError } = useFormAccessibility();

try {
  await submitForm(data);
} catch (error) {
  const validationError = handleValidationError(error, 'ContactForm');
  
  // Automatic field error mapping
  if (validationError.details?.fields) {
    Object.entries(validationError.details.fields).forEach(([field, message]) => {
      setFieldError(field, message);
      announceFormError(field, message);
    });
  }
}
```

## Error Recovery Patterns

### 1. Retry Mechanisms

**Exponential Backoff**:
```typescript
const retryWithBackoff = async (operation, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

### 2. Fallback Strategies

**Data Fallbacks**:
```typescript
const fetchUserData = async () => {
  try {
    return await api.getUserData();
  } catch (error) {
    handleApiError(error, 'UserData');
    
    // Fallback to cached data
    const cachedData = await getCachedUserData();
    if (cachedData) {
      announce('Using offline data', 'medium');
      return cachedData;
    }
    
    // Final fallback to default data
    return getDefaultUserData();
  }
};
```

## Error Hook Usage

### 1. useErrorHandler Hook

**Location**: `src/hooks/useErrorHandler.ts`

Simplified error handling in components:

```typescript
const { 
  handleError, 
  withErrorHandling,
  handleApiError 
} = useErrorHandler({
  context: 'UserProfile',
  onRetry: refetchData,
  enableRetry: true
});

// Wrap async operations
const fetchData = withErrorHandling(async () => {
  const data = await api.getUserData();
  return data;
});

// Manual error handling
try {
  await submitForm();
} catch (error) {
  handleApiError(error);
}
```

### 2. Form Error Handling

**Enhanced Form Experience**:
```typescript
const { 
  announceFormError, 
  announceFormStatus,
  getFieldAccessibilityProps 
} = useFormAccessibility();

const handleSubmit = async () => {
  try {
    announceFormStatus('submitting');
    await submitForm();
    announceFormStatus('success');
  } catch (error) {
    announceFormStatus('error', error.message);
    handleValidationError(error);
  }
};
```

## Best Practices

### 1. Error Message Guidelines

**User-Friendly Messages**:
- Use plain language, avoid technical jargon
- Be specific about what went wrong
- Provide clear next steps
- Include helpful context when possible

### 2. Error Prevention

**Input Validation**:
```typescript
const validateInput = (value, rules) => {
  const errors = [];
  
  rules.forEach(rule => {
    if (!rule.validator(value)) {
      errors.push(rule.message);
    }
  });
  
  return errors;
};
```

### 3. Error Recovery Strategy

**Recovery Priority**:
1. **Automatic Recovery**: Silent retry for transient errors
2. **User-Initiated Recovery**: Retry buttons for user control
3. **Graceful Degradation**: Fallback to basic functionality
4. **Error Reporting**: Log for future improvement

## Testing Error Handling

### 1. Error Simulation

**Development Tools**:
```typescript
if (__DEV__) {
  // Simulate network errors
  window.simulateNetworkError = () => {
    throw new Error('Simulated network error');
  };
}
```

### 2. Error Boundary Testing

**Test Error Recovery**:
```typescript
test('error boundary shows error UI and allows retry', () => {
  const { getByText } = render(
    <ErrorBoundary>
      <ThrowError shouldThrow={true} />
    </ErrorBoundary>
  );
  
  expect(getByText(/something went wrong/i)).toBeTruthy();
  fireEvent.press(getByText(/try again/i));
});
```

## Conclusion

The comprehensive error handling system in CTRON Home ensures that users have a smooth experience even when things go wrong. The combination of automatic error detection, intelligent recovery mechanisms, and user-friendly error communication provides a robust foundation for application reliability.
