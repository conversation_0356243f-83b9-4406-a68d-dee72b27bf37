import 'react-native-gesture-handler';
import { registerRootComponent } from 'expo';
import { AppRegistry } from 'react-native';

import { Platform } from 'react-native';

// This is needed for react-native-web to resolve correctly
if (Platform.OS === 'web') {
  require('react-native-web');
}
import AppNative from './App.native';
import AppWeb from './App.web';

let App = AppNative;

if (Platform.OS === 'web') {
  App = AppWeb;
}
registerRootComponent(App);

// For web, we also need to run the application on the DOM
if (Platform.OS === 'web') {
  AppRegistry.runApplication('ctron-mobile-new', {
    initialProps: {},
    rootTag: document.getElementById('root'),
  });
}