import 'react-native-gesture-handler';
import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';
import AppNative from './App.native';
import AppWeb from './App.web';
import { setupWebPolyfills } from './src/utils/webPolyfills';

// Import polyfills first for web
if (Platform.OS === 'web') {
  // Ensure polyfills are loaded before anything else
  require('./src/polyfills/Platform-web.js');
  setupWebPolyfills();
}


// Select the appropriate app component
const App = Platform.OS === 'web' ? AppWeb : AppNative;

// Register the root component
registerRootComponent(App);