import 'react-native-gesture-handler';
import { registerRootComponent } from 'expo';
// AppRegistry not needed when using Expo registerRootComponent

import { Platform } from 'react-native';

// This is needed for react-native-web to resolve correctly
if (Platform.OS === 'web') {
  require('react-native-web');
}
import AppNative from './App.native';
import AppWeb from './App.web';

let App = AppNative;

if (Platform.OS === 'web') {
  App = AppWeb;
}
registerRootComponent(App);

// Expo registerRootComponent handles both native and web platforms