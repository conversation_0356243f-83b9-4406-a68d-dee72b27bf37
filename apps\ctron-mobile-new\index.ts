import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';

// Import gesture handler only for native platforms
if (Platform.OS !== 'web') {
  require('react-native-gesture-handler');
}

import AppNative from './App.native';
import AppWeb from './App.web';

// Load polyfills conditionally for web only
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  require('./polyfill-loader');
}

console.log('📱 Platform detected:', Platform.OS);

// Select the appropriate app component
const App = Platform.OS === 'web' ? AppWeb : AppNative;

console.log('🚀 Registering app component:', App.name || 'App');

// Register the root component
registerRootComponent(App);