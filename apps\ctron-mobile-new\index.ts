// Load polyfills first
import './polyfill-loader';

import 'react-native-gesture-handler';
import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';
import AppNative from './App.native';
import AppWeb from './App.web';

console.log('📱 Platform detected:', Platform.OS);

// Select the appropriate app component
const App = Platform.OS === 'web' ? AppWeb : AppNative;

console.log('🚀 Registering app component...');

// Register the root component
registerRootComponent(App);