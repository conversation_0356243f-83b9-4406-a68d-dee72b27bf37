// metro.config.js - SMART PLATFORM-AWARE CONFIGURATION
const { getDefaultConfig } = require('@expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Clean alias configuration
config.resolver.alias = {
  // Path shortcuts for cleaner imports
  '@components': './src/components',
  '@screens': './src/screens',
  '@api': './src/api',
  '@utils': './src/utils',
  '@assets': './src/assets',
  '@polyfills': './src/polyfills',

  // Essential React Native Web compatibility (WEB ONLY)
  'stream': 'stream-browserify',
  'react-native$': 'react-native-web', // Ensure react-native resolves to react-native-web for web platform
};

// Platform support configuration
config.resolver.platforms = ['native', 'web', 'ios', 'android'];
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Asset and source extension configuration
config.resolver.assetExts.push('png', 'jpg', 'jpeg', 'gif', 'svg');
config.resolver.sourceExts = config.resolver.sourceExts.filter(ext => !['png', 'jpg', 'jpeg', 'gif', 'svg'].includes(ext));

// SMART PLATFORM-AWARE RESOLVER - COMPREHENSIVE WEB POLYFILLS + NATIVE COMPATIBILITY
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // CRITICAL: Preserve native functionality for iOS and Android
  if (platform === 'ios' || platform === 'android' || platform === 'native') {
    return context.resolveRequest(context, moduleName, platform);
  }

  // COMPREHENSIVE WEB POLYFILLS - Only applied to web platform
  if (platform === 'web') {
    const polyfillsPath = path.resolve(__dirname, 'src/polyfills');
    
    // 1. PlatformColorValueTypes polyfill (ALL PATHS)
    if (moduleName === './PlatformColorValueTypes' ||
        moduleName.includes('PlatformColorValueTypes') ||
        moduleName.includes('StyleSheet/PlatformColorValueTypes')) {
      return {
        filePath: path.join(polyfillsPath, 'PlatformColorValueTypes.js'),
        type: 'sourceFile',
      };
    }

    // 2. Expo device polyfill for web
    if (moduleName === 'expo-device') {
      return {
        filePath: path.join(polyfillsPath, 'expo-device.web.ts'),
        type: 'sourceFile',
      };
    }

    // 3. AsyncStorage polyfill for web
    if (moduleName === '@react-native-async-storage/async-storage') {
      return {
        filePath: path.join(polyfillsPath, 'async-storage-web.js'),
        type: 'sourceFile',
      };
    }

    // 4. React Native Platform utilities polyfill for web
    if (moduleName.includes('Utilities/Platform') || 
        moduleName.includes('Platform/Platform') || 
        moduleName === './Platform') {
      return {
        filePath: path.join(polyfillsPath, 'react-native-platform-web.js'),
        type: 'sourceFile',
      };
    }

    // 5. DateTimePicker polyfill for web
    if (moduleName === '@react-native-community/datetimepicker') {
      return {
        filePath: path.join(polyfillsPath, 'datetimepicker-web.js'),
        type: 'sourceFile',
      };
    }

    // 6. BaseViewConfig polyfill for web
    if (moduleName === './BaseViewConfig' || moduleName.includes('BaseViewConfig')) {
      return {
        filePath: path.join(polyfillsPath, 'BaseViewConfig-web.js'),
        type: 'sourceFile',
      };
    }

    // 7. RCTAlertManager polyfill for web
    if (moduleName === './RCTAlertManager' || moduleName.includes('RCTAlertManager')) {
      return {
        filePath: path.join(polyfillsPath, 'RCTAlertManager-web.js'),
        type: 'sourceFile',
      };
    }

    // 8. RCTNetworking polyfill for web
    if (moduleName === './RCTNetworking' || moduleName.includes('RCTNetworking')) {
      return {
        filePath: path.join(polyfillsPath, 'RCTNetworking-web.js'),
        type: 'sourceFile',
      };
    }

    // 9. DevToolsSettingsManager polyfill for web
    if (moduleName.includes('DevToolsSettingsManager') || moduleName.includes('DevToolsSettings')) {
      return {
        filePath: path.join(polyfillsPath, 'DevToolsSettingsManager-web.js'),
        type: 'sourceFile',
      };
    }

    // 10. BackHandler polyfill for web
    if (moduleName.includes('BackHandler') || moduleName.includes('Utilities/BackHandler')) {
      return {
        filePath: path.join(polyfillsPath, 'BackHandler-web.js'),
        type: 'sourceFile',
      };
    }

    // 11. Image component polyfill for web
    if (moduleName.includes('Image/Image') || 
        moduleName === './Image' || 
        (moduleName.includes('/Image') && !moduleName.includes('ImageBackground'))) {
      return {
        filePath: path.join(polyfillsPath, 'Image-web.js'),
        type: 'sourceFile',
      };
    }

    // 12. React Native utilities polyfill for web
    if (moduleName.includes('Utilities/') && 
        !moduleName.includes('Platform') && 
        !moduleName.includes('BackHandler')) {
      return {
        filePath: path.join(polyfillsPath, 'ReactNativeUtilities-web.js'),
        type: 'sourceFile',
      };
    }

    // 13. LogBox and development utilities polyfill for web
    if (moduleName.includes('LogBoxImages')) {
      return {
        filePath: path.resolve(__dirname, 'assets/images/system/chevron-right.png'),
        type: 'sourceFile',
      };
    }
    
    if (moduleName.includes('LogBox') || 
        moduleName.includes('YellowBox') || 
        moduleName.includes('ExceptionsManager') || 
        moduleName.includes('HMRClient')) {
      return {
        filePath: path.join(polyfillsPath, 'LogBox-web.js'),
        type: 'sourceFile',
      };
    }

    // 14. ConsoleErrorReporter polyfill for web
    if (moduleName.includes('ConsoleErrorReporter') || 
        moduleName.includes('installConsoleErrorReporter') || 
        moduleName.includes('ErrorReporter')) {
      return {
        filePath: path.join(polyfillsPath, 'ConsoleErrorReporter-web.js'),
        type: 'sourceFile',
      };
    }

    // 15. AccessibilityInfo polyfill for web
    if (moduleName.includes('AccessibilityInfo') || 
        moduleName.includes('legacySendAccessibilityEvent')) {
      return {
        filePath: path.join(polyfillsPath, 'AccessibilityInfo-web.js'),
        type: 'sourceFile',
      };
    }

    // 16. Handle asset registry path with proper implementation
    if (moduleName === 'missing-asset-registry-path') {
      return context.resolveRequest(context, 'react-native/Libraries/Image/AssetRegistry', platform);
    }
  }

  // Fall back to default resolver for all other cases
  return context.resolveRequest(context, moduleName, platform);
};

// Hermes/Bridgeless configuration
config.transformer = {
  ...config.transformer,
  // Temporary Hermes/Bridgeless workaround
  minifierConfig: {
    ...config.transformer.minifierConfig,
    compress: {
      ...config.transformer.minifierConfig?.compress,
      keep_fnames: true, // Preserve function names
      reduce_vars: false // Disable variable reduction
    }
  },
  unstable_allowRequireContext: true,

  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Enhanced resolver configuration
config.resolver = {
  ...config.resolver,
  alias: config.resolver.alias, // Ensure alias is preserved
  platforms: config.resolver.platforms,
  resolverMainFields: config.resolver.resolverMainFields,
  resolveRequest: config.resolver.resolveRequest,
  unstable_enablePackageExports: false,
  extraNodeModules: {
    ...config.resolver.extraNodeModules,
    'react-native': require.resolve('react-native-web'),
    'stream': require.resolve('stream-browserify'),
  }
};

module.exports = config;