// metro.config.js - Simplified and stable configuration
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Clean alias configuration
config.resolver.alias = {
  '@': './src',
  '@components': './src/components',
  '@screens': './src/screens',
  '@api': './src/api',
  '@utils': './src/utils',
  '@assets': './src/assets',
  '@context': './src/context',
  '@hooks': './src/hooks',
  '@navigation': './src/navigation',
  '@services': './src/services',
  '@types': './src/types',
};

// Add web-specific configuration
config.resolver.alias['react-native$'] = 'react-native-web';


// Polyfills for Node.js core modules
config.resolver.extraNodeModules = {
  "stream": require.resolve('stream-browserify'),
  "crypto": require.resolve('crypto-browserify'),
  "process": require.resolve('process/browser'),
};

module.exports = config;