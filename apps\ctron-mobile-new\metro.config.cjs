// metro.config.js - Simplified and stable configuration
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Clean alias configuration
config.resolver.alias = {
  '@': './src',
  '@components': './src/components',
  '@screens': './src/screens',
  '@api': './src/api',
  '@utils': './src/utils',
  '@assets': './src/assets',
  '@context': './src/context',
  '@hooks': './src/hooks',
  '@navigation': './src/navigation',
  '@services': './src/services',
  '@types': './src/types',
};

// Only add web-specific configuration if you're actually targeting web
if (process.env.EXPO_PUBLIC_PLATFORM === 'web') {
  // Add minimal web polyfills only if needed
  config.resolver.alias['react-native$'] = 'react-native-web';
}


// Polyfills for Node.js core modules
config.resolver.extraNodeModules = {
  "stream": require.resolve('stream-browserify'),
  "crypto": require.resolve('crypto-browserify'),
  "process": require.resolve('process/browser'),
};

module.exports = config;