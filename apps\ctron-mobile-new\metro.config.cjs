// metro.config.js - Web compatibility configuration
const { getDefaultConfig } = require('@expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Comprehensive alias configuration for React Native Web
config.resolver.alias = {
  'react-native$': 'react-native-web',
};

// Custom resolver to handle React Native internal imports
const originalResolveRequest = config.resolver.resolveRequest;
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Handle React Native internal Platform utilities
  if (moduleName.includes('Utilities/Platform') || moduleName.endsWith('/Platform')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/Platform-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle PlatformColorValueTypes
  if (moduleName.includes('PlatformColorValueTypes') || moduleName.endsWith('/PlatformColorValueTypes')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/PlatformColorValueTypes.js'),
      type: 'sourceFile',
    };
  }

  // Handle BaseViewConfig
  if (moduleName.includes('BaseViewConfig') || moduleName.endsWith('/BaseViewConfig')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/BaseViewConfig-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle AccessibilityInfo and related modules
  if (moduleName.includes('AccessibilityInfo') || moduleName.includes('legacySendAccessibilityEvent')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/AccessibilityInfo-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle RCTNetworking
  if (moduleName.includes('RCTNetworking') || moduleName.endsWith('/RCTNetworking')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/RCTNetworking-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle RCTAlertManager
  if (moduleName.includes('RCTAlertManager') || moduleName.endsWith('/RCTAlertManager')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/RCTAlertManager-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle DevToolsSettingsManager
  if (moduleName.includes('DevToolsSettingsManager') || moduleName.includes('DevToolsSettings')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/DevToolsSettingsManager-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle BackHandler
  if (moduleName.includes('BackHandler') || moduleName.endsWith('/BackHandler')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/BackHandler-web.js'),
      type: 'sourceFile',
    };
  }

  // Handle Image
  if (moduleName.includes('Image/Image') || moduleName.endsWith('/Image')) {
    return {
      filePath: path.resolve(__dirname, 'src/polyfills/Image-web.js'),
      type: 'sourceFile',
    };
  }

  // Use default resolver for everything else
  if (originalResolveRequest) {
    return originalResolveRequest(context, moduleName, platform);
  }

  return context.resolveRequest(context, moduleName, platform);
};

// Ensure proper platform resolution
config.resolver.platforms = ['web', 'native', 'ios', 'android'];

module.exports = config;