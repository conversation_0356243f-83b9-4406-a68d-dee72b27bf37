{"name": "ctron-mobile-new", "type": "module", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/metro-runtime": "~3.2.3", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@stripe/stripe-js": "^7.4.0", "@stripe/stripe-react-native": "0.37.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "axios": "^1.7.7", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "expo": "~51.0.36", "expo-crypto": "~13.0.2", "expo-device": "~6.0.2", "expo-document-picker": "~12.0.2", "expo-image-picker": "~15.0.7", "expo-linking": "~6.3.1", "expo-location": "~17.0.1", "expo-notifications": "~0.28.16", "expo-secure-store": "~13.0.2", "expo-status-bar": "~1.12.1", "jwt-decode": "^4.0.0", "process": "^0.11.10", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.10", "socket.io-client": "^4.8.1", "stream-browserify": "^3.0.0", "typescript-eslint": "^8.35.0", "uuid": "^10.0.0", "expo-task-manager": "~11.8.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/metro-config": "^0.18.11", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.12", "@types/node": "^20.14.8", "@types/react": "~18.2.79", "@types/react-dom": "^19.1.6", "@types/react-native": "^0.72.8", "@types/uuid": "^10.0.0", "babel-plugin-dotenv-import": "^2.2.0", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-react-native": "2.1.0", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-expo": "~7.1.2", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.30.0", "eslint-plugin-react-hooks": "^4.6.2", "react-native-dotenv": "^3.4.11", "typescript": "~5.3.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["jwt-decode", "react-native-vector-icons", "socket.io-client", "uuid"]}}}}