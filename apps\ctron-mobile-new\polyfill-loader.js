// polyfill-loader.js
// Load polyfills before anything else

console.log('🔧 POLYFILL LOADER: Starting...');

// Check if we're in a web environment
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  console.log('🌐 POLYFILL LOADER: Web environment detected, loading polyfills...');
  
  // Load Platform polyfill
  try {
    require('./src/polyfills/Platform-web.js');
    console.log('🌐 POLYFILL LOADER: Platform polyfill loaded');
  } catch (error) {
    console.error('🔴 POLYFILL LOADER: Failed to load Platform polyfill:', error);
  }
} else {
  console.log('📱 POLYFILL LOADER: Native environment detected, skipping web polyfills');
}

console.log('🔧 POLYFILL LOADER: Complete');
