// polyfill-loader.js
// Load polyfills before anything else

console.log('🔧 POLYFILL LOADER: Starting...');

// Check if we're in a web environment (more strict check)
const isWeb = typeof window !== 'undefined' &&
  typeof document !== 'undefined' &&
  typeof navigator !== 'undefined' &&
  navigator.product !== 'ReactNative';

if (isWeb) {
  console.log('🌐 POLYFILL LOADER: Web environment detected, loading polyfills...');

  // Load Platform polyfill only for web
  try {
    require('./src/polyfills/Platform-web.js');
    console.log('🌐 POLYFILL LOADER: Platform polyfill loaded');
  } catch (error) {
    console.error('🔴 POLYFILL LOADER: Failed to load Platform polyfill:', error);
  }
} else {
  console.log('📱 POLYFILL LOADER: Native environment detected, skipping web polyfills');
}

console.log('🔧 POLYFILL LOADER: Complete');
