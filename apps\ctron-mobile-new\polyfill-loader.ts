// Web polyfills for React Native compatibility
console.log('🔧 Loading web polyfills...');

// Polyfill for React Native's Platform
if (typeof window !== 'undefined') {
  // Ensure Platform.OS is set to 'web'
  if (!global.Platform) {
    global.Platform = { OS: 'web' };
  }

  // Polyfill for native modules that don't exist on web
  if (!global.__fbBatchedBridgeConfig) {
    global.__fbBatchedBridgeConfig = {};
  }

  // Mock native event emitter
  if (!global.NativeEventEmitter) {
    global.NativeEventEmitter = class MockNativeEventEmitter {
      addListener() { return { remove: () => {} }; }
      removeListener() {}
      removeAllListeners() {}
    };
  }

  // Mock native modules that might be accessed
  if (!global.NativeModules) {
    global.NativeModules = {};
  }

  console.log('✅ Web polyfills loaded successfully');
}
