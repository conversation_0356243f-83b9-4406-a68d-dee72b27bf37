declare module 'react-native' {
  import { ComponentType } from 'react';
  
  // Base components
  export const View: ComponentType<any>;
  export const Text: ComponentType<any>;
  export const ScrollView: ComponentType<any>;
  export const TouchableOpacity: ComponentType<any>;
  export const ActivityIndicator: ComponentType<any>;
  export const FlatList: ComponentType<any> & {
    scrollToEnd: (params?: { animated?: boolean }) => void;
  };
  export const TextInput: ComponentType<any>;
  export const KeyboardAvoidingView: ComponentType<any>;
  export const Image: ComponentType<any>;

  // Utilities
  export const Dimensions: {
    get: (dim: string) => { width: number; height: number };
  };
  
  export const Platform: {
    OS: 'ios' | 'android' | 'windows' | 'macos' | 'web';
    Version: number | string;
  };

  export const Alert: {
    alert: (title: string, message?: string, buttons?: any[]) => void;
  };

  export const StyleSheet: {
    create: <T extends {}>(styles: T) => T;
    hairlineWidth: number;
    absoluteFill: object;
  };
}