import axios from 'axios';

// ✅ Use centralized configuration
import { API_BASE_URL } from '../config/api.config';

interface SignupPayload {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  role: 'HOMEOWNER' | 'TECHNICIAN';
}

interface LoginPayload {
  email: string;
  password: string;
}

interface AuthResponse {
  token: string;
}

const API_URL = API_BASE_URL;

// 🔍 Enhanced debug logging for development
if (__DEV__) {
  console.log('🔧 AuthAPI Configuration:');
  console.log('API_BASE_URL:', API_BASE_URL);
  console.log('API_URL:', API_URL);
  console.log('Environment variables:');
  console.log('EXPO_PUBLIC_API_BASE_URL:', process.env.EXPO_PUBLIC_API_BASE_URL);
  console.log('EXPO_PUBLIC_API_URL:', process.env.EXPO_PUBLIC_API_URL);
  console.log('EXPO_PUBLIC_SOCKET_URL:', process.env.EXPO_PUBLIC_SOCKET_URL);
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('__DEV__:', __DEV__);
}

const AuthAPI = {
  signup: async (payload: SignupPayload): Promise<AuthResponse> => {
    const url = `${API_URL}/api/auth/signup`;

    if (__DEV__) {
      console.log('🔐 Attempting signup with:', { email: payload.email, role: payload.role });
      console.log('📡 Signup URL:', url);
      console.log('📦 Signup payload:', { ...payload, password: '[HIDDEN]' });
    }

    try {
      const response = await axios.post(url, payload);

      if (__DEV__) {
        console.log('✅ Signup successful:', response.status);
        // Don't log response data in development to prevent sensitive data exposure
      }

      return response.data;
    } catch (error: any) {
      if (__DEV__) {
        console.error('❌ Signup failed:', error.message);
        console.error('📄 Error details:', error.response?.data || error);
        console.error('🌐 Network error:', error.code);
      }
      throw error;
    }
  },

  login: async (payload: LoginPayload): Promise<AuthResponse> => {
    const url = `${API_URL}/api/auth/login`;

    if (__DEV__) {
      console.log('🔐 Attempting login with:', payload.email);
      console.log('📡 Login URL:', url);
      console.log('📦 Login payload:', { ...payload, password: '[HIDDEN]' });
    }

    try {
      const response = await axios.post(url, payload);

      if (__DEV__) {
        console.log('✅ Login successful:', response.status);
        // Don't log response data in development to prevent sensitive data exposure
      }

      return response.data;
    } catch (error: any) {
      if (__DEV__) {
        console.error('❌ Login failed:', error.message);
        console.error('📄 Error details:', error.response?.data || error);
        console.error('🌐 Network error:', error.code);
        console.error('🔗 Request URL:', url);
      }
      throw error;
    }
  },
};

export default AuthAPI;
