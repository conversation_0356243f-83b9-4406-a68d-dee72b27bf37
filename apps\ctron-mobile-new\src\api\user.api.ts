// src/api/user.api.ts
import axios from 'axios';
import { getAuthToken } from '../utils/auth.utils';
import { API_BASE_URL } from '../config/api.config';

export const UserAPI = {
  /** save Expo push token for current user */
  savePushToken: async (token: string): Promise<void> => {
    const authToken = await getAuthToken();
    await axios.patch(`${API_BASE_URL}/api/users/push-token`,
      { pushToken: token },
      {
        headers: {
          'Content-Type': 'application/json',
          ...(authToken && { Authorization: `Bearer ${authToken}` }),
        },
      }
    );
  },
};
