// Centralized image asset management

import { Asset } from 'expo-asset';

import logoImage from '../../../assets/images/logo.png';
import splashImage from '../../../assets/images/splash.png';
import technicianDefaultImage from '../../../assets/images/technician-default.png';

const logo = Asset.fromModule(logoImage);
const splash = Asset.fromModule(splashImage);
const technicianDefault = Asset.fromModule(technicianDefaultImage);

export const AppImages = {
  logo: logo.uri,
  splash: splash.uri,
  technicianDefault: technicianDefault.uri,
};