// apps/mobile/src/components/Chat/TypingIndicator.tsx

import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet } from 'react-native';
// Note: Animated removed - using simple implementation for now
import { designSystem } from '../../styles/designSystem';

const { colors, spacing, typography, borderRadius } = designSystem;

interface TypingIndicatorProps {
  users: string[];
  maxUsersToShow?: number;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  users,
  maxUsersToShow = 3,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (users.length > 0) {
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();

      // Animated dots
      const animateDots = () => {
        const dotAnimation = (dotAnim: Animated.Value, delay: number) =>
          Animated.loop(
            Animated.sequence([
              Animated.delay(delay),
              Animated.timing(dotAnim, {
                toValue: 1,
                duration: 400,
                useNativeDriver: true,
              }),
              Animated.timing(dotAnim, {
                toValue: 0,
                duration: 400,
                useNativeDriver: true,
              }),
            ])
          );

        Animated.parallel([
          dotAnimation(dot1Anim, 0),
          dotAnimation(dot2Anim, 200),
          dotAnimation(dot3Anim, 400),
        ]).start();
      };

      animateDots();
    } else {
      // Fade out animation
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }

    return () => {
      dot1Anim.stopAnimation();
      dot2Anim.stopAnimation();
      dot3Anim.stopAnimation();
    };
  }, [users.length, dot1Anim, dot2Anim, dot3Anim, fadeAnim]);

  if (users.length === 0) {
    return null;
  }

  const getTypingText = () => {
    const displayUsers = users.slice(0, maxUsersToShow);
    const remainingCount = users.length - maxUsersToShow;

    if (users.length === 1) {
      return `${displayUsers[0]} is typing`;
    } else if (users.length === 2) {
      return `${displayUsers[0]} and ${displayUsers[1]} are typing`;
    } else if (users.length <= maxUsersToShow) {
      const lastUser = displayUsers.pop();
      return `${displayUsers.join(', ')} and ${lastUser} are typing`;
    } else {
      return `${displayUsers.join(', ')} and ${remainingCount} other${remainingCount > 1 ? 's' : ''
        } are typing`;
    }
  };

  const AnimatedDot: React.FC<{ animValue: Animated.Value }> = ({ animValue }) => (
    <Animated.View
      style={[
        styles.dot,
        {
          opacity: animValue,
          transform: [
            {
              scale: animValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.8, 1.2],
              }),
            },
          ],
        },
      ]}
    />
  );

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.bubble}>
        <View style={styles.content}>
          <Text style={styles.typingText}>{getTypingText()}</Text>
          <View style={styles.dotsContainer}>
            <AnimatedDot animValue={dot1Anim} />
            <AnimatedDot animValue={dot2Anim} />
            <AnimatedDot animValue={dot3Anim} />
          </View>
        </View>
      </View>

      {/* Bubble tail */}
      <View style={styles.tail} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-start',
    marginHorizontal: spacing.md,
    marginVertical: spacing.xs,
    maxWidth: '80%',
  },
  bubble: {
    backgroundColor: colors.secondarySystemBackground,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginLeft: spacing.sm,
    borderWidth: 1,
    borderColor: colors.separator,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  typingText: {
    ...typography.caption1,
    color: colors.secondaryLabel,
    fontStyle: 'italic',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: colors.secondaryLabel,
  },
  tail: {
    position: 'absolute',
    left: -spacing.xs,
    bottom: spacing.sm,
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderLeftWidth: 0,
    borderRightWidth: spacing.sm,
    borderTopWidth: spacing.sm,
    borderBottomWidth: 0,
    borderLeftColor: 'transparent',
    borderRightColor: colors.secondarySystemBackground,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
  },
});
