// apps/mobile/src/components/Chat/__tests__/ChatScreen.test.tsx

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { AuthProvider } from '../../../context/AuthContext';
import ChatScreen from '../../../screens/Chat/ChatScreen';
import { ChatAPI } from '../../../api/chat.api';
import { SocketService } from '../../../services/socket.service';

// Mock dependencies
jest.mock('../../../api/chat.api');
jest.mock('../../../services/socket.service');
jest.mock('../../../context/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    user: {
      userId: 'test-user-id',
      name: 'Test User',
      role: 'HOMEOWNER',
    },
  }),
}));

const mockChatAPI = ChatAPI as jest.Mocked<typeof ChatAPI>;
const mockSocketService = SocketService as jest.Mocked<typeof SocketService>;

// Mock navigation
const mockNavigation = {
  goBack: jest.fn(),
  navigate: jest.fn(),
};

const mockRoute = {
  params: {
    chatId: 'test-chat-id',
    jobTitle: 'Test Job',
  },
};

// Test data
const mockMessages = [
  {
    id: 'msg-1',
    content: 'Hello, I need help with plumbing',
    senderId: 'test-user-id',
    chatId: 'test-chat-id',
    createdAt: '2024-01-01T10:00:00Z',
    sender: {
      id: 'test-user-id',
      fullName: 'Test User',
      role: 'HOMEOWNER' as const,
    },
    readBy: [],
    attachments: [],
  },
  {
    id: 'msg-2',
    content: 'I can help you with that',
    senderId: 'technician-id',
    chatId: 'test-chat-id',
    createdAt: '2024-01-01T10:05:00Z',
    sender: {
      id: 'technician-id',
      fullName: 'John Technician',
      role: 'TECHNICIAN' as const,
    },
    readBy: [],
    attachments: [],
  },
];

const renderChatScreen = () => {
  return render(
    <NavigationContainer>
      <AuthProvider>
        <ChatScreen />
      </AuthProvider>
    </NavigationContainer>
  );
};

describe('ChatScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock navigation
    jest.doMock('@react-navigation/native', () => ({
      useNavigation: () => mockNavigation,
      useRoute: () => mockRoute,
    }));

    // Setup default API mocks
    mockChatAPI.getChatMessages.mockResolvedValue({
      messages: mockMessages,
      pagination: {
        page: 1,
        limit: 50,
        total: mockMessages.length,
        totalPages: 1,
      },
    });
    mockChatAPI.markMessagesRead.mockResolvedValue({ success: true });

    // Setup socket mocks
    mockSocketService.on.mockImplementation(() => { });
    mockSocketService.off.mockImplementation(() => { });
    mockSocketService.emit.mockImplementation(() => { });
  });

  describe('Rendering', () => {
    it('should render chat screen with messages', async () => {
      const { getByText, getByPlaceholderText } = renderChatScreen();

      await waitFor(() => {
        expect(getByText('Test Job')).toBeTruthy();
        expect(getByText('Hello, I need help with plumbing')).toBeTruthy();
        expect(getByText('I can help you with that')).toBeTruthy();
        expect(getByPlaceholderText('Type a message...')).toBeTruthy();
      });
    });

    it('should show loading state initially', () => {
      mockChatAPI.getChatMessages.mockImplementation(
        () => new Promise(() => { }) // Never resolves
      );

      const { getByText } = renderChatScreen();
      expect(getByText('Loading...')).toBeTruthy();
    });

    it('should display sender names for other users messages', async () => {
      const { getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByText('John Technician')).toBeTruthy();
      });
    });
  });

  describe('Message Sending', () => {
    it('should send message when send button is pressed', async () => {
      const { getByPlaceholderText, getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByPlaceholderText('Type a message...')).toBeTruthy();
      });

      const textInput = getByPlaceholderText('Type a message...');
      const sendButton = getByText('Send');

      // Type message
      fireEvent.changeText(textInput, 'New test message');

      // Send message
      fireEvent.press(sendButton);

      await waitFor(() => {
        expect(mockSocketService.emit).toHaveBeenCalledWith('chat:sendMessage', {
          chatId: 'test-chat-id',
          content: 'New test message',
          attachments: [],
        });
      });
    });

    it('should not send empty messages', async () => {
      const { getByPlaceholderText, getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByPlaceholderText('Type a message...')).toBeTruthy();
      });

      const sendButton = getByText('Send');

      // Try to send without typing
      fireEvent.press(sendButton);

      expect(mockSocketService.emit).not.toHaveBeenCalledWith(
        'chat:sendMessage',
        expect.any(Object)
      );
    });

    it('should clear input after sending message', async () => {
      const { getByPlaceholderText, getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByPlaceholderText('Type a message...')).toBeTruthy();
      });

      const textInput = getByPlaceholderText('Type a message...');
      const sendButton = getByText('Send');

      // Type and send message
      fireEvent.changeText(textInput, 'Test message');
      fireEvent.press(sendButton);

      await waitFor(() => {
        expect(textInput.props.value).toBe('');
      });
    });
  });

  describe('Real-time Features', () => {
    it('should setup socket listeners on mount', async () => {
      renderChatScreen();

      await waitFor(() => {
        expect(mockSocketService.on).toHaveBeenCalledWith('chat:newMessage', expect.any(Function));
        expect(mockSocketService.on).toHaveBeenCalledWith('chat:userTyping', expect.any(Function));
        expect(mockSocketService.on).toHaveBeenCalledWith('chat:messageRead', expect.any(Function));
        expect(mockSocketService.on).toHaveBeenCalledWith('chat:onlineUsers', expect.any(Function));
      });
    });

    it('should join chat room on mount', async () => {
      renderChatScreen();

      await waitFor(() => {
        expect(mockSocketService.emit).toHaveBeenCalledWith('chat:join', {
          chatId: 'test-chat-id',
        });
      });
    });

    it('should send typing indicator when typing', async () => {
      const { getByPlaceholderText } = renderChatScreen();

      await waitFor(() => {
        expect(getByPlaceholderText('Type a message...')).toBeTruthy();
      });

      const textInput = getByPlaceholderText('Type a message...');

      // Start typing
      fireEvent.changeText(textInput, 'T');

      await waitFor(() => {
        expect(mockSocketService.emit).toHaveBeenCalledWith('chat:typing', {
          chatId: 'test-chat-id',
          isTyping: true,
        });
      });
    });

    it('should handle new message from socket', async () => {
      let newMessageHandler: (payload: { message: any; chatId: string }) => void;

      mockSocketService.on.mockImplementation((event, handler) => {
        if (event === 'chat:newMessage') {
          newMessageHandler = handler as any;
        }
      });

      const { getByText } = renderChatScreen();

      await waitFor(() => {
        expect(mockSocketService.on).toHaveBeenCalled();
      });

      // Simulate receiving new message
      const newMessage = {
        message: {
          id: 'msg-3',
          content: 'New real-time message',
          senderId: 'technician-id',
          chatId: 'test-chat-id',
          createdAt: '2024-01-01T10:10:00Z',
          sender: {
            id: 'technician-id',
            fullName: 'John Technician',
            role: 'TECHNICIAN' as const,
          },
          readBy: [],
        },
        chatId: 'test-chat-id',
      };

      act(() => {
        newMessageHandler(newMessage);
      });

      await waitFor(() => {
        expect(getByText('New real-time message')).toBeTruthy();
      });
    });
  });

  describe('Message Display', () => {
    it('should show read status for own messages', async () => {
      const messagesWithRead = [
        {
          ...mockMessages[0],
          readBy: [{ userId: 'technician-id', readAt: '2024-01-01T10:01:00Z' }],
          attachments: [],
        },
      ];

      mockChatAPI.getChatMessages.mockResolvedValue({
        messages: messagesWithRead as any,
        pagination: {
          page: 1,
          limit: 50,
          total: messagesWithRead.length,
          totalPages: 1,
        },
      });

      const { getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByText('✓✓')).toBeTruthy(); // Read indicator
      });
    });

    it('should format message timestamps correctly', async () => {
      const { getByText } = renderChatScreen();

      await waitFor(() => {
        // Should show time in HH:MM format
        expect(getByText(/\d{1,2}:\d{2}/)).toBeTruthy();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockChatAPI.getChatMessages.mockRejectedValue(new Error('Network error'));

      const { getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByText(/Error/)).toBeTruthy();
      });
    });

    it('should handle socket connection errors', async () => {
      mockSocketService.emit.mockImplementation(() => {
        throw new Error('Socket error');
      });

      const { getByPlaceholderText, getByText } = renderChatScreen();

      await waitFor(() => {
        expect(getByPlaceholderText('Type a message...')).toBeTruthy();
      });

      const textInput = getByPlaceholderText('Type a message...');
      const sendButton = getByText('Send');

      fireEvent.changeText(textInput, 'Test message');
      fireEvent.press(sendButton);

      // Should not crash the app
      await waitFor(() => {
        expect(getByText('Send')).toBeTruthy();
      });
    });
  });
});
