// Debug overlay to help identify blank screen issues

import { View, Text, StyleSheet, Platform } from 'react-native';

export const DebugOverlay = () => {
  if (!__DEV__) return null;

  return (
    <View style={styles.overlay}>
      <Text style={styles.text}>🔍 DEBUG: App is rendering</Text>
      <Text style={styles.text}>Platform: {Platform.OS}</Text>
      <Text style={styles.text}>Time: {new Date().toLocaleTimeString()}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: 'rgba(255, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 5,
    zIndex: 9999,
  },
  text: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
