// CTRON Home - Environment Validator
// Component to validate environment configuration and detect mock data

import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { validateEnvironmentConfig, checkForMockData } from '../utils/mockDataDetector';
import { debugLogger } from '../utils/debugLogger';
import { useTheme } from '../context/ThemeContext';

interface EnvironmentValidatorProps {
  children: React.ReactNode;
}

export const EnvironmentValidator: React.FC<EnvironmentValidatorProps> = ({ children }) => {
  const { colors, spacing, typography } = useTheme();
  const styles = getStyles(colors, spacing, typography);
  const [isValidated, setIsValidated] = useState(false);
  const [hasErrors] = useState(false);

  if (__DEV__) {
    console.log('🔧 EnvironmentValidator rendering - isValidated:', isValidated, 'hasErrors:', hasErrors);
  }

  useEffect(() => {
    validateEnvironment();
  }, []);

  const validateEnvironment = async () => {
    try {
      if (__DEV__) {
        console.log('🔧 EnvironmentValidator: Starting validation...');
      }

      // In development mode, be more lenient with validation
      if (__DEV__) {
        console.log('🔧 Environment Validator: Development mode - skipping strict validation');
        setIsValidated(true);
        return;
      }

      // Validate environment configuration
      const { isValid, issues } = validateEnvironmentConfig();

      if (!isValid) {
        debugLogger.error('Environment validation failed:', issues);

        // In production, show error but don't block the app completely
        console.warn('🚨 Environment configuration issues:');
        issues.forEach(issue => console.warn(`  - ${issue}`));

        // Still allow the app to continue in case of minor issues
        setIsValidated(true);
        return;
      }

      // Check for mock data in environment variables
      const envVars = {
        API_URL: process.env.EXPO_PUBLIC_API_URL,
        API_BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL,
        SOCKET_URL: process.env.EXPO_PUBLIC_SOCKET_URL,
        STRIPE_KEY: process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY,
        APP_NAME: process.env.EXPO_PUBLIC_APP_NAME,
      };

      checkForMockData(envVars, 'environment variables');

      // Log successful validation
      if (isValid) {
        debugLogger.success('Environment validation passed');
      }

      setIsValidated(true);
    } catch (error) {
      debugLogger.error('Environment validation error:', error);
      // Don't block the app - just log the error and continue
      console.warn('🚨 Environment validation failed, but continuing anyway:', error);
      setIsValidated(true);
    }
  };

  // Show error state if validation failed in production
  if (hasErrors && !__DEV__) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Configuration Error</Text>
        <Text style={styles.errorMessage}>
          The app is not properly configured. Please contact support.
        </Text>
      </View>
    );
  }

  // Show loading state during validation (but only briefly)
  if (!isValidated) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  // Render children if validation passed
  return <>{children}</>;
};

const getStyles = (colors: any, spacing: any, typography: any) => StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.systemBackground,
  },
  errorTitle: {
    ...typography.h2,
    color: colors.destructive,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  errorMessage: {
    ...typography.body,
    color: colors.text.primary,
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.systemBackground,
  },
  loadingText: {
    ...typography.body,
    color: colors.text.secondary,
  },
});
