import { Component, ErrorInfo, ReactNode } from 'react';
 import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
 import { ThemeContext } from '../context/ThemeContext';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  static contextType = ThemeContext;

  componentDidMount() {
    if (this.context) {
      const { colors, typography, spacing, borderRadius } = this.context;
      this.setState({ styles: getStyles(colors, typography, spacing, borderRadius) });
    }
  }
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service in production
    if (__DEV__) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
    // In production, send to error tracking service like Sentry
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

            const { colors, typography, spacing, borderRadius } = this.context;
      const themedStyles = getStyles(colors, typography, spacing, borderRadius);

      return (
        <View style={themedStyles.container}>
          <Text style={themedStyles.title}>Oops! Something went wrong</Text>
          <Text style={themedStyles.message}>
            We&apos;re sorry for the inconvenience. Please try again.
          </Text>
          {__DEV__ && this.state.error && (
            <Text style={themedStyles.errorDetails}>
              {this.state.error.message}
            </Text>
          )}
          <TouchableOpacity style={themedStyles.retryButton} onPress={this.handleRetry}>
            <Text style={themedStyles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

const getStyles = (colors: any, typography: any, spacing: any, borderRadius: any) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.background.primary,
  },
  title: {
    ...typography.title1,
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  message: {
    ...typography.body,
    color: colors.text.secondary,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  errorDetails: {
    ...typography.footnote,
    color: colors.error.main,
    marginBottom: spacing.lg,
    textAlign: 'center',
    fontFamily: 'monospace',
  },
  retryButton: {
    backgroundColor: colors.primary.main,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.md,
  },
  retryButtonText: {
    ...typography.callout,
    color: colors.white,
    fontWeight: '600',
  },
});
