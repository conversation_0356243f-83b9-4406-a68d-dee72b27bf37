import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useTheme } from '../hooks/useTheme';
import { useErrorHandler } from '../hooks/useErrorHandler';

interface ErrorRecoveryProps {
  error?: Error | null;
  onRetry?: () => void;
  onReset?: () => void;
  context?: string;
  showDetails?: boolean;
  customMessage?: string;
  children?: React.ReactNode;
}

/**
 * Enhanced error recovery component with automatic retry and fallback options
 */
export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onReset,
  context = 'ErrorRecovery',
  showDetails = false,
  customMessage,
  children,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  const { handleError } = useErrorHandler({
    context,
    onRetry: onRetry,
    enableRetry: !!onRetry,
  });

  useEffect(() => {
    if (error) {
      handleError(error);
    }
  }, [error, handleError]);

  const handleRetry = async () => {
    if (retryCount >= maxRetries) {
      Alert.alert(
        'Maximum Retries Reached',
        'The operation has failed multiple times. Please try again later or contact support.',
        [
          { text: 'Reset', onPress: onReset },
          { text: 'OK', style: 'cancel' }
        ]
      );
      return;
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      await onRetry?.();
    } catch (retryError) {
      handleError(retryError);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleShowDetails = () => {
    if (error) {
      Alert.alert(
        'Error Details',
        `Message: ${error.message}\n\nStack: ${error.stack?.substring(0, 500)}...`,
        [{ text: 'OK' }]
      );
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing[6],
      backgroundColor: colors.background.primary,
    },
    errorCard: {
      backgroundColor: colors.background.secondary,
      borderRadius: borderRadius.lg,
      padding: spacing[6],
      marginBottom: spacing[6],
      borderWidth: 1,
      borderColor: colors.secondary[200],
      shadowColor: colors.secondary[900],
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      maxWidth: 400,
      width: '100%',
    },
    errorIcon: {
      fontSize: 48,
      textAlign: 'center',
      marginBottom: spacing[4],
    },
    errorTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: '600',
      color: colors.text.primary,
      textAlign: 'center',
      marginBottom: spacing[2],
    },
    errorMessage: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing[6],
      lineHeight: 24,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: spacing[3],
      flexWrap: 'wrap',
    },
    retryButton: {
      backgroundColor: colors.primary.main,
      paddingHorizontal: spacing[6],
      paddingVertical: spacing[3],
      borderRadius: borderRadius.md,
      minWidth: 100,
      alignItems: 'center',
    },
    retryButtonDisabled: {
      backgroundColor: colors.gray[400],
    },
    resetButton: {
      backgroundColor: colors.background.primary,
      borderWidth: 1,
      borderColor: colors.border.medium,
      paddingHorizontal: spacing[6],
      paddingVertical: spacing[3],
      borderRadius: borderRadius.md,
      minWidth: 100,
      alignItems: 'center',
    },
    detailsButton: {
      backgroundColor: colors.background.primary,
      borderWidth: 1,
      borderColor: colors.border.light,
      paddingHorizontal: spacing[4],
      paddingVertical: spacing[2],
      borderRadius: borderRadius.sm,
      marginTop: spacing[4],
    },
    buttonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: '500',
      color: colors.background.primary,
    },
    resetButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: '500',
      color: colors.text.primary,
    },
    detailsButtonText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
    retryInfo: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      textAlign: 'center',
      marginTop: spacing[2],
    },
  });

  if (!error) {
    return <>{children}</>;
  }

  const errorMessage = customMessage || error.message || 'An unexpected error occurred';

  return (
    <View style={styles.container}>
      <View style={styles.errorCard}>
        <Text style={styles.errorIcon}>⚠️</Text>
        <Text style={styles.errorTitle}>Something went wrong</Text>
        <Text style={styles.errorMessage}>{errorMessage}</Text>

        <View style={styles.buttonContainer}>
          {onRetry && (
            <TouchableOpacity
              style={[
                styles.retryButton,
                (isRetrying || retryCount >= maxRetries) && styles.retryButtonDisabled
              ]}
              onPress={handleRetry}
              disabled={isRetrying || retryCount >= maxRetries}
            >
              {isRetrying ? (
                <ActivityIndicator size="small" color={colors.background.primary} />
              ) : (
                <Text style={styles.buttonText}>
                  {retryCount >= maxRetries ? 'Max Retries' : 'Try Again'}
                </Text>
              )}
            </TouchableOpacity>
          )}

          {onReset && (
            <TouchableOpacity style={styles.resetButton} onPress={onReset}>
              <Text style={styles.resetButtonText}>Reset</Text>
            </TouchableOpacity>
          )}
        </View>

        {retryCount > 0 && (
          <Text style={styles.retryInfo}>
            Retry attempt: {retryCount}/{maxRetries}
          </Text>
        )}

        {showDetails && __DEV__ && (
          <TouchableOpacity style={styles.detailsButton} onPress={handleShowDetails}>
            <Text style={styles.detailsButtonText}>Show Details (Dev)</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default ErrorRecovery;
