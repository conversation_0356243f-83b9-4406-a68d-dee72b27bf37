// CTRON Home - Screen Error Boundary
// Specialized error boundary for individual screens

import React, { ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';


import { debugLogger } from '../utils/debugLogger';

interface Props {
  children: ReactNode;
  screenName?: string;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class ScreenErrorBoundary extends React.Component<Props, State> {
  static displayName = 'ScreenErrorBoundary';
  static contextType = ThemeContext;
  declare context: React.ContextType<typeof ThemeContext>;

  componentDidMount() {
    if (this.context) {
      // Context properties can be accessed directly via this.context if needed
    }
  }
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error for debugging
    debugLogger.error(`Screen Error in ${this.props.screenName || 'Unknown Screen'}:`, {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // In production, send to error tracking service
    if (!__DEV__) {
      // Example: Sentry.captureException(error, { contexts: { react: errorInfo } });
      console.error('Production error captured:', error);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });

    // Call custom retry handler if provided
    this.props.onRetry?.();
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { colors, spacing, typography } = this.context;
      const themedStyles = styles(colors, spacing, typography);

      // Default error screen
      return (
        <View style={themedStyles.container}>
          <View style={themedStyles.errorContainer}>
            <Ionicons
              name="warning-outline"
              size={64}
              color={themedStyles.errorIcon.color}
              style={themedStyles.errorIcon}
            />

            <Text style={themedStyles.errorTitle}>
              Oops! Something went wrong
            </Text>

            <Text style={themedStyles.errorMessage}>
              {this.props.screenName
                ? `There was an error loading the ${this.props.screenName} screen.`
                : 'There was an unexpected error.'
              }
            </Text>

            {__DEV__ && this.state.error && (
              <View style={themedStyles.debugContainer}>
                <Text style={themedStyles.debugTitle}>Debug Information:</Text>
                <Text style={themedStyles.debugText}>
                  {this.state.error.message}
                </Text>
                {this.state.error.stack && (
                  <Text style={themedStyles.debugStack} numberOfLines={10}>
                    {this.state.error.stack}
                  </Text>
                )}
              </View>
            )}

            <View style={themedStyles.buttonContainer}>
              <TouchableOpacity
                style={themedStyles.retryButton}
                onPress={this.handleRetry}
                activeOpacity={0.8}
              >
                <Ionicons name="refresh" size={20} color="white" />
                <Text style={themedStyles.retryButtonText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = (colors: any, spacing: any, typography: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorContainer: {
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
  },
  errorIcon: {
    marginBottom: spacing.lg,
    color: colors.error.main,
  },
  errorTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  errorMessage: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  debugContainer: {
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.lg,
    width: '100%',
    maxHeight: 200,
  },
  debugTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  debugText: {
    fontSize: typography.fontSize.sm,
    color: colors.error.main,
    marginBottom: spacing.sm,
    fontFamily: 'monospace',
  },
  debugStack: {
    fontSize: typography.fontSize.xs,
    color: '#666',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  retryButton: {
    backgroundColor: '#007AFF', // Default primary color
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  retryButtonText: {
    ...typography.button,
    color: 'white',
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
});

// Convenience wrapper for screens
export const withScreenErrorBoundary = (
  WrappedComponent: React.ComponentType<any>,
  screenName?: string
) => {
  const ComponentWithErrorBoundary = (props: any) => (
    <ScreenErrorBoundary screenName={screenName}>
      <WrappedComponent {...props} />
    </ScreenErrorBoundary>
  );
  ComponentWithErrorBoundary.displayName = `withScreenErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;
  return ComponentWithErrorBoundary;
};

export default ScreenErrorBoundary;
