import React, { useEffect, useState } from 'react';
import { loadStripe, Stripe } from '@stripe/stripe-js';

interface StripeWebProviderProps {
  children: React.ReactNode;
}

const StripeWebContext = React.createContext<Stripe | null>(null);

export const StripeWebProvider: React.FC<StripeWebProviderProps> = ({ children }) => {
  const [/* stripePromise, */ setStripePromise] = useState<Promise<Stripe | null> | null>(null);

  useEffect(() => {
    if (process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
      const stripeKey = process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY;
      if (stripeKey) {
        const stripePromise = loadStripe(stripeKey);
        setStripePromise(stripePromise);
      }
    }
  }, [setStripePromise]);

  return (
    <StripeWebContext.Provider value={null}>
      {children}
    </StripeWebContext.Provider>
  );
};

export const useStripeWeb = () => {
  return React.useContext(StripeWebContext);
};