// src/components/TechnicianListItem.tsx

import { Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { Technician } from '../types/technician';

interface Props {
  technician: Technician;
  onPress: () => void;
}

const TechnicianListItem = ({ technician, onPress }: Props) => {
  const { colors, typography, spacing, borderRadius } = useTheme();
  const styles = getStyles(colors, typography, spacing, borderRadius);
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Text style={styles.name}>🔧 {technician.specialization}</Text>
      <Text style={styles.meta}>Rating: {technician.rating.toFixed(1)}</Text>
      <Text style={styles.meta}>Available: {technician.isAvailable ? 'Yes' : 'No'}</Text>
    </TouchableOpacity>
  );
};

export default TechnicianListItem;

const getStyles = (colors: any, typography: any, spacing: any, borderRadius: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginVertical: spacing.sm,
  },
  name: {
    ...typography.h4,
    color: colors.text.primary,
  },
  meta: {
    ...typography.body,
    color: colors.text.secondary,
  },
});
