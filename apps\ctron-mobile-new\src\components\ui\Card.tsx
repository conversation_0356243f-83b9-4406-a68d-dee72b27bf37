// CTRON Home Design System - Card Component
// Flexible card component for jobs, technicians, and content display


import {
  View,
  StyleSheet,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';

export interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'job' | 'technician';
  padding?: number;
  margin?: number;
  onPress?: () => void;
  style?: any;
  testID?: string;
}

export const Card: React.FC<CardProps> = (
  { children, variant = 'default', padding = 16, margin, onPress, style, testID, ...props }
) => {
  const theme = useTheme();
  const styles = getStyles(theme.colors, theme.spacing, theme.borderRadius, theme.shadows);

  const cardStyles = [
     styles.base,
     styles[variant],
    { padding },
    margin && { margin },
    style,
  ];

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyles as any}
        onPress={onPress}
        activeOpacity={0.95}
        testID={testID}
        accessibilityRole="button"
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyles as any} testID={testID}>
      {children}
    </View>
  );
};

// Job Card Specific Component
export interface JobCardProps extends CardProps {
  status?: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled';
  priority?: 'low' | 'medium' | 'high' | 'emergency';
}

export const JobCard: React.FC<JobCardProps> = ({
  children,
  status,
  priority,
  style,
  ...props
}) => {
  const theme = useTheme();
  const styles = getStyles(theme.colors, theme.spacing, theme.borderRadius, theme.shadows);
  const jobCardStyles = [
    styles.jobCard,
    status && styles[`status_${status}`],
    priority === 'emergency' && styles.emergencyBorder,
    style,
  ];

  return (
    <Card variant="job" style={jobCardStyles} {...props}>
      {children}
    </Card>
  );
};

// Technician Card Specific Component
export interface TechnicianCardProps extends CardProps {
  verified?: boolean;
  available?: boolean;
}

export const TechnicianCard: React.FC<TechnicianCardProps> = ({
  children,
  verified,
  available,
  style,
  ...props
}) => {
  const theme = useTheme();
  const styles = getStyles(theme.colors, theme.spacing, theme.borderRadius, theme.shadows);
  const technicianCardStyles = [
    styles.technicianCard,
    verified && styles.verifiedCard,
    available && styles.availableCard,
    style,
  ];

  return (
    <Card variant="technician" style={technicianCardStyles} {...props}>
      {children}
    </Card>
  );
};

const getStyles = (colors: any, spacing: any, borderRadius: any, shadows: any) => StyleSheet.create({
  base: {
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
  },

  // Variants
  default: {
    borderWidth: 1,
    borderColor: colors.border.primary,
    ...shadows.sm,
  },

  elevated: {
    backgroundColor: colors.background.secondary,
    ...shadows.md,
    borderWidth: 1,
    borderColor: colors.border.primary,
  },

  outlined: {
    borderWidth: 1,
    borderColor: colors.border.primary,
    backgroundColor: colors.background.primary,
  },

  job: {
    backgroundColor: colors.background.primary,
    ...shadows.sm,
    borderWidth: 1,
    borderColor: colors.border.primary,
    marginBottom: spacing.md,
  },

  technician: {
    backgroundColor: colors.background.primary,
    ...shadows.sm,
    borderWidth: 1,
    borderColor: colors.border.primary,
    position: 'relative',
  },

  // Job Status Styles
  status_pending: {
    borderLeftWidth: 4,
    borderLeftColor: colors.warning.main,
  },

  status_assigned: {
    borderLeftWidth: 4,
    borderLeftColor: colors.info.main,
  },

  status_active: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary.main,
  },

  status_completed: {
    borderLeftWidth: 4,
    borderLeftColor: colors.success.main,
  },

  status_cancelled: {
    borderLeftWidth: 4,
    borderLeftColor: colors.error.main,
  },

  emergencyBorder: {
    borderWidth: 2,
    borderColor: colors.error.main,
    ...shadows.lg,
  },

  // Technician Card Styles
  verifiedCard: {
    borderTopWidth: 4,
    borderTopColor: colors.success.main,
  },

  availableCard: {
    backgroundColor: colors.secondarySystemBackground,
  },

  jobCard: {
    // Additional job-specific styles can be added here
  },

  technicianCard: {
    // Additional technician-specific styles can be added here
  },
});

export default Card;
