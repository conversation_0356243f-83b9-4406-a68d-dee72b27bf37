// CTRON Home Design System - Custom Date Picker Component
// Visual date picker with calendar interface

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,

  ScrollView,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

export interface CustomDatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'date' | 'time' | 'datetime';
  label?: string;
  style?: any;
  disabled?: boolean;
}

export const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  value,
  onChange,
  minimumDate,
  maximumDate,
  mode = 'datetime',
  label,
  style,
  disabled = false,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const styles = getStyles(colors, spacing, typography, borderRadius);
  const [showPicker, setShowPicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value);
  const [selectedTime, setSelectedTime] = useState({
    hour: value.getHours(),
    minute: value.getMinutes(),
  });

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const formatDisplayValue = () => {
    if (mode === 'date') {
      return value.toLocaleDateString();
    } else if (mode === 'time') {
      return value.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return value.toLocaleString([], {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const isDateDisabled = (date: Date) => {
    if (minimumDate && date < minimumDate) return true;
    if (maximumDate && date > maximumDate) return true;
    return false;
  };

  const handleDateSelect = (day: number) => {
    const newDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), day);
    if (!isDateDisabled(newDate)) {
      setSelectedDate(newDate);
    }
  };

  const handleMonthChange = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setSelectedDate(newDate);
  };

  const handleTimeChange = (type: 'hour' | 'minute', value: number) => {
    setSelectedTime(prev => ({
      ...prev,
      [type]: value
    }));
  };

  const handleConfirm = () => {
    const finalDate = new Date(selectedDate);
    if (mode === 'datetime' || mode === 'time') {
      finalDate.setHours(selectedTime.hour, selectedTime.minute);
    }
    onChange(finalDate);
    setShowPicker(false);
  };

  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(selectedDate);
    const firstDay = getFirstDayOfMonth(selectedDate);
    const days = [];

    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.dayCell} />);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), day);
      const isDisabled = isDateDisabled(date);
      const isSelected = day === selectedDate.getDate();

      days.push(
        <TouchableOpacity
          key={day}
          style={[
            styles.dayCell,
            isSelected && styles.selectedDay,
            isDisabled && styles.disabledDay,
          ]}
          onPress={() => handleDateSelect(day)}
          disabled={isDisabled}
        >
          <Text style={[
            styles.dayText,
            isSelected && styles.selectedDayText,
            isDisabled && styles.disabledDayText,
          ]}>
            {day}
          </Text>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.calendar}>
        <View style={styles.calendarHeader}>
          <TouchableOpacity onPress={() => handleMonthChange('prev')}>
            <Text style={styles.navButton}>‹</Text>
          </TouchableOpacity>
          <Text style={styles.monthYear}>
            {months[selectedDate.getMonth()]} {selectedDate.getFullYear()}
          </Text>
          <TouchableOpacity onPress={() => handleMonthChange('next')}>
            <Text style={styles.navButton}>›</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.weekDays}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <Text key={day} style={styles.weekDayText}>{day}</Text>
          ))}
        </View>

        <View style={styles.daysGrid}>
          {days}
        </View>
      </View>
    );
  };

  const renderTimePicker = () => {
    const hours = Array.from({ length: 24 }, (_, i) => i);
    const minutes = Array.from({ length: 60 }, (_, i) => i);

    return (
      <View style={styles.timePicker}>
        <Text style={styles.timeLabel}>Select Time</Text>
        <View style={styles.timeSelectors}>
          <View style={styles.timeColumn}>
            <Text style={styles.timeColumnLabel}>Hour</Text>
            <ScrollView style={styles.timeScroll} showsVerticalScrollIndicator={false}>
              {hours.map(hour => (
                <TouchableOpacity
                  key={hour}
                  style={[
                    styles.timeOption,
                    selectedTime.hour === hour && styles.selectedTimeOption,
                  ]}
                  onPress={() => handleTimeChange('hour', hour)}
                >
                  <Text style={[
                    styles.timeOptionText,
                    selectedTime.hour === hour && styles.selectedTimeOptionText,
                  ]}>
                    {hour.toString().padStart(2, '0')}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View style={styles.timeColumn}>
            <Text style={styles.timeColumnLabel}>Minute</Text>
            <ScrollView style={styles.timeScroll} showsVerticalScrollIndicator={false}>
              {minutes.filter(m => m % 15 === 0).map(minute => (
                <TouchableOpacity
                  key={minute}
                  style={[
                    styles.timeOption,
                    selectedTime.minute === minute && styles.selectedTimeOption,
                  ]}
                  onPress={() => handleTimeChange('minute', minute)}
                >
                  <Text style={[
                    styles.timeOptionText,
                    selectedTime.minute === minute && styles.selectedTimeOptionText,
                  ]}>
                    {minute.toString().padStart(2, '0')}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}

      <TouchableOpacity
        style={[styles.dateButton, disabled && styles.disabled]}
        onPress={() => !disabled && setShowPicker(true)}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel={`Select ${mode}, current value: ${formatDisplayValue()}`}
      >
        <Text style={[styles.dateText, disabled && styles.disabledText]}>
          {formatDisplayValue()}
        </Text>
        <Text style={styles.chevron}>📅</Text>
      </TouchableOpacity>

      <Modal
        visible={showPicker}
        transparent
        animationType="slide"
        onRequestClose={() => setShowPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select {mode === 'datetime' ? 'Date & Time' : mode}</Text>
              <TouchableOpacity onPress={() => setShowPicker(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {(mode === 'date' || mode === 'datetime') && renderCalendar()}
              {(mode === 'time' || mode === 'datetime') && renderTimePicker()}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowPicker(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleConfirm}
              >
                <Text style={styles.confirmButtonText}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any) => StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },

  label: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },

  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    backgroundColor: colors.background.primary,
    minHeight: 48,
  },

  dateText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    flex: 1,
  },

  chevron: {
    fontSize: 20,
    marginLeft: spacing.sm,
  },

  disabled: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[300],
  },

  disabledText: {
    color: colors.gray[500],
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.lg,
    width: width * 0.9,
    maxHeight: '80%',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing[5],
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },

  modalTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
  },

  closeButton: {
    fontSize: 20,
    color: colors.text.secondary,
    padding: spacing.sm,
  },

  modalBody: {
    maxHeight: 400,
  },

  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: spacing[5],
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
  },

  cancelButton: {
    flex: 1,
    padding: spacing.md,
    marginRight: spacing.sm,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border.medium,
    alignItems: 'center',
  },

  cancelButtonText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
  },

  confirmButton: {
    flex: 1,
    padding: spacing.md,
    marginLeft: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: colors.primary[900],
    alignItems: 'center',
  },

  confirmButtonText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.inverse,
    fontWeight: '600' as const,
  },

  // Calendar styles
  calendar: {
    padding: spacing.lg,
  },

  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },

  navButton: {
    fontSize: 24,
    color: colors.primary[900],
    padding: spacing.sm,
    fontWeight: 'bold',
  },

  monthYear: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
  },

  weekDays: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },

  weekDayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.tertiary,
    fontWeight: '600' as const,
  },

  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },

  dayCell: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },

  dayText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
  },

  selectedDay: {
    backgroundColor: colors.primary[900],
    borderRadius: borderRadius.full,
  },

  selectedDayText: {
    color: colors.text.inverse,
    fontWeight: '600' as const,
  },

  disabledDay: {
    opacity: 0.3,
  },

  disabledDayText: {
    color: colors.gray[400],
  },

  // Time picker styles
  timePicker: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
  },

  timeLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },

  timeSelectors: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },

  timeColumn: {
    flex: 1,
    marginHorizontal: spacing.sm,
  },

  timeColumnLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.sm,
    fontWeight: '600' as const,
  },

  timeScroll: {
    maxHeight: 120,
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: borderRadius.md,
  },

  timeOption: {
    padding: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },

  timeOptionText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
  },

  selectedTimeOption: {
    backgroundColor: colors.primary[100],
  },

  selectedTimeOptionText: {
    color: colors.primary[900],
    fontWeight: '600' as const,
  },
});

export default CustomDatePicker;
