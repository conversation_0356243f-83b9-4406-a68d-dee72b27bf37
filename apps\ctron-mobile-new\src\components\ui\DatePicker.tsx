// CTRON Home Design System - Date Picker Component
// Cross-platform date picker with fallback support

import DateTimePickerModule from '@react-native-community/datetimepicker';
import { colors, typography, borderRadius, spacing } from '@/theme';
import * as React from 'react';
import { useState } from 'react';
import {
  Platform, 
  Alert,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
} from 'react-native';




// DateTimePicker configuration with fallback support
let DateTimePicker: any = null;
let hasDateTimePicker = false;

try {
  DateTimePicker = DateTimePickerModule;
  hasDateTimePicker = true;
} catch {
  if (__DEV__) {
    console.log('DateTimePicker not available, using text input fallback');
  }
  hasDateTimePicker = false;
}

export interface DatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'date' | 'time' | 'datetime';

  label?: string;
  style?: any;
  disabled?: boolean;
}

export const DatePicker: React.FC<DatePickerProps> = ({ 
  value,
  onChange,
  minimumDate,
  maximumDate,
  mode = 'datetime',
  // placeholder = 'Select date and time',
  label,
  style,
  disabled = false,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [textValue, setTextValue] = useState(value.toLocaleString());

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // Handle platform-specific behavior
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (event?.type === 'dismissed') {
      setShowPicker(false);
      return;
    }

    if (selectedDate) {
      // Validate date range
      if (minimumDate && selectedDate < minimumDate) {
        Alert.alert('Invalid Date', 'Please select a date in the future');
        return;
      }

      if (maximumDate && selectedDate > maximumDate) {
        Alert.alert('Invalid Date', 'Please select an earlier date');
        return;
      }

      onChange(selectedDate);
      setTextValue(selectedDate.toLocaleString());

      if (Platform.OS === 'ios') {
        setShowPicker(false);
      }
    }
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);

    // Try to parse the text as a date with multiple formats
    let parsedDate: Date | null = null;

    // Try different date formats
    const formats = [
      text, // Direct parsing
      text.replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/, '$3-$1-$2'), // MM/DD/YYYY to YYYY-MM-DD
      text.replace(/(\d{1,2})-(\d{1,2})-(\d{4})/, '$3-$1-$2'), // MM-DD-YYYY to YYYY-MM-DD
      text.replace(/(\d{4})\/(\d{1,2})\/(\d{1,2})/, '$1-$2-$3'), // YYYY/MM/DD to YYYY-MM-DD
    ];

    for (const format of formats) {
      const testDate = new Date(format);
      if (!isNaN(testDate.getTime())) {
        parsedDate = testDate;
        break;
      }
    }

    if (parsedDate) {
      // Validate parsed date
      if (minimumDate && parsedDate < minimumDate) {
        return;
      }
      if (maximumDate && parsedDate > maximumDate) {
        return;
      }
      onChange(parsedDate);
    }
  };

  const openPicker = () => {
    if (disabled) return;
    setShowPicker(true);
  };

  const formatDisplayValue = () => {
    if (mode === 'date') {
      return value.toLocaleDateString();
    } else if (mode === 'time') {
      return value.toLocaleTimeString();
    } else {
      return value.toLocaleString();
    }
  };

  const styles = getStyles(colors, typography, borderRadius, spacing);

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}

      {hasDateTimePicker ? (
        <>
          <TouchableOpacity
            style={[styles.dateButton, disabled && styles.disabled]}
            onPress={openPicker}
            disabled={disabled}
            accessibilityRole="button"
            accessibilityLabel={`Select ${mode}, current value: ${formatDisplayValue()}`}
          >
            <Text style={[styles.dateText, disabled && styles.disabledText]}>
              {formatDisplayValue()}
            </Text>
            <Text style={styles.chevron}>▼</Text>
          </TouchableOpacity>

          {showPicker && Platform.OS !== 'web' && (
            <DateTimePicker
              testID="dateTimePicker"
              value={value}
              mode={mode}
              is24Hour={true}
              display={Platform.OS === 'ios' ? 'default' : 'default'}
              onChange={handleDateChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
            />
          )}
        </>
      ) : (
        // Fallback to text input with better formatting
        <View>
          <TextInput
            style={[styles.textInput, disabled && styles.disabled]}
            value={textValue}
            onChangeText={handleTextChange}
            placeholder={mode === 'date' ? 'MM/DD/YYYY' : mode === 'time' ? 'HH:MM AM/PM' : 'MM/DD/YYYY HH:MM AM/PM'}
            placeholderTextColor={colors.text.tertiary}
            editable={!disabled}
            accessibilityLabel={label || 'Date input'}
          />
          <Text style={styles.helpText}>
            {mode === 'date'
              ? 'Enter date as MM/DD/YYYY (e.g., 12/25/2024)'
              : mode === 'time'
              ? 'Enter time as HH:MM AM/PM (e.g., 2:30 PM)'
              : 'Enter date and time (e.g., 12/25/2024 2:30 PM)'
            }
          </Text>
        </View>
      )}
    </View>
  );
};

const getStyles = (colors: any, typography: any, borderRadius: any, spacing: any) => StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },
  label: {
    ...typography.text.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: borderRadius.md,
    backgroundColor: colors.background.primary,
  },
  dateText: {
    ...typography.text.md,
    color: colors.text.primary,
  },
  chevron: {
    color: colors.text.tertiary,
  },
  textInput: {
    ...typography.text.md,
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
  },
  helpText: {
    ...typography.text.xs,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
  },
  disabled: {
    backgroundColor: colors.background.disabled,
    borderColor: colors.border.disabled,
  },
  disabledText: {
    color: colors.text.disabled,
  },
});

export default DatePicker;
