// CTRON Home Design System - Header Component
// Consistent header with navigation and actions

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,

} from 'react-native';

import { useTheme } from '../../context/ThemeContext';
// ... existing code ...


export interface HeaderProps {
  title?: string;
  subtitle?: string;
  leftAction?: {
    icon: React.ReactNode;
    onPress: () => void;
    accessibilityLabel: string;
  };
  rightAction?: {
    icon: React.ReactNode;
    onPress: () => void;
    accessibilityLabel: string;
    badge?: number;
  };
  backgroundColor?: string;
  style?: any;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftAction,
  rightAction,
  backgroundColor = '#007AFF', // Default primary color
  style,
}) => {
  const { colors, typography, shadows, spacing } = useTheme();
  const styles = getStyles(colors, typography, shadows, spacing);
  return (
    <>
      {/* StatusBar not available in React Native web */}

      return (
      <View style={[{ backgroundColor }, style]}>
        <View style={[styles.container, { backgroundColor }]}>
          {/* Left Action */}
          <View style={styles.leftSection}>
            {leftAction && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={leftAction.onPress}
                accessibilityRole="button"
                accessibilityLabel={leftAction.accessibilityLabel}
              >
                {leftAction.icon}
              </TouchableOpacity>
            )}
          </View>

          {/* Title Section */}
          <View style={styles.titleSection}>
            {title && (
              <Text style={styles.title} numberOfLines={1}>
                {title}
              </Text>
            )}
            {subtitle && (
              <Text style={styles.subtitle} numberOfLines={1}>
                {subtitle}
              </Text>
            )}
          </View>

          {/* Right Action */}
          <View style={styles.rightSection}>
            {rightAction && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={rightAction.onPress}
                accessibilityRole="button"
                accessibilityLabel={rightAction.accessibilityLabel}
              >
                <View style={styles.iconContainer}>
                  {rightAction.icon}
                  {rightAction.badge && rightAction.badge > 0 && (
                    <View style={styles.badge}>
                      <Text style={styles.badgeText}>
                        {rightAction.badge > 99 ? '99+' : rightAction.badge.toString()}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </>
  );
};

const getStyles = (colors: any, typography: any, shadows: any, spacing: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 56,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.secondary,
    ...shadows.sm,
  },

  leftSection: {
    flex: 1,
    alignItems: 'flex-start',
  },

  titleSection: {
    flex: 3,
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
  },

  rightSection: {
    flex: 1,
    alignItems: 'flex-end',
  },

  actionButton: {
    padding: spacing.xs,
    borderRadius: 8,
    minWidth: 44,
    minHeight: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },

  iconContainer: {
    position: 'relative',
  },

  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.primary.main,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xs,
    borderWidth: 2,
    borderColor: colors.background.primary,
  },

  badgeText: {
    color: colors.text.inverted,
    ...typography.text.xs,
  },

  title: {
    ...typography.text.lg,
    color: colors.text.primary,
    textAlign: 'center',
    fontWeight: 'bold',
  },

  subtitle: {
    ...typography.text.sm,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
});

export default Header;
