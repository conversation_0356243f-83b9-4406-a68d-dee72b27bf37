// CTRON Home - Enhanced Input Component
// Modern input component with design system integration

import { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TextInputProps } from 'react-native';
import { useTheme } from '../../context/ThemeContext';



interface InputProps extends TextInputProps {
  label?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
}

export const Input = (props: InputProps) => {
  const { label, required = false, error, hint, variant = 'default', size = 'medium', style, onFocus, onBlur, ...restProps } = props;
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const { colors, spacing, typography, borderRadius, shadows, sizes } = useTheme();
  const styles = getStyles(colors, spacing, typography, borderRadius, shadows, sizes);

  const inputStyles = [
    styles.input,
    styles[variant],
    styles[`size_${size}`],
    isFocused && styles.focused,
    error && styles.inputError,
    style,
  ].filter(Boolean) as any;

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      <TextInput
        style={inputStyles}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholderTextColor={colors.tertiaryLabel}
        selectionColor={colors.primary.main}
        {...restProps}
      />
      {error && <Text style={styles.errorText}>{error}</Text>}
      {hint && !error && <Text style={styles.hintText}>{hint}</Text>}
    </View>
  );
};

const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any, shadows: any, sizes: any) => StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },

  label: {
    ...typography.text.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },

  required: {
    color: colors.error.main,
  },

  input: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.text.md,
    color: colors.text.primary,
    minHeight: sizes.touchTarget,
    ...shadows.sm,
  },

  // Variants
  default: {
    backgroundColor: colors.background.secondary,
    borderColor: colors.border.primary,
  },

  outlined: {
    backgroundColor: colors.background.primary,
    borderColor: colors.border.primary,
  },

  filled: {
    backgroundColor: colors.background.secondary,
    borderColor: 'transparent',
  },

  // Sizes
  size_small: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    minHeight: sizes.inputHeight.sm,
    ...typography.text.sm,
  },

  size_medium: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 44,
    ...typography.text.md,
  },

  size_large: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: sizes.inputHeight.lg,
    ...typography.text.lg,
  },

  // States
  focused: {
    borderColor: colors.primary.main,
    borderWidth: 2,
    ...shadows.md,
  },

  inputError: {
    borderColor: colors.error.main,
    borderWidth: 2,
  },

  errorText: {
    ...typography.text.xs,
    color: colors.error.main,
    marginTop: spacing.xs,
  },

  hintText: {
    ...typography.text.xs,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
  },
});
