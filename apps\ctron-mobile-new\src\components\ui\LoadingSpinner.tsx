// src/components/ui/LoadingSpinner.tsx
import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../context/ThemeContext';


interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  style?: any;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = memo(({ 
  size = 'large',
  color,
  text,
  style,
}) => {
  LoadingSpinner.displayName = 'LoadingSpinner';
  const { colors, spacing, typography } = useTheme();
  const themedColor = color || colors.primary.main;
  const styles = getStyles(colors, spacing, typography);
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={themedColor} />
      {text && <Text style={styles.text}>{text}</Text>}
    </View>
  );
});

LoadingSpinner.propTypes = {
  size: PropTypes.oneOf(['small', 'large']),
  color: PropTypes.string,
  text: PropTypes.string,
  style: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.array,
  ]),
};

const getStyles = (colors: any, spacing: any, typography: any) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  text: {
    marginTop: spacing.sm,
    ...typography.text.sm,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});
