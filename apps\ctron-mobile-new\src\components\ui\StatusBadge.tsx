// CTRON Home Design System - Status Badge Component
// Visual status indicators for jobs, technicians, and system states

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';

// Define types locally since they're not available in React Native web
type ViewStyle = any;
type TextStyle = any;
import { useTheme } from '../../context/ThemeContext';



const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any) => StyleSheet.create({
  base: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderRadius: borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  sm: {
    paddingVertical: spacing.xxs,
    paddingHorizontal: spacing.xs,
  },
  md: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
  },
  lg: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  filled: {},
  outlined: {
    borderWidth: 1,
  },
  subtle: {},
  priority: {
    borderRadius: borderRadius.full,
    paddingVertical: spacing.xxs,
    paddingHorizontal: spacing.xs,
  },
  text: {
    ...typography.text.xs,
    fontWeight: '600' as const,
  },
  smText: {
    ...typography.text.xxs,
  },
  mdText: {
    ...typography.text.xs,
  },
  lgText: {
    ...typography.text.sm,
  },
  verificationBadge: {
    backgroundColor: colors.success.light,
    borderRadius: borderRadius.full,
    paddingVertical: spacing.xxs,
    paddingHorizontal: spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  verificationIcon: {
    color: colors.success.dark,
    marginRight: spacing.xxs,
    ...typography.text.xxs,
  },
  verificationText: {
    color: colors.success.dark,
    ...typography.text.xxs,
    fontWeight: '600' as const,
  },
  emergencyPulse: {
    // Emergency pulse animation styles can be added here
  },
  emergencyText: {
    fontWeight: 'bold' as const,
  },
});

export interface StatusBadgeProps {
  status: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' | 'overdue' | 'verified' | 'available' | 'offline' | 'emergency';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'filled' | 'outlined' | 'subtle' | 'priority';
  customText?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'md',
  variant = 'filled',
  customText,
  style,
  textStyle,
  testID,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();

  const getStatusConfig = () => {
    const configs = {
      pending: {
        text: 'Pending',
        color: colors.warning.main,
        backgroundColor: colors.warning.main,
        lightBackground: '#FEF3C7',
      },
      assigned: {
        text: 'Assigned',
        color: colors.info.main,
        backgroundColor: colors.info.main,
        lightBackground: '#DBEAFE',
      },
      active: {
        text: 'In Progress',
        color: colors.primary.main,
        backgroundColor: colors.primary.main,
        lightBackground: '#E5F3FF',
      },
      completed: {
        text: 'Completed',
        color: colors.success.main,
        backgroundColor: colors.success.main,
        lightBackground: '#D1FAE5',
      },
      cancelled: {
        text: 'Cancelled',
        color: colors.gray500,
        backgroundColor: colors.gray500,
        lightBackground: '#F3F4F6',
      },
      overdue: {
        text: 'Overdue',
        color: colors.error.main,
        backgroundColor: colors.error.main,
        lightBackground: '#FECACA',
      },
      verified: {
        text: 'Verified',
        color: colors.success.main,
        backgroundColor: colors.success.main,
        lightBackground: '#D1FAE5',
      },
      available: {
        text: 'Available',
        color: colors.success.main,
        backgroundColor: colors.success.main,
        lightBackground: '#D1FAE5',
      },
      offline: {
        text: 'Offline',
        color: colors.gray500,
        backgroundColor: colors.gray500,
        lightBackground: colors.gray100,
      },
      emergency: {
        text: 'EMERGENCY',
        color: colors.white,
        backgroundColor: colors.error.main,
        lightBackground: '#FECACA',
      },
    };

    return configs[status];
  };

  const statusConfig = getStatusConfig();
  const displayText = customText || statusConfig.text;

  const styles = getStyles(colors, spacing, typography, borderRadius);







  const statusBadgeStyles = [
    styles.base,
    styles[size],
    styles[variant],
    variant === 'filled' && { backgroundColor: statusConfig.backgroundColor },
    variant === 'outlined' && {
      borderColor: statusConfig.color,
      backgroundColor: 'transparent',
    },
    variant === 'subtle' && { backgroundColor: statusConfig.lightBackground },
    variant === 'priority' && { backgroundColor: statusConfig.backgroundColor },
    style,
  ].filter(Boolean);

  const textStyles = [
    styles.text,
    styles[`${size}Text`],
    variant === 'filled' && { color: colors.white },
    variant === 'outlined' && { color: statusConfig.color },
    variant === 'subtle' && { color: statusConfig.color },
    variant === 'priority' && { color: statusConfig.color },
    textStyle,
  ].filter(Boolean);



  return (
    <View style={statusBadgeStyles} testID={testID}>
      <Text style={textStyles}>{displayText}</Text>
    </View>
  );
}





// Verification Badge Component
export interface VerificationBadgeProps {
  verified: boolean;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  style?: ViewStyle;
}

export const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  verified,
  size = 'md',
  showIcon = true,
  style,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const styles = getStyles(colors, spacing, typography, borderRadius);

  if (!verified) return null;



  return (
    <View style={[styles.verificationBadge, styles[size], style]}>
      {showIcon && <Text style={styles.verificationIcon}>✓</Text>}
      <Text style={[styles.verificationText, styles[`${size}Text`]]}>
        Verified
      </Text>
    </View>
  );
};

// Priority Badge Component
export interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high' | 'emergency';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}



export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  priority,
  size = 'md',
  style,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const styles = getStyles(colors, spacing, typography, borderRadius);

  const getPriorityConfig = () => {
    const configs = {
      low: {
        text: 'Low',
        color: colors.gray500,
        backgroundColor: colors.gray100,
      },
      medium: {
        text: 'Medium',
        color: colors.warning.main,
        backgroundColor: '#FEF3C7',
      },
      high: {
        text: 'High',
        color: colors.secondary.main,
        backgroundColor: '#E8F5E8',
      },
      emergency: {
        text: 'EMERGENCY',
        color: colors.white,
        backgroundColor: colors.error.main,
      },
    };

    return configs[priority];
  };

  const priorityConfig = getPriorityConfig();

  const priorityBadgeStyles = [
    styles.base,
    styles[size],
    { backgroundColor: priorityConfig.backgroundColor },
    priority === 'emergency' && styles.emergencyPulse,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${size}Text`],
    { color: priorityConfig.color },
    priority === 'emergency' && styles.emergencyText,
  ];

  return (
    <View style={priorityBadgeStyles}>
      <Text style={textStyles}>{priorityConfig.text}</Text>
    </View>
  );
};


export default StatusBadge;
