// CTRON Home Design System - Technician Card Component
// Card for displaying technician profiles and information

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { StatusBadge, VerificationBadge } from './StatusBadge';

export interface TechnicianData {
  id: string;
  name: string;
  avatar?: string;
  specialization: string;
  rating: number;
  reviewCount: number;
  verified: boolean;
  available: boolean;
  skills: string[];
  hourlyRate?: number;
  distance?: number;
  responseTime?: string;
  completedJobs?: number;
}

export interface TechnicianCardProps {
  technician: TechnicianData;
  onPress?: () => void;
  onContact?: () => void;
  style?: ViewStyle;
  showContactButton?: boolean;
  showDistance?: boolean;
  compact?: boolean;
}

export const TechnicianCard: React.FC<TechnicianCardProps> = ({
  technician,
  onPress,
  onContact,
  style,
  showContactButton = true,
  showDistance = true,
  compact = false,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const getAvailabilityColor = () => {
    return technician.available ? colors.success.main : colors.gray400;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    if (hasHalfStar) {
      stars.push('⭐'); // Using full star for simplicity
    }

    return stars.join('');
  };

  const styles = getStyles(colors, spacing, typography, borderRadius);

  const cardStyles = [
    styles.container,
    technician.verified && styles.verifiedCard,
    technician.available && styles.availableCard,
    compact && styles.compactCard,
    style,
  ];

  const CardContent = (
    <View style={cardStyles}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Image
            source={{
              uri: technician.avatar || `https://i.pravatar.cc/100?u=${technician.id}`,
            }}
            style={styles.avatar}
          />
          <View
            style={[
              styles.availabilityDot,
              { backgroundColor: getAvailabilityColor() },
            ]}
          />
        </View>

        <View style={styles.basicInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.name} numberOfLines={1}>
              {technician.name}
            </Text>
            {technician.verified && (
              <VerificationBadge verified={true} size="sm" />
            )}
          </View>

          <Text style={styles.specialization} numberOfLines={1}>
            {technician.specialization}
          </Text>

          <View style={styles.ratingContainer}>
            <Text style={styles.stars}>{renderStars(technician.rating)}</Text>
            <Text style={styles.ratingText}>
              {technician.rating.toFixed(1)} ({technician.reviewCount} reviews)
            </Text>
          </View>
        </View>

        <View style={styles.statusContainer}>
          <StatusBadge
            status={technician.available ? 'available' : 'offline'}
            size="sm"
          />
        </View>
      </View>

      {!compact && (
        <>
          {/* Skills */}
          <View style={styles.skillsContainer}>
            {technician.skills.slice(0, 3).map((skill, index) => (
              <View key={index} style={styles.skillTag}>
                <Text style={styles.skillText}>{skill}</Text>
              </View>
            ))}
            {technician.skills.length > 3 && (
              <Text style={styles.moreSkills}>
                +{technician.skills.length - 3} more
              </Text>
            )}
          </View>

          {/* Details */}
          <View style={styles.details}>
            {technician.hourlyRate && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Rate:</Text>
                <Text style={styles.detailValue}>£{technician.hourlyRate}/hr</Text>
              </View>
            )}

            {showDistance && technician.distance && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Distance:</Text>
                <Text style={styles.detailValue}>{technician.distance.toFixed(1)} km</Text>
              </View>
            )}

            {technician.responseTime && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Response:</Text>
                <Text style={styles.detailValue}>{technician.responseTime}</Text>
              </View>
            )}

            {technician.completedJobs && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Jobs:</Text>
                <Text style={styles.detailValue}>{technician.completedJobs} completed</Text>
              </View>
            )}
          </View>

          {/* Contact Button */}
          {showContactButton && onContact && (
            <TouchableOpacity
              style={[
                styles.contactButton,
                !technician.available && styles.contactButtonDisabled,
              ]}
              onPress={onContact}
              disabled={!technician.available}
              accessibilityRole="button"
              accessibilityLabel={`Contact ${technician.name}`}
            >
              <Text
                style={[
                  styles.contactButtonText,
                  !technician.available && styles.contactButtonTextDisabled,
                ]}
              >
                {technician.available ? 'Contact' : 'Unavailable'}
              </Text>
            </TouchableOpacity>
          )}
        </>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.95}
        accessibilityRole="button"
        accessibilityLabel={`Technician: ${technician.name}, ${technician.specialization}`}
      >
        {CardContent}
      </TouchableOpacity>
    );
  }

  return CardContent;
};

const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.systemBackground,
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.separator,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
  },

  verifiedCard: {
    borderTopWidth: 4,
    borderTopColor: colors.success.main,
  },

  availableCard: {
    backgroundColor: colors.systemBackground,
  },

  compactCard: {
    padding: spacing.md,
  },

  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },

  avatarContainer: {
    position: 'relative',
    marginRight: spacing.sm,
  },

  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: colors.separator,
  },

  availabilityDot: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.systemBackground,
  },

  basicInfo: {
    flex: 1,
    marginRight: spacing.xs,
  },

  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },

  name: {
    ...typography.title3,
    color: colors.text.primary,
    marginRight: spacing.xs,
  },

  specialization: {
    ...typography.footnote,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },

  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  stars: {
    ...typography.footnote,
    marginRight: spacing.xs,
  },

  ratingText: {
    ...typography.caption2,
    color: colors.tertiaryLabel,
  },

  statusContainer: {
    alignItems: 'flex-end',
  },

  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.md,
  },

  skillTag: {
    backgroundColor: colors.primary.light,
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },

  skillText: {
    ...typography.caption2,
    fontWeight: '500',
    color: colors.primary.dark,
  },

  moreSkills: {
    ...typography.caption2,
    color: colors.tertiaryLabel,
    alignSelf: 'center',
  },

  details: {
    marginBottom: spacing.md,
  },

  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },

  detailLabel: {
    ...typography.footnote,
    fontWeight: '500',
    color: colors.tertiaryLabel,
  },

  detailValue: {
    ...typography.footnote,
    color: colors.text.secondary,
  },

  contactButton: {
    backgroundColor: colors.primary.main,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },

  contactButtonDisabled: {
    backgroundColor: colors.gray300,
  },

  contactButtonText: {
    ...typography.body,
    fontWeight: '600',
    color: colors.white,
  },

  contactButtonTextDisabled: {
    color: colors.gray500,
  },
});

export default TechnicianCard;
