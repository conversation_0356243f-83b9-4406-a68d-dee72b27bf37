// CTRON Home - API Configuration
// Configuration for API endpoints and Socket.IO connection

import Constants from 'expo-constants';

// Get API URL from environment or build dynamically
const getApiUrl = (): string => {
  // Check for explicit API URL first
  const envApiUrl = Constants.expoConfig?.extra?.API_BASE_URL;
  if (envApiUrl && typeof envApiUrl === 'string') {
    return envApiUrl;
  }



  if (__DEV__) {
    // Use EXPO_PUBLIC_API_URL if available, otherwise construct from IP and port
    if (process.env.EXPO_PUBLIC_API_URL) {
      return process.env.EXPO_PUBLIC_API_URL;
    }

    // Fallback to dynamic construction for development
    const devBackendIP = process.env.EXPO_PUBLIC_DEV_SERVER_IP || 'localhost';
    const devBackendPort = process.env.EXPO_PUBLIC_DEV_SERVER_PORT || '3001';
    return `http://${devBackendIP}:${devBackendPort}`;
  }

  // Production URL must be set via environment variables
  throw new Error('Production API URL must be set via EXPO_PUBLIC_API_BASE_URL environment variable');
};

// Get Socket.IO URL (typically same as API URL but different port/path)
const getSocketUrl = (): string => {
  // Check for explicit Socket URL first
  const envSocketUrl = Constants.expoConfig?.extra?.SOCKET_URL;
  if (envSocketUrl) {
    return envSocketUrl;
  }

  if (__DEV__) {
    // Use EXPO_PUBLIC_SOCKET_URL if available, otherwise construct from IP and port
    if (process.env.EXPO_PUBLIC_SOCKET_URL) {
      return process.env.EXPO_PUBLIC_SOCKET_URL;
    }

    // Fallback to dynamic construction for development
    const devBackendIP = process.env.EXPO_PUBLIC_DEV_SERVER_IP || 'localhost';
    const devBackendPort = process.env.EXPO_PUBLIC_DEV_SERVER_PORT || '3001';
    return `http://${devBackendIP}:${devBackendPort}`;
  }

  // Production URL must be set via environment variables
  throw new Error('Production Socket URL must be set via EXPO_PUBLIC_SOCKET_URL environment variable');
};

// Export configuration
export const API_BASE_URL = getApiUrl();
export const SOCKET_URL = getSocketUrl();

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login',
    SIGNUP: '/api/auth/signup',
    REFRESH: '/api/auth/refresh',
    LOGOUT: '/api/auth/logout',
  },

  // Chat endpoints
  CHAT: {
    LIST: '/api/chats',
    CREATE: '/api/chats',
    BY_ID: (id: string) => `/api/chats/${id}`,
    BY_JOB: (jobId: string) => `/api/chats/job/${jobId}`,
    MESSAGES: (chatId: string) => `/api/chats/${chatId}/messages`,
    SEND_MESSAGE: (chatId: string) => `/api/chats/${chatId}/messages`,
    MARK_READ: (chatId: string) => `/api/chats/${chatId}/read`,
    MARK_MESSAGE_READ: (messageId: string) => `/api/chats/messages/${messageId}/read`,
    UPDATE_STATUS: (chatId: string) => `/api/chats/${chatId}/status`,
  },

  // Job endpoints
  JOBS: {
    LIST: '/api/jobs',
    CREATE: '/api/jobs',
    BY_ID: (id: string) => `/api/jobs/${id}`,
    UPDATE: (id: string) => `/api/jobs/${id}`,
    DELETE: (id: string) => `/api/jobs/${id}`,
  },

  // User endpoints
  USERS: {
    PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
  },
} as const;

// Socket.IO events
export const SOCKET_EVENTS = {
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',

  // Chat events - Client to Server
  CHAT_JOIN: 'chat:join',
  CHAT_LEAVE: 'chat:leave',
  CHAT_SEND_MESSAGE: 'chat:sendMessage',
  CHAT_TYPING: 'chat:typing',
  CHAT_MARK_READ: 'chat:markRead',
  CHAT_GET_ONLINE_USERS: 'chat:getOnlineUsers',

  // Chat events - Server to Client
  CHAT_NEW_MESSAGE: 'chat:newMessage',
  CHAT_USER_TYPING: 'chat:userTyping',
  CHAT_MESSAGE_READ: 'chat:messageRead',
  CHAT_ONLINE_USERS: 'chat:onlineUsers',
  CHAT_USER_JOINED: 'chat:userJoined',
  CHAT_USER_LEFT: 'chat:userLeft',
} as const;

// Request timeout configuration
export const REQUEST_TIMEOUT = 10000; // 10 seconds

// API timeout configuration
export const API_TIMEOUT =
  (Constants.expoConfig?.extra as any)?.API_TIMEOUT ||
  process.env.EXPO_PUBLIC_API_TIMEOUT ||
  30000;

// Socket.IO configuration
export const SOCKET_CONFIG = {
  timeout:
    (Constants.expoConfig?.extra as any)?.SOCKET_TIMEOUT ||
    process.env.EXPO_PUBLIC_SOCKET_TIMEOUT ||
    10000,
  reconnection: true,
  reconnectionAttempts:
    (Constants.expoConfig?.extra as any)?.SOCKET_RECONNECT_ATTEMPTS ||
    process.env.EXPO_PUBLIC_SOCKET_RECONNECT_ATTEMPTS ||
    5,
  reconnectionDelay:
    (Constants.expoConfig?.extra as any)?.SOCKET_RECONNECT_DELAY ||
    process.env.EXPO_PUBLIC_SOCKET_RECONNECT_DELAY ||
    1000,
  reconnectionDelayMax:
    (Constants.expoConfig?.extra as any)?.SOCKET_RECONNECT_DELAY_MAX ||
    process.env.EXPO_PUBLIC_SOCKET_RECONNECT_DELAY_MAX ||
    5000,
  transports: ['websocket', 'polling'],
} as const;

// Development configuration
export const DEV_CONFIG = {
  enableLogging: __DEV__,
  enableSocketLogging: __DEV__,
  enableApiLogging: __DEV__,
} as const;

// Export types for TypeScript
export type ApiEndpoints = typeof API_ENDPOINTS;
export type SocketEvents = typeof SOCKET_EVENTS;
