// src/context/AuthContext.tsx
import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import * as SecureStore from 'expo-secure-store';
import { jwtDecode } from 'jwt-decode';
import { Platform } from 'react-native';
import { CommonActions } from '@react-navigation/native';
import Toast from 'react-native-toast-message';

import { setAuthToken } from '../utils/auth.utils';
import { navigationRef } from '../navigation/navigationRef';
import { SocketService } from '../services/socket.service';

export interface UserPayload {
  userId: string;
  fullName: string;
  email: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  exp: number;
  iat: number;
  technicianProfile?: { id: string };
}

interface AuthContextType {
  user: UserPayload | null;
  token: string | null;
  loading: boolean;
  login: (token: string) => Promise<void>;
  logout: (showMessage?: boolean) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);
export { AuthContext };

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserPayload | null>(null);
  const [token, setTokenState] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);





  const logout = useCallback(async (showMessage = true) => {
    console.log('🔐 AuthProvider: Logging out...');
    setUser(null);
    setTokenState(null);
    setAuthToken(null);
    SocketService.disconnect();

    if (Platform.OS === 'web') {
      localStorage.removeItem('token');
    } else {
      await SecureStore.deleteItemAsync('token');
    }

    if (navigationRef.isReady()) {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        })
      );
    }

    if (showMessage) {
      Toast.show({
        type: 'info',
        text1: 'Logged Out',
        text2: 'You have been successfully logged out.',
      });
    }
  }, []);



  const navigateToRoleDashboard = useCallback((role: UserPayload['role']) => {
    if (!navigationRef.isReady()) {
      console.warn('Navigation ref not ready during role-based navigation.');
      return false;
    }

    let routeName: string | null = null;
    switch (role) {
      case 'HOMEOWNER':
        routeName = 'HomeownerDashboard';
        break;
      case 'TECHNICIAN':
        routeName = 'TechnicianDashboard';
        break;
      case 'ADMIN':
        routeName = 'AdminDashboard';
        break;
      default:
        console.warn('Unknown role:', role);
        return false;
    }

    const currentRoute = navigationRef.getCurrentRoute();
    if (currentRoute && currentRoute.name === routeName) {
      console.log(`Already on ${routeName}. No navigation needed.`);
      return true;
    }

    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: routeName }],
      })
    );
    return true;
  }, []);

  const bootstrap = useCallback(async () => {
    console.log('🔐 AuthProvider: Bootstrap function called, Platform:', Platform.OS);
    try {
      // Handle web platform authentication
      if (Platform.OS === 'web') {
        console.log('🌐 AuthProvider: Handling web platform authentication');
        try {
          const webToken = localStorage.getItem('token');
          console.log('🌐 AuthProvider: Web token from localStorage:', webToken ? 'Found' : 'Not found');
          if (webToken) {
            const decoded = jwtDecode<UserPayload>(webToken);

            if (__DEV__) {
              console.log('🌐 Web token decoded successfully for user:', decoded.userId);
            }

            if (!decoded.role) {
              console.warn('❌ Web token is missing role. Clearing...');
              localStorage.removeItem('token');
              setLoading(false);
              return;
            }

            if (decoded.exp * 1000 < Date.now()) {
              console.warn('⚠️ Web token expired. Clearing...');
              localStorage.removeItem('token');
              setLoading(false);
              return;
            }

            // Valid web token found
            setUser(decoded);
            setTokenState(webToken);
            setAuthToken(webToken);

            const success = navigateToRoleDashboard(decoded.role);
            if (!success) {
              console.warn('🚫 Web role mismatch detected. Logging out...');
              await logout(true);
            }
          }
        } catch (error) {
          console.error('🔴 Web auth bootstrap failed:', error);
          localStorage.removeItem('token');
        }
        setLoading(false);
        return;
      }

      const storedToken = await SecureStore.getItemAsync('token');
      if (!storedToken) {
        setLoading(false);
        return;
      }

      const decoded = jwtDecode<UserPayload>(storedToken);
      console.log('🧾 Decoded token payload (bootstrap):', decoded);

      if (decoded.exp * 1000 < Date.now()) {
        console.warn('⚠️ Token expired. Clearing...');
        await SecureStore.deleteItemAsync('token');
        setLoading(false);
        return;
      }

      setUser(decoded);
      setTokenState(storedToken);
      setAuthToken(storedToken);

      const success = navigateToRoleDashboard(decoded.role);
      if (!success) {
        console.warn('🚫 Role mismatch detected. Logging out...');
        await logout(true);
      }
    } catch (error) {
      console.error('🔴 Auth bootstrap failed:', error);
      await SecureStore.deleteItemAsync('token');
    } finally {
      setLoading(false);
    }
  }, [logout, navigateToRoleDashboard]);

  useEffect(() => {
    console.log('🔐 AuthProvider: Starting bootstrap...');
    bootstrap();
  }, [bootstrap]);

  const login = useCallback(async (newToken: string) => {
    try {
      const decoded = jwtDecode<UserPayload>(newToken);
      setUser(decoded);
      setTokenState(newToken);
      setAuthToken(newToken);

      if (Platform.OS === 'web') {
        localStorage.setItem('token', newToken);
      } else {
        await SecureStore.setItemAsync('token', newToken);
      }

      navigateToRoleDashboard(decoded.role);
    } catch (error) {
      console.error('🔴 Login failed:', error);
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: 'Invalid credentials or token.',
      });
      await logout();
    }
  }, [logout, navigateToRoleDashboard]);


  return (
    <AuthContext.Provider value={{ user, token, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('❌ useAuth must be used inside an <AuthProvider>');
  }
  return context;
};
