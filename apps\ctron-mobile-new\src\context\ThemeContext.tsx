import React, { createContext, useContext, ReactNode } from 'react';
import { lightTheme, Theme } from '../styles/theme';



// Define the ThemeContext
const ThemeContext = createContext<Theme | undefined>(undefined);

// Create a ThemeProvider component
interface ThemeProviderProps {
  children: ReactNode;
}


export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  return (
    <ThemeContext.Provider value={lightTheme}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};