import { useEffect, useState, useCallback, useRef } from 'react';
import { Platform } from 'react-native';

// AccessibilityInfo is not available on web
const AccessibilityInfo = Platform.OS !== 'web' ? require('react-native').AccessibilityInfo : null;
import {
  accessibilityManager,
  announce,
  isScreenReaderEnabled,
  isReduceMotionEnabled,
  createAccessibilityProps,
  createInputA11yProps,
  createButtonA11yProps,
  createCardA11yProps,
  AccessibilityProps
} from '../utils/accessibility';

interface UseAccessibilityOptions {
  announceOnMount?: string;
  announceOnUnmount?: string;
  trackFocus?: boolean;
}

/**
 * Hook for managing accessibility features in components
 */
export const useAccessibility = (options: UseAccessibilityOptions = {}) => {
  const { announceOnMount, announceOnUnmount, trackFocus = false } = options;
  const [screenReaderEnabled, setScreenReaderEnabled] = useState(false);
  const [reduceMotionEnabled, setReduceMotionEnabled] = useState(false);
  const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize accessibility state
  useEffect(() => {
    const initializeAccessibility = async () => {
      setScreenReaderEnabled(isScreenReaderEnabled());
      setReduceMotionEnabled(isReduceMotionEnabled());
    };

    initializeAccessibility();

    // Set up listeners for accessibility changes
    const handleScreenReaderChange = (enabled: boolean) => {
      setScreenReaderEnabled(enabled);
    };

    const handleReduceMotionChange = (enabled: boolean) => {
      setReduceMotionEnabled(enabled);
    };

    if (Platform.OS !== 'web') {
      AccessibilityInfo.addEventListener('screenReaderChanged', handleScreenReaderChange);
      AccessibilityInfo.addEventListener('reduceMotionChanged', handleReduceMotionChange);

      return () => {
        AccessibilityInfo.removeEventListener('screenReaderChanged', handleScreenReaderChange);
        AccessibilityInfo.removeEventListener('reduceMotionChanged', handleReduceMotionChange);
      };
    }
  }, []);

  // Handle mount/unmount announcements
  useEffect(() => {
    if (announceOnMount) {
      // Delay announcement to ensure component is fully rendered
      const timer = setTimeout(() => {
        announce(announceOnMount, 'medium');
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [announceOnMount]);

  useEffect(() => {
    return () => {
      if (announceOnUnmount) {
        announce(announceOnUnmount, 'low');
      }
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
    };
  }, [announceOnUnmount]);

  /**
   * Announce text to screen readers
   */
  const announceText = useCallback((text: string, priority: 'low' | 'medium' | 'high' = 'medium') => {
    announce(text, priority);
  }, []);

  /**
   * Announce with delay (useful for state changes)
   */
  const announceDelayed = useCallback((text: string, delay: number = 500, priority: 'low' | 'medium' | 'high' = 'medium') => {
    setTimeout(() => {
      announce(text, priority);
    }, delay);
  }, []);

  /**
   * Focus management for accessibility
   */
  const setAccessibilityFocus = useCallback((ref: React.RefObject<any>) => {
    if (Platform.OS !== 'web' && ref.current) {
      AccessibilityInfo.setAccessibilityFocus(ref.current);
    } else if (Platform.OS === 'web' && ref.current) {
      // Web focus management
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }

      focusTimeoutRef.current = setTimeout(() => {
        ref.current?.focus();
      }, 100);
    }
  }, []);

  /**
   * Get animation duration based on reduce motion preference
   */
  const getAnimationDuration = useCallback((normalDuration: number, reducedDuration: number = 0) => {
    return reduceMotionEnabled ? reducedDuration : normalDuration;
  }, [reduceMotionEnabled]);

  /**
   * Check if animations should be enabled
   */
  const shouldAnimate = useCallback(() => {
    return !reduceMotionEnabled;
  }, [reduceMotionEnabled]);

  /**
   * Generate accessibility props for form inputs
   */
  const getInputAccessibilityProps = useCallback((config: {
    label: string;
    value?: string;
    placeholder?: string;
    error?: string;
    required?: boolean;
    testID?: string;
  }) => {
    return createInputA11yProps(config);
  }, []);

  /**
   * Generate accessibility props for buttons
   */
  const getButtonAccessibilityProps = useCallback((config: {
    label: string;
    hint?: string;
    disabled?: boolean;
    loading?: boolean;
    pressed?: boolean;
    testID?: string;
  }) => {
    return createButtonA11yProps(config);
  }, []);

  /**
   * Generate accessibility props for cards/list items
   */
  const getCardAccessibilityProps = useCallback((config: {
    title: string;
    subtitle?: string;
    description?: string;
    actionHint?: string;
    testID?: string;
  }) => {
    return createCardA11yProps(config);
  }, []);

  /**
   * Generate general accessibility props
   */
  const getAccessibilityProps = useCallback((config: {
    label: string;
    hint?: string;
    role?: AccessibilityProps['accessibilityRole'];
    state?: AccessibilityProps['accessibilityState'];
    value?: AccessibilityProps['accessibilityValue'];
    actions?: AccessibilityProps['accessibilityActions'];
    testID?: string;
  }) => {
    return createAccessibilityProps(config);
  }, []);

  /**
   * Handle accessibility action events
   */
  const handleAccessibilityAction = useCallback((
    event: { nativeEvent: { actionName: string } },
    actions: Record<string, () => void>
  ) => {
    const actionName = event.nativeEvent.actionName;
    const action = actions[actionName];
    if (action) {
      action();
    }
  }, []);

  return {
    // State
    screenReaderEnabled,
    reduceMotionEnabled,

    // Functions
    announceText,
    announceDelayed,
    setAccessibilityFocus,
    getAnimationDuration,
    shouldAnimate,

    // Accessibility props generators
    getInputAccessibilityProps,
    getButtonAccessibilityProps,
    getCardAccessibilityProps,
    getAccessibilityProps,
    handleAccessibilityAction,
  };
};

/**
 * Hook specifically for form accessibility
 */
export const useFormAccessibility = () => {
  const {
    announceText,
    getInputAccessibilityProps,
    getButtonAccessibilityProps
  } = useAccessibility();

  /**
   * Announce form validation errors
   */
  const announceFormError = useCallback((fieldName: string, error: string) => {
    announceText(`${fieldName}: ${error}`, 'high');
  }, [announceText]);

  /**
   * Announce form submission status
   */
  const announceFormStatus = useCallback((status: 'submitting' | 'success' | 'error', message?: string) => {
    const statusMessages = {
      submitting: 'Form is being submitted, please wait',
      success: message || 'Form submitted successfully',
      error: message || 'Form submission failed, please check for errors',
    };

    announceText(statusMessages[status], status === 'error' ? 'high' : 'medium');
  }, [announceText]);

  /**
   * Get accessibility props for form fields with error handling
   */
  const getFieldAccessibilityProps = useCallback((config: {
    label: string;
    value?: string;
    placeholder?: string;
    error?: string;
    required?: boolean;
    fieldName?: string;
    testID?: string;
  }) => {
    const props = getInputAccessibilityProps(config);

    // Add error announcement on error change
    if (config.error && config.fieldName) {
      setTimeout(() => {
        announceFormError(config.fieldName!, config.error!);
      }, 100);
    }

    return props;
  }, [getInputAccessibilityProps, announceFormError]);

  return {
    announceFormError,
    announceFormStatus,
    getFieldAccessibilityProps,
    getButtonAccessibilityProps,
  };
};

/**
 * Hook for navigation accessibility
 */
export const useNavigationAccessibility = () => {
  const { announceText, setAccessibilityFocus } = useAccessibility();

  /**
   * Announce screen navigation
   */
  const announceScreenChange = useCallback((screenName: string, description?: string) => {
    const message = description
      ? `Navigated to ${screenName}. ${description}`
      : `Navigated to ${screenName}`;

    announceText(message, 'medium');
  }, [announceText]);

  /**
   * Focus on screen header after navigation
   */
  const focusScreenHeader = useCallback((headerRef: React.RefObject<any>) => {
    setTimeout(() => {
      setAccessibilityFocus(headerRef);
    }, 500);
  }, [setAccessibilityFocus]);

  return {
    announceScreenChange,
    focusScreenHeader,
  };
};

export default useAccessibility;
