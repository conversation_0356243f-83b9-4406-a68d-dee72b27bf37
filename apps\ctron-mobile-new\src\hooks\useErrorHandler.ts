import { useCallback, useEffect, useRef } from 'react';
import { errorHandler } from '../utils/errorHandler';

interface UseErrorHandlerOptions {
  context?: string;
  onRetry?: () => void;
  enableRetry?: boolean;
}

/**
 * Hook for consistent error handling across components
 */
export const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {
  const { context, onRetry, enableRetry = false } = options;
  const contextRef = useRef(context);

  // Update context ref when context changes
  useEffect(() => {
    contextRef.current = context;
  }, [context]);

  // Register retry callback if provided
  useEffect(() => {
    if (enableRetry && onRetry && context) {
      errorHandler.registerRetryCallback(context, onRetry);
      
      return () => {
        errorHandler.unregisterRetryCallback(context);
      };
    }
  }, [context, onRetry, enableRetry]);

  /**
   * Handle API errors with context
   */
  const handleApiError = useCallback((error: any) => {
    return errorHandler.handleApiError(error, contextRef.current);
  }, []);

  /**
   * Handle network errors with context
   */
  const handleNetworkError = useCallback((error: any) => {
    return errorHandler.handleNetworkError(error, contextRef.current);
  }, []);

  /**
   * Handle validation errors with context
   */
  const handleValidationError = useCallback((error: any) => {
    return errorHandler.handleValidationError(error, contextRef.current);
  }, []);

  /**
   * Handle authentication errors with context
   */
  const handleAuthError = useCallback((error: any) => {
    return errorHandler.handleAuthError(error, contextRef.current);
  }, []);

  /**
   * Handle unexpected errors with context
   */
  const handleUnexpectedError = useCallback((error: any) => {
    return errorHandler.handleUnexpectedError(error, contextRef.current);
  }, []);

  /**
   * Generic error handler that determines error type
   */
  const handleError = useCallback((error: any) => {
    return errorHandler.handleError(error, contextRef.current);
  }, []);

  /**
   * Wrapper for async operations with automatic error handling
   */
  const withErrorHandling = useCallback(
    <T>(asyncOperation: () => Promise<T>) => {
      return async (): Promise<T | null> => {
        try {
          return await asyncOperation();
        } catch (error) {
          handleError(error);
          return null;
        }
      };
    },
    [handleError]
  );

  /**
   * Wrapper for sync operations with automatic error handling
   */
  const withSyncErrorHandling = useCallback(
    <T>(operation: () => T, fallback: T) => {
      return (): T => {
        try {
          return operation();
        } catch (error) {
          handleError(error);
          return fallback;
        }
      };
    },
    [handleError]
  );

  return {
    handleApiError,
    handleNetworkError,
    handleValidationError,
    handleAuthError,
    handleUnexpectedError,
    handleError,
    withErrorHandling,
    withSyncErrorHandling,
  };
};

export default useErrorHandler;
