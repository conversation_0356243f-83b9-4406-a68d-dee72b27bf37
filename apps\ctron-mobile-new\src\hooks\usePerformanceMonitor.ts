import { useEffect, useRef, useCallback } from 'react';
import { performanceMonitor } from '../utils/performanceMonitor';

interface UsePerformanceMonitorOptions {
  componentName?: string;
  trackRenders?: boolean;
  trackMemory?: boolean;
  memoryInterval?: number;
}

/**
 * Hook for monitoring component performance
 */
export const usePerformanceMonitor = (options: UsePerformanceMonitorOptions = {}) => {
  const {
    componentName = 'UnknownComponent',
    trackRenders = true,
    trackMemory = false,
    memoryInterval = 5000,
  } = options;

  const renderCountRef = useRef(0);
  const mountTimeRef = useRef<number | null>(null);
  const memoryIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Track component mount time
  useEffect(() => {
    if (trackRenders) {
      mountTimeRef.current = Date.now();
      performanceMonitor.startMeasure(`${componentName}_mount`);
      
      return () => {
        if (mountTimeRef.current) {
          performanceMonitor.endMeasure(`${componentName}_mount`);
        }
      };
    }
  }, [componentName, trackRenders]);

  // Track render count
  useEffect(() => {
    if (trackRenders) {
      renderCountRef.current += 1;
      
      if (__DEV__ && renderCountRef.current > 10) {
        console.warn(`⚠️ Performance: ${componentName} has rendered ${renderCountRef.current} times`);
      }
    }
  });

  // Track memory usage
  useEffect(() => {
    if (trackMemory) {
      // Take initial memory snapshot
      performanceMonitor.takeMemorySnapshot();
      
      // Set up periodic memory monitoring
      memoryIntervalRef.current = setInterval(() => {
        performanceMonitor.takeMemorySnapshot();
      }, memoryInterval);

      return () => {
        if (memoryIntervalRef.current) {
          clearInterval(memoryIntervalRef.current);
        }
      };
    }
  }, [trackMemory, memoryInterval]);

  /**
   * Measure an async operation
   */
  const measureAsync = useCallback(
    async <T>(operationName: string, operation: () => Promise<T>): Promise<T> => {
      return performanceMonitor.measureAsync(
        `${componentName}_${operationName}`,
        operation,
        { component: componentName }
      );
    },
    [componentName]
  );

  /**
   * Measure a sync operation
   */
  const measureSync = useCallback(
    <T>(operationName: string, operation: () => T): T => {
      return performanceMonitor.measureSync(
        `${componentName}_${operationName}`,
        operation,
        { component: componentName }
      );
    },
    [componentName]
  );

  /**
   * Start measuring a custom metric
   */
  const startMeasure = useCallback(
    (metricName: string, metadata?: Record<string, any>) => {
      performanceMonitor.startMeasure(
        `${componentName}_${metricName}`,
        { component: componentName, ...metadata }
      );
    },
    [componentName]
  );

  /**
   * End measuring a custom metric
   */
  const endMeasure = useCallback(
    (metricName: string) => {
      return performanceMonitor.endMeasure(`${componentName}_${metricName}`);
    },
    [componentName]
  );

  /**
   * Get component performance stats
   */
  const getStats = useCallback(() => {
    const mountTime = mountTimeRef.current ? Date.now() - mountTimeRef.current : 0;
    
    return {
      renderCount: renderCountRef.current,
      mountTime,
      componentName,
    };
  }, [componentName]);

  return {
    measureAsync,
    measureSync,
    startMeasure,
    endMeasure,
    getStats,
    renderCount: renderCountRef.current,
  };
};

/**
 * Hook for monitoring API call performance
 */
export const useApiPerformanceMonitor = () => {
  const trackApiCall = useCallback(
    (url: string, method: string = 'GET') => {
      return performanceMonitor.trackNetworkRequest(url, method);
    },
    []
  );

  const measureApiCall = useCallback(
    async <T>(
      name: string,
      apiCall: () => Promise<T>,
      url?: string,
      method?: string
    ): Promise<T> => {
      const networkTracker = url ? trackApiCall(url, method) : null;
      
      try {
        const result = await performanceMonitor.measureAsync(
          `api_${name}`,
          apiCall,
          { type: 'api_call', url, method }
        );
        
        networkTracker?.end(200);
        return result;
      } catch (error: any) {
        const status = error.response?.status || 0;
        networkTracker?.end(status, undefined, error.message);
        throw error;
      }
    },
    [trackApiCall]
  );

  return {
    trackApiCall,
    measureApiCall,
  };
};

/**
 * Hook for monitoring screen performance
 */
export const useScreenPerformanceMonitor = (screenName: string) => {
  const { measureAsync, measureSync, startMeasure, endMeasure, getStats } = usePerformanceMonitor({
    componentName: `Screen_${screenName}`,
    trackRenders: true,
    trackMemory: true,
    memoryInterval: 10000, // 10 seconds for screens
  });

  // Track screen navigation time
  useEffect(() => {
    startMeasure('navigation_time');
    
    // End measurement after a short delay to capture initial render
    const timer = setTimeout(() => {
      endMeasure('navigation_time');
    }, 100);

    return () => clearTimeout(timer);
  }, [startMeasure, endMeasure]);

  /**
   * Track user interaction performance
   */
  const trackInteraction = useCallback(
    (interactionName: string, interaction: () => void) => {
      return measureSync(`interaction_${interactionName}`, interaction);
    },
    [measureSync]
  );

  /**
   * Track async user interaction performance
   */
  const trackAsyncInteraction = useCallback(
    (interactionName: string, interaction: () => Promise<any>) => {
      return measureAsync(`interaction_${interactionName}`, interaction);
    },
    [measureAsync]
  );

  return {
    measureAsync,
    measureSync,
    startMeasure,
    endMeasure,
    getStats,
    trackInteraction,
    trackAsyncInteraction,
  };
};

export default usePerformanceMonitor;
