import { Platform } from 'react-native';

import { useState, useEffect } from 'react';

export const useStripe = (...args: any[]) => {
  const [stripeHook, setStripeHook] = useState<any>(null);

  useEffect(() => {
    const loadStripeModule = async () => {
      if (Platform.OS === 'web') {
        const { useStripe: webUseStripe } = await import('./useStripe.web');
        setStripeHook(() => webUseStripe);
      } else {
        const { useStripe: nativeUseStripe } = await import('./useStripe.native');
        setStripeHook(() => nativeUseStripe);
      }
    };

    loadStripeModule();
  }, []);

  if (!stripeHook) {
    return () => {
      console.warn('Stripe hook is not yet loaded. Ensure your component handles the loading state.');
      return {};
    };
  }

  return stripeHook(...args);
};