// src/navigation/AdminStack.tsx

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AdminDashboardScreen from '../screens/Admin/AdminDashboardScreen';
import AdminJobsScreen from '../screens/Admin/AdminJobsScreen';
import AdminTechniciansScreen from '../screens/Admin/AdminTechniciansScreen';
import AdminSettingsScreen from '../screens/Admin/AdminSettingsScreen';
import AdminJobDetailsScreen from '../screens/Admin/AdminJobDetailsScreen';
import ChatListScreen from '../screens/Chat/ChatListScreen';
import ChatScreen from '../screens/Chat/ChatScreen';

export type AdminStackParamList = {
  AdminDashboard: undefined;
  AdminJobs: undefined;
  AdminTechnicians: undefined;
  AdminSettings: undefined;
  AdminJobDetails: { jobId: string };
  ChatList: undefined;
  Chat: { chatId: string; jobTitle: string };
};

const Stack = createNativeStackNavigator<AdminStackParamList>();

export default function AdminStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="AdminDashboard" component={AdminDashboardScreen} />
      <Stack.Screen name="AdminJobs" component={AdminJobsScreen} />
      <Stack.Screen name="AdminTechnicians" component={AdminTechniciansScreen} />
      <Stack.Screen name="AdminSettings" component={AdminSettingsScreen} />
      <Stack.Screen name="AdminJobDetails" component={AdminJobDetailsScreen} />
      <Stack.Screen name="ChatList" component={ChatListScreen} />
      <Stack.Screen name="Chat" component={ChatScreen} />
    </Stack.Navigator>
  );
}
