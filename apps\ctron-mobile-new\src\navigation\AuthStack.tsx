// src/navigation/AuthStack.tsx

import React, { lazy, Suspense } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { View, ActivityIndicator } from 'react-native';
import { useTheme } from '../hooks/useTheme';

// Lazy load screens for better bundle splitting
const LoginScreen = lazy(() => import('../screens/Auth/LoginScreen'));
const SignupScreen = lazy(() => import('../screens/Auth/SignupScreen'));

const Stack = createNativeStackNavigator();

// Loading component for lazy-loaded screens
const LoadingScreen = () => {
  const { colors } = useTheme();
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    }}>
      <ActivityIndicator size="large" color={colors.primary.main} />
    </View>
  );
};

// Wrapper components with Suspense
const LazyLoginScreen = () => (
  <Suspense fallback={<LoadingScreen />}>
    <LoginScreen />
  </Suspense>
);

const LazySignupScreen = () => (
  <Suspense fallback={<LoadingScreen />}>
    <SignupScreen />
  </Suspense>
);

export default function AuthStack() {
  if (__DEV__) {
    console.log('🔐 AuthStack rendering');
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Login" component={LazyLoginScreen} />
      <Stack.Screen name="Signup" component={LazySignupScreen} />
    </Stack.Navigator>
  );
}
