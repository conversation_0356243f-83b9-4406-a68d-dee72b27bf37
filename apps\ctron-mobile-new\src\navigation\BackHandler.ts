//src/navigation/Backhandler.ts
// BackHandler is not available in React Native web, using a polyfill
const RNBackHandler = {
  addEventListener: () => ({ remove: () => { } }),
  removeEventListener: () => { },
  exitApp: () => { },
};
import { navigationRef } from './navigationRef';

export const BackHandler = {
  attach: () => {
    // BackHandler addEventListener not available in web, using mock
    const subscription = { remove: () => { } };
    return subscription;
  },
};
