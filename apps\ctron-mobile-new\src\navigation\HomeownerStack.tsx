//src/navigation/HomownerStack.tsx

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { HomeownerStackParamList } from './types';

import HomeScreen from '../screens/Homeowner/HomeScreen';
import MyJobsScreen from '../screens/Homeowner/MyJobsScreen';
import JobDetailsScreen from '../screens/Homeowner/JobDetailsScreen';
import EditJobScreen from '../screens/Homeowner/EditJobScreen';
import PaymentScreen from '../screens/Homeowner/PaymentScreen';
import BookJobScreen from '../screens/Homeowner/BookJobScreen';
import ChatListScreen from '../screens/Chat/ChatListScreen';
import ChatScreen from '../screens/Chat/ChatScreen';

const Stack = createNativeStackNavigator<HomeownerStackParamList>();

const HomeownerStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Home" component={HomeScreen} />
    <Stack.Screen name="MyJobs" component={MyJobsScreen} />
    <Stack.Screen name="JobDetails" component={JobDetailsScreen} />
    <Stack.Screen name="EditJob" component={EditJobScreen} />
    <Stack.Screen name="Payment" component={PaymentScreen} />
    <Stack.Screen name="BookJob" component={BookJobScreen} />
    <Stack.Screen name="ChatList" component={ChatListScreen} />
    <Stack.Screen name="Chat" component={ChatScreen} />
  </Stack.Navigator>
);

export default HomeownerStack;
