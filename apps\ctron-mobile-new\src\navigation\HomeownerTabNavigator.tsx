// CTRON Home - Homeowner Tab Navigator
// Bottom tab navigation for homeowner screens

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, StyleSheet } from 'react-native';

import { useTheme } from '../context/ThemeContext';

// Import screens
import HomeScreen from '../screens/Homeowner/HomeScreen';
import MyJobsScreen from '../screens/Homeowner/MyJobsScreen';
import BookJobScreen from '../screens/Homeowner/BookJobScreen';
import ChatListScreen from '../screens/Chat/ChatListScreen';



const Tab = createBottomTabNavigator();

export const HomeownerTabNavigator = () => {
  const { colors, spacing } = useTheme();

  const styles = StyleSheet.create({
    tabBar: {
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
      paddingTop: spacing.sm,
      paddingBottom: spacing.sm,
      height: 80,
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },

    tabBarItem: {
      paddingVertical: spacing.xs,
    },

    tabBarLabel: {
      fontSize: 12,
      fontWeight: '500',
      marginTop: spacing.xs,
    },

    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.xs,
    },

    iconContainerActive: {
      backgroundColor: `${colors.primary[900]}15`, // 15% opacity
    },

    icon: {
      fontSize: 20,
      color: colors.text.secondary,
    },

    iconActive: {
      color: colors.primary.main,
    },
  });

  // Icon components with access to styles
  const HomeIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>🏠</Text>
    </View>
  );

  const JobsIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>📋</Text>
    </View>
  );

  const BookIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>➕</Text>
    </View>
  );

  const ChatIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>💬</Text>
    </View>
  );

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: colors.primary[900],
        tabBarInactiveTintColor: colors.text.tertiary,
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarItemStyle: styles.tabBarItem,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
          tabBarIcon: ({ focused }) => <HomeIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="MyJobs"
        component={MyJobsScreen}
        options={{
          tabBarLabel: 'My Jobs',
          tabBarIcon: ({ focused }) => <JobsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="BookJob"
        component={BookJobScreen}
        options={{
          tabBarLabel: 'Book',
          tabBarIcon: ({ focused }) => <BookIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Messages"
        component={ChatListScreen}
        options={{
          tabBarLabel: 'Messages',
          tabBarIcon: ({ focused }) => <ChatIcon focused={focused} />,
        }}
      />
    </Tab.Navigator>
  );
};

export default HomeownerTabNavigator;
