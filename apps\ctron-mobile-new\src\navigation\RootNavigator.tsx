// src/navigation/RootNavigator.tsx
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainer } from '@react-navigation/native';
import { View, ActivityIndicator, Text, Image, StyleSheet, Dimensions } from 'react-native';
import { navigationRef } from './navigationRef';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';

// Navigation Stacks
import AuthStack from './AuthStack';
import HomeownerStack from './HomeownerStack';
import TechnicianStack from './TechnicianStack';
import AdminStack from './AdminStack';

// Types for the routes
export type RootStackParamList = {
  Auth: undefined;
  HomeownerStack: undefined;
  TechnicianStack: undefined;
  AdminStack: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

// Get screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Ensure proper default export
const RootNavigator = () => {
  const { colors } = useTheme();
  const { user, loading } = useAuth();

  // Debug logging
  if (__DEV__) {
    console.log('🧭 RootNavigator render - Loading:', loading, 'User:', user ? `${user.role} (${(user as any).id || user.userId})` : 'null');
  }

  // Safety timeout for loading state (prevent infinite loading)
  React.useEffect(() => {
    if (loading) {
      const timeout = setTimeout(() => {
        if (__DEV__) {
          console.warn('⚠️ Loading timeout reached - this might indicate an issue with AuthContext');
        }
      }, 10000); // 10 second timeout

      return () => clearTimeout(timeout);
    }
    return undefined;
  }, [loading]);

  // Show beautiful loading screen while checking authentication
  if (loading) {
    if (__DEV__) {
      console.log('⏳ RootNavigator showing loading screen');
    }

    const styles = StyleSheet.create({
      loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.primary[50],
        paddingHorizontal: 40,
      },
      backgroundGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: colors.background.primary,
        opacity: 0.95,
      },
      logoContainer: {
        width: 140,
        height: 140,
        borderRadius: 70,
        backgroundColor: colors.background.primary,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 32,
        shadowColor: colors.primary[900],
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 12,
      },
      logoImage: {
        width: 100,
        height: 100,
      },
      brandName: {
        fontSize: 48,
        fontWeight: '800',
        color: colors.primary[900],
        marginBottom: 16,
        letterSpacing: 3,
        textAlign: 'center',
      },
      loadingText: {
        fontSize: 18,
        fontWeight: '500',
        color: colors.text.secondary,
        marginBottom: 32,
        textAlign: 'center',
      },
      spinner: {
        transform: [{ scale: 1.5 }],
      },
    });

    return (
      <View style={styles.loadingContainer}>
        <View style={styles.backgroundGradient} />

        {/* Logo Section */}
        <View style={styles.logoContainer}>
          {/* Temporarily remove image to test if it's causing issues */}
          <Text style={{ fontSize: 64, color: colors.primary[900] }}>🏠</Text>
        </View>

        {/* Brand Name */}
        <Text style={styles.brandName}>CTRON</Text>

        {/* Loading Text */}
        <Text style={styles.loadingText}>Loading your experience...</Text>

        {/* Loading Spinner */}
        <ActivityIndicator
          size="large"
          color={colors.primary.main}
          style={styles.spinner}
        />
      </View>
    );
  }

  // Determine initial route
  const initialRoute = user ?
    (user.role === 'TECHNICIAN' ? 'TechnicianStack' :
      user.role === 'ADMIN' ? 'AdminStack' : 'HomeownerStack')
    : 'Auth';

  if (__DEV__) {
    console.log('🧭 RootNavigator initial route:', initialRoute);
  }

  return (
    <ErrorBoundary>
      <NavigationContainer ref={navigationRef}>
        <Stack.Navigator
          screenOptions={{ headerShown: false }}
          initialRouteName={initialRoute}
        >
          <Stack.Screen name="Auth" component={AuthStack} />
          <Stack.Screen name="HomeownerStack" component={HomeownerStack} />
          <Stack.Screen name="TechnicianStack" component={TechnicianStack} />
          <Stack.Screen name="AdminStack" component={AdminStack} />
        </Stack.Navigator>
      </NavigationContainer>
    </ErrorBoundary>
  );
};

export default RootNavigator;
