// src/polyfills/BackHandler-web.js
// Web polyfill for React Native BackHandler utility

import { Platform } from 'react-native';

/**
 * Web implementation of BackHandler using browser history API
 * Provides the same API as React Native's BackHandler utility
 */

const BackHandler = {
  /**
   * Add event listener for hardware back button
   */
  addEventListener: (eventName, handler) => {
    if (Platform.OS !== 'web') {
      return { remove: () => {} };
    }

    if (eventName === 'hardwareBackPress') {
      // Store the handler for web back button simulation
      if (!BackHandler._listeners) {
        BackHandler._listeners = [];
      }
      
      BackHandler._listeners.push(handler);
      
      // Set up browser back button handling
      const handlePopState = () => {
        // Call all registered handlers
        let handled = false;
        for (const listener of BackHandler._listeners) {
          if (listener()) {
            handled = true;
            break;
          }
        }
        
        // If no handler consumed the event, allow default behavior
        if (!handled) {
          // Let the browser handle the back navigation
          return;
        } else {
          // Prevent the back navigation
          window.history.pushState(null, '', window.location.href);
        }
      };

      // Add the popstate listener if it's the first handler
      if (BackHandler._listeners.length === 1) {
        window.addEventListener('popstate', handlePopState);
        // Push a state to enable back button detection
        window.history.pushState(null, '', window.location.href);
      }

      // Return subscription object
      return {
        remove: () => {
          const index = BackHandler._listeners.indexOf(handler);
          if (index > -1) {
            BackHandler._listeners.splice(index, 1);
          }
          
          // Remove popstate listener if no more handlers
          if (BackHandler._listeners.length === 0) {
            window.removeEventListener('popstate', handlePopState);
          }
        },
      };
    }

    // For other event types, return empty subscription
    return { remove: () => {} };
  },

  /**
   * Remove event listener
   */
  removeEventListener: (eventName, handler) => {
    if (Platform.OS !== 'web' || !BackHandler._listeners) {
      return;
    }

    const index = BackHandler._listeners.indexOf(handler);
    if (index > -1) {
      BackHandler._listeners.splice(index, 1);
    }
  },

  /**
   * Exit the application (web implementation)
   */
  exitApp: () => {
    if (Platform.OS === 'web') {
      // On web, we can't actually exit the app, but we can close the tab
      if (window.confirm('Are you sure you want to close this application?')) {
        window.close();
      }
    }
  },

  /**
   * Check if back handler is supported
   */
  isSupported: () => {
    return Platform.OS === 'web' && typeof window !== 'undefined' && window.history;
  },

  /**
   * Get the number of registered listeners
   */
  getListenerCount: () => {
    return BackHandler._listeners ? BackHandler._listeners.length : 0;
  },

  /**
   * Clear all listeners
   */
  clearListeners: () => {
    if (BackHandler._listeners) {
      BackHandler._listeners.length = 0;
    }
  },

  /**
   * Simulate back button press (for testing)
   */
  simulateBackPress: () => {
    if (Platform.OS === 'web' && BackHandler._listeners) {
      for (const listener of BackHandler._listeners) {
        if (listener()) {
          return true; // Handler consumed the event
        }
      }
    }
    return false; // No handler consumed the event
  },
};

// Initialize listeners array
BackHandler._listeners = [];

// Export as default for compatibility
export default BackHandler;

// Also export named exports for compatibility
export const addEventListener = BackHandler.addEventListener;
export const removeEventListener = BackHandler.removeEventListener;
export const exitApp = BackHandler.exitApp;
export const isSupported = BackHandler.isSupported;
export const getListenerCount = BackHandler.getListenerCount;
export const clearListeners = BackHandler.clearListeners;
export const simulateBackPress = BackHandler.simulateBackPress;

// Web-specific utilities
export const webBackHandlerUtils = {
  /**
   * Set up custom back button behavior
   */
  setupCustomBackBehavior: (callback) => {
    return BackHandler.addEventListener('hardwareBackPress', callback);
  },

  /**
   * Prevent browser back navigation
   */
  preventBrowserBack: () => {
    return BackHandler.addEventListener('hardwareBackPress', () => true);
  },

  /**
   * Allow browser back navigation
   */
  allowBrowserBack: () => {
    return BackHandler.addEventListener('hardwareBackPress', () => false);
  },

  /**
   * Show confirmation before leaving
   */
  confirmBeforeLeaving: (message = 'Are you sure you want to leave?') => {
    const handleBeforeUnload = (event) => {
      event.preventDefault();
      event.returnValue = message;
      return message;
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return {
      remove: () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      },
    };
  },

  /**
   * Handle keyboard shortcuts for back navigation
   */
  handleKeyboardShortcuts: () => {
    const handleKeyDown = (event) => {
      // Handle Alt+Left Arrow (browser back shortcut)
      if (event.altKey && event.key === 'ArrowLeft') {
        event.preventDefault();
        BackHandler.simulateBackPress();
      }
      
      // Handle Escape key as back
      if (event.key === 'Escape') {
        BackHandler.simulateBackPress();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return {
      remove: () => {
        window.removeEventListener('keydown', handleKeyDown);
      },
    };
  },

  /**
   * Get browser navigation state
   */
  getNavigationState: () => {
    if (typeof window !== 'undefined' && window.history) {
      return {
        canGoBack: window.history.length > 1,
        canGoForward: false, // Not easily detectable in browsers
        currentIndex: window.history.length - 1,
        totalLength: window.history.length,
      };
    }
    return {
      canGoBack: false,
      canGoForward: false,
      currentIndex: 0,
      totalLength: 1,
    };
  },

  /**
   * Navigate back programmatically
   */
  goBack: () => {
    if (typeof window !== 'undefined' && window.history) {
      window.history.back();
    }
  },

  /**
   * Navigate forward programmatically
   */
  goForward: () => {
    if (typeof window !== 'undefined' && window.history) {
      window.history.forward();
    }
  },

  /**
   * Replace current history entry
   */
  replaceState: (url) => {
    if (typeof window !== 'undefined' && window.history) {
      window.history.replaceState(null, '', url);
    }
  },

  /**
   * Push new history entry
   */
  pushState: (url) => {
    if (typeof window !== 'undefined' && window.history) {
      window.history.pushState(null, '', url);
    }
  },
};

// Auto-cleanup on page unload
if (Platform.OS === 'web' && typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    BackHandler.clearListeners();
  });
}
