// BatchedBridge polyfill for React Native Web
// Fixes: __fbBatchedBridgeConfig is not set, cannot invoke native modules

// Detect platform without importing React Native to avoid circular dependencies
const Platform = {
  OS: typeof window !== 'undefined' && typeof document !== 'undefined' ? 'web' :
      typeof navigator !== 'undefined' && navigator.product === 'ReactNative' ? 'ios' : 'android'
};

// Only apply polyfills on web platform
if (Platform.OS === 'web') {
  console.log('🌐 Setting up BatchedBridge polyfill for web...');

  // Setup global __fbBatchedBridgeConfig
  if (typeof global !== 'undefined') {
    if (!global.__fbBatchedBridgeConfig) {
      global.__fbBatchedBridgeConfig = {
        remoteModuleConfig: [],
        localModulesConfig: []
      };
      console.log('🌐 __fbBatchedBridgeConfig initialized');
    }

    // Setup BatchedBridge
    if (!global.BatchedBridge) {
      global.BatchedBridge = {
        registerCallableModule: (name) => {
          console.log(`🌐 Mock: registerCallableModule(${name})`);
        },
        enqueueNativeCall: (moduleID, methodID, params, onFail, onSucc) => {
          console.log(`🌐 Mock: enqueueNativeCall(${moduleID}, ${methodID})`);
          if (onSucc) onSucc();
        },
        callFunctionReturnFlushedQueue: (method) => {
          console.log(`🌐 Mock: callFunctionReturnFlushedQueue(${module}, ${method})`);
          return [];
        },
        invokeCallbackAndReturnFlushedQueue: (cbID) => {
          console.log(`🌐 Mock: invokeCallbackAndReturnFlushedQueue(${cbID})`);
          return [];
        },
        flushedQueue: () => {
          return [];
        },
        getEventLoopRunningTime: () => 0,
        setGlobalHandler: () => {
          console.log('🌐 Mock: setGlobalHandler');
        }
      };
      console.log('🌐 BatchedBridge initialized');
    }

    // Setup NativeModules
    if (!global.nativeModules) {
      global.nativeModules = {};
      console.log('🌐 nativeModules initialized');
    }

    // Setup __fbBatchedBridge
    if (!global.__fbBatchedBridge) {
      global.__fbBatchedBridge = global.BatchedBridge;
      console.log('🌐 __fbBatchedBridge linked to BatchedBridge');
    }
  }

  console.log('✅ BatchedBridge polyfill setup complete');
}

// Export a mock BatchedBridge for direct imports
export default {
  registerCallableModule: () => {},
  enqueueNativeCall: () => {},
  callFunctionReturnFlushedQueue: () => [],
  invokeCallbackAndReturnFlushedQueue: () => [],
  flushedQueue: () => [],
  getEventLoopRunningTime: () => 0,
  setGlobalHandler: () => {}
};
