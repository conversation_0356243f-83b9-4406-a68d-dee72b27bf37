// CTRON Home - Platform Utilities Polyfill
// Polyfill for React Native's Platform utilities module

console.log('🔧 PLATFORM UTILITIES: Polyfill loaded');

/**
 * Platform utilities polyfill for React Native Web compatibility
 * This module provides platform detection and utilities that React Native expects
 */

// Platform detection for web
const Platform = {
  OS: 'web',
  Version: undefined,
  isPad: false,
  isTesting: typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'test',
  isTV: false,

  select: (obj) => {
    return obj.web !== undefined ? obj.web : obj.default;
  },

  // Web-specific additions
  isWeb: true,
  isMobile: typeof navigator !== 'undefined' ? /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) : false,
  isTablet: typeof navigator !== 'undefined' ? /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent) : false,
  isDesktop: typeof navigator !== 'undefined' ? !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) : true,

  // Add constants property for compatibility
  constants: {
    reactNativeVersion: { major: 0, minor: 74, patch: 5 },
    systemName: 'Web',
    osVersion: undefined,
    interfaceIdiom: 'phone',
    isTesting: typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'test',
  },
};

// Additional platform utilities
const PlatformConstants = {
  forceTouchAvailable: false,
  interfaceIdiom: 'phone',
  isTesting: false,
  osVersion: undefined,
  reactNativeVersion: { major: 0, minor: 74, patch: 5 },
  systemName: 'Web',
};

// Dimensions utilities
const Dimensions = {
  get: (dimension) => {
    if (typeof window === 'undefined') {
      return { width: 375, height: 667, scale: 1, fontScale: 1 };
    }

    if (dimension === 'window') {
      return {
        width: window.innerWidth || 375,
        height: window.innerHeight || 667,
        scale: window.devicePixelRatio || 1,
        fontScale: 1,
      };
    }
    if (dimension === 'screen') {
      return {
        width: (window.screen && window.screen.width) || 375,
        height: (window.screen && window.screen.height) || 667,
        scale: window.devicePixelRatio || 1,
        fontScale: 1,
      };
    }
    return { width: 375, height: 667, scale: 1, fontScale: 1 };
  },

  addEventListener: () => { },
  removeEventListener: () => { },
};

// PixelRatio utilities
const PixelRatio = {
  get: () => (typeof window !== 'undefined' && window.devicePixelRatio) || 1,
  getFontScale: () => 1,
  getPixelSizeForLayoutSize: (layoutSize) => Math.round(layoutSize * PixelRatio.get()),
  roundToNearestPixel: (layoutSize) => {
    const ratio = PixelRatio.get();
    return Math.round(layoutSize * ratio) / ratio;
  },
};

// Export all utilities
export default Platform;
export { Platform, PlatformConstants, Dimensions, PixelRatio };

// CommonJS compatibility for React Native internal imports
module.exports = Platform;
module.exports.default = Platform;
module.exports.Platform = Platform;
module.exports.PlatformConstants = PlatformConstants;
module.exports.Dimensions = Dimensions;
module.exports.PixelRatio = PixelRatio;

// Global registration for compatibility
if (typeof global !== 'undefined') {
  global.Platform = Platform;
  global.PlatformConstants = PlatformConstants;
  global.Dimensions = Dimensions;
  global.PixelRatio = PixelRatio;

  // Also add to global.__fbBatchedBridge for React Native compatibility
  global.__fbBatchedBridge = global.__fbBatchedBridge || {};
  global.__fbBatchedBridge.PlatformConstants = PlatformConstants;
}

if (typeof window !== 'undefined') {
  window.Platform = Platform;
  window.PlatformConstants = PlatformConstants;
  window.Dimensions = Dimensions;
  window.PixelRatio = PixelRatio;
}

console.log('🔧 PLATFORM UTILITIES: Polyfill ready');
