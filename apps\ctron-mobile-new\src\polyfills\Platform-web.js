// CTRON Home - Platform Utilities Polyfill
// Polyfill for React Native's Platform utilities module

console.log('🔧 PLATFORM UTILITIES: Polyfill loaded');

/**
 * Platform utilities polyfill for React Native Web compatibility
 * This module provides platform detection and utilities that React Native expects
 */

// Platform detection for web
const Platform = {
  OS: 'web',
  Version: undefined,
  isPad: false,
  isTesting: process.env.NODE_ENV === 'test',
  isTV: false,
  
  select: (obj) => {
    return obj.web !== undefined ? obj.web : obj.default;
  },
  
  // Web-specific additions
  isWeb: true,
  isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
  isTablet: /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent),
  isDesktop: !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
};

// Additional platform utilities
const PlatformConstants = {
  forceTouchAvailable: false,
  interfaceIdiom: Platform.isPad ? 'pad' : 'phone',
  isTesting: Platform.isTesting,
  osVersion: undefined,
  reactNativeVersion: { major: 0, minor: 74, patch: 5 },
  systemName: 'Web',
};

// Dimensions utilities
const Dimensions = {
  get: (dimension) => {
    if (dimension === 'window') {
      return {
        width: window.innerWidth,
        height: window.innerHeight,
        scale: window.devicePixelRatio || 1,
        fontScale: 1,
      };
    }
    if (dimension === 'screen') {
      return {
        width: window.screen.width,
        height: window.screen.height,
        scale: window.devicePixelRatio || 1,
        fontScale: 1,
      };
    }
    return { width: 0, height: 0, scale: 1, fontScale: 1 };
  },
  
  addEventListener: () => {},
  removeEventListener: () => {},
};

// PixelRatio utilities
const PixelRatio = {
  get: () => window.devicePixelRatio || 1,
  getFontScale: () => 1,
  getPixelSizeForLayoutSize: (layoutSize) => Math.round(layoutSize * PixelRatio.get()),
  roundToNearestPixel: (layoutSize) => {
    const ratio = PixelRatio.get();
    return Math.round(layoutSize * ratio) / ratio;
  },
};

// Export all utilities
export default Platform;
export { Platform, PlatformConstants, Dimensions, PixelRatio };

// Global registration for compatibility
if (typeof global !== 'undefined') {
  global.Platform = Platform;
  global.PlatformConstants = PlatformConstants;
  global.Dimensions = Dimensions;
  global.PixelRatio = PixelRatio;
}

if (typeof window !== 'undefined') {
  window.Platform = Platform;
  window.PlatformConstants = PlatformConstants;
  window.Dimensions = Dimensions;
  window.PixelRatio = PixelRatio;
}

console.log('🔧 PLATFORM UTILITIES: Polyfill ready');
