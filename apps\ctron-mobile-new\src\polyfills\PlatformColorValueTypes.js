// CTRON Home - PlatformColorValueTypes Polyfill
// Polyfill for React Native's PlatformColorValueTypes module

console.log('🔧 PLATFORM COLOR VALUE TYPES: Polyfill loaded');

/**
 * PlatformColorValueTypes polyfill for React Native Web compatibility
 * This module provides platform-specific color value types that React Native expects
 */

// Define platform color value types for web compatibility
const PlatformColorValueType = {
  // iOS specific color types
  ios: {
    semantic: [
      'systemBlue',
      'systemGreen',
      'systemIndigo',
      'systemOrange',
      'systemPink',
      'systemPurple',
      'systemRed',
      'systemTeal',
      'systemYellow',
      'systemGray',
      'systemGray2',
      'systemGray3',
      'systemGray4',
      'systemGray5',
      'systemGray6',
      'label',
      'secondaryLabel',
      'tertiaryLabel',
      'quaternaryLabel',
      'systemFill',
      'secondarySystemFill',
      'tertiarySystemFill',
      'quaternarySystemFill',
      'placeholderText',
      'systemBackground',
      'secondarySystemBackground',
      'tertiarySystemBackground',
      'systemGroupedBackground',
      'secondarySystemGroupedBackground',
      'tertiarySystemGroupedBackground',
      'separator',
      'opaqueSeparator',
      'link',
      'darkText',
      'lightText'
    ],
    dynamic: [
      'systemBlue',
      'systemGreen',
      'systemIndigo',
      'systemOrange',
      'systemPink',
      'systemPurple',
      'systemRed',
      'systemTeal',
      'systemYellow',
      'systemGray',
      'systemGray2',
      'systemGray3',
      'systemGray4',
      'systemGray5',
      'systemGray6'
    ]
  },

  // Android specific color types
  android: {
    resource: [
      'colorPrimary',
      'colorPrimaryDark',
      'colorAccent',
      'colorControlNormal',
      'colorControlActivated',
      'colorControlHighlight',
      'colorButtonNormal',
      'textColorPrimary',
      'textColorSecondary',
      'textColorTertiary',
      'textColorPrimaryInverse',
      'textColorSecondaryInverse',
      'colorBackground',
      'colorForeground',
      'colorBackgroundFloating'
    ],
    attr: [
      'colorPrimary',
      'colorPrimaryDark',
      'colorAccent',
      'colorControlNormal',
      'colorControlActivated',
      'colorControlHighlight',
      'colorButtonNormal',
      'textColorPrimary',
      'textColorSecondary'
    ]
  },

  // Web fallback colors
  web: {
    css: [
      'currentColor',
      'transparent',
      'inherit',
      'initial',
      'unset'
    ],
    system: [
      'ActiveBorder',
      'ActiveCaption',
      'AppWorkspace',
      'Background',
      'ButtonFace',
      'ButtonHighlight',
      'ButtonShadow',
      'ButtonText',
      'CaptionText',
      'GrayText',
      'Highlight',
      'HighlightText',
      'InactiveBorder',
      'InactiveCaption',
      'InactiveCaptionText',
      'InfoBackground',
      'InfoText',
      'Menu',
      'MenuText',
      'Scrollbar',
      'ThreeDDarkShadow',
      'ThreeDFace',
      'ThreeDHighlight',
      'ThreeDLightShadow',
      'ThreeDShadow',
      'Window',
      'WindowFrame',
      'WindowText'
    ]
  }
};

/**
 * Validate if a color value is a valid platform color
 */
function isPlatformColor(color) {
  if (typeof color !== 'object' || color === null) {
    return false;
  }

  // Check for iOS semantic colors
  if (color.semantic && typeof color.semantic === 'string') {
    return PlatformColorValueTypes.ios.semantic.includes(color.semantic);
  }

  // Check for iOS dynamic colors
  if (color.dynamic && typeof color.dynamic === 'object') {
    return true; // Dynamic colors are complex objects
  }

  // Check for Android resource colors
  if (color.resource_paths && Array.isArray(color.resource_paths)) {
    return color.resource_paths.every(path => 
      typeof path === 'string' && path.length > 0
    );
  }

  return false;
}

/**
 * Convert platform color to web-compatible color
 */
function platformColorToWebColor(color) {
  if (!isPlatformColor(color)) {
    return color;
  }

  // Handle iOS semantic colors
  if (color.semantic) {
    const semanticColorMap = {
      'systemBlue': '#007AFF',
      'systemGreen': '#34C759',
      'systemIndigo': '#5856D6',
      'systemOrange': '#FF9500',
      'systemPink': '#FF2D92',
      'systemPurple': '#AF52DE',
      'systemRed': '#FF3B30',
      'systemTeal': '#5AC8FA',
      'systemYellow': '#FFCC00',
      'systemGray': '#8E8E93',
      'systemGray2': '#AEAEB2',
      'systemGray3': '#C7C7CC',
      'systemGray4': '#D1D1D6',
      'systemGray5': '#E5E5EA',
      'systemGray6': '#F2F2F7',
      'label': '#000000',
      'secondaryLabel': '#3C3C43',
      'tertiaryLabel': '#3C3C43',
      'quaternaryLabel': '#3C3C43',
      'systemBackground': '#FFFFFF',
      'secondarySystemBackground': '#F2F2F7',
      'tertiarySystemBackground': '#FFFFFF'
    };
    
    return semanticColorMap[color.semantic] || '#000000';
  }

  // Handle iOS dynamic colors (use light variant)
  if (color.dynamic && color.dynamic.light) {
    return color.dynamic.light;
  }

  // Handle Android resource colors (return a default)
  if (color.resource_paths) {
    return '#000000'; // Default fallback
  }

  return '#000000'; // Ultimate fallback
}

export const PlatformColorValueTypes = {
  PlatformColorValueType,
  isPlatformColor,
  platformColorToWebColor
};

export { isPlatformColor, platformColorToWebColor };

if (typeof global !== 'undefined') {
  global.PlatformColorValueTypes = PlatformColorValueTypes;
  global.isPlatformColor = isPlatformColor;
  global.platformColorToWebColor = platformColorToWebColor;
}

if (typeof window !== 'undefined') {
  window.PlatformColorValueTypes = PlatformColorValueTypes;
  window.isPlatformColor = isPlatformColor;
  window.platformColorToWebColor = platformColorToWebColor;
}

console.log('🔧 PLATFORM COLOR VALUE TYPES: Polyfill ready');
