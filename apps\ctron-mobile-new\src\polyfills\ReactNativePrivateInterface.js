// CTRON Home - ReactNativePrivateInterface Polyfill
// Polyfill for React Native's ReactNativePrivateInterface module

console.log('🔧 REACT NATIVE PRIVATE INTERFACE: Polyfill loaded');

/**
 * ReactNativePrivateInterface polyfill for React Native Web compatibility
 * This module provides the private interface that React Native expects
 */

// Mock BatchedBridge for React Native Web
const BatchedBridge = {
  registerCallableModule: function(name, module) {
    console.log('🔧 BATCHED BRIDGE: registerCallableModule called', name);
    // Store the module for potential future use
    if (typeof global !== 'undefined') {
      global.__registeredModules = global.__registeredModules || {};
      global.__registeredModules[name] = module;
    }
  },

  enqueueNativeCall: function(moduleID, methodID, params, onFail, onSucc) {
    console.log('🔧 BATCHED BRIDGE: enqueueNativeCall called', { moduleID, methodID });
    // For web, we can't make native calls, so we just log and potentially call success
    if (onSucc && typeof onSucc === 'function') {
      setTimeout(() => onSucc(null), 0);
    }
  },

  callFunctionReturnFlushedQueue: function(module, method, args) {
    console.log('🔧 BATCHED BRIDGE: callFunctionReturnFlushedQueue called', { module, method });
    return null;
  },

  invokeCallbackAndReturnFlushedQueue: function(cbID, args) {
    console.log('🔧 BATCHED BRIDGE: invokeCallbackAndReturnFlushedQueue called', cbID);
    return null;
  },

  flushedQueue: function() {
    console.log('🔧 BATCHED BRIDGE: flushedQueue called');
    return null;
  },

  getEventLoopRunningTime: function() {
    console.log('🔧 BATCHED BRIDGE: getEventLoopRunningTime called');
    return 0;
  }
};

// Mock ReactNativeViewConfigRegistry for React Native Web
const ReactNativeViewConfigRegistry = {
  register: function(name, callback) {
    console.log('🔧 VIEW CONFIG REGISTRY: register called', name);
    // Store the view config for potential future use
    if (typeof global !== 'undefined') {
      global.__viewConfigs = global.__viewConfigs || {};
      global.__viewConfigs[name] = callback;
    }
    return name;
  },

  get: function(name) {
    console.log('🔧 VIEW CONFIG REGISTRY: get called', name);
    if (typeof global !== 'undefined' && global.__viewConfigs && global.__viewConfigs[name]) {
      return global.__viewConfigs[name];
    }
    return null;
  }
};

// Mock UIManager for React Native Web
const UIManager = {
  createView: function(reactTag, viewName, rootTag, props) {
    console.log('🔧 UI MANAGER: createView called', { reactTag, viewName, rootTag });
  },

  updateView: function(reactTag, viewName, props) {
    console.log('🔧 UI MANAGER: updateView called', { reactTag, viewName });
  },

  manageChildren: function(containerTag, moveFromIndices, moveToIndices, addChildReactTags, addAtIndices, removeAtIndices) {
    console.log('🔧 UI MANAGER: manageChildren called', containerTag);
  },

  measure: function(reactTag, callback) {
    console.log('🔧 UI MANAGER: measure called', reactTag);
    if (callback && typeof callback === 'function') {
      // Return mock measurements
      setTimeout(() => callback(0, 0, 100, 100, 0, 0), 0);
    }
  },

  measureInWindow: function(reactTag, callback) {
    console.log('🔧 UI MANAGER: measureInWindow called', reactTag);
    if (callback && typeof callback === 'function') {
      // Return mock measurements
      setTimeout(() => callback(0, 0, 100, 100), 0);
    }
  },

  measureLayout: function(reactTag, ancestorTag, errorCallback, successCallback) {
    console.log('🔧 UI MANAGER: measureLayout called', { reactTag, ancestorTag });
    if (successCallback && typeof successCallback === 'function') {
      // Return mock measurements
      setTimeout(() => successCallback(0, 0, 100, 100), 0);
    }
  },

  setJSResponder: function(reactTag, blockNativeResponder) {
    console.log('🔧 UI MANAGER: setJSResponder called', { reactTag, blockNativeResponder });
  },

  clearJSResponder: function() {
    console.log('🔧 UI MANAGER: clearJSResponder called');
  },

  dispatchViewManagerCommand: function(reactTag, commandID, commandArgs) {
    console.log('🔧 UI MANAGER: dispatchViewManagerCommand called', { reactTag, commandID });
  },

  showPopupMenu: function(reactTag, items, error, success) {
    console.log('🔧 UI MANAGER: showPopupMenu called', reactTag);
    if (success && typeof success === 'function') {
      setTimeout(() => success(0), 0);
    }
  }
};

// Mock TextInputState for React Native Web
const TextInputState = {
  focusTextInput: function(reactTag) {
    console.log('🔧 TEXT INPUT STATE: focusTextInput called', reactTag);
    // For web, try to focus the element if it exists
    if (typeof document !== 'undefined') {
      const element = document.querySelector(`[data-reactroot] [data-tag="${reactTag}"]`);
      if (element && element.focus) {
        element.focus();
      }
    }
  },

  blurTextInput: function(reactTag) {
    console.log('🔧 TEXT INPUT STATE: blurTextInput called', reactTag);
    // For web, try to blur the element if it exists
    if (typeof document !== 'undefined') {
      const element = document.querySelector(`[data-reactroot] [data-tag="${reactTag}"]`);
      if (element && element.blur) {
        element.blur();
      }
    }
  },

  currentlyFocusedInput: function() {
    console.log('🔧 TEXT INPUT STATE: currentlyFocusedInput called');
    // For web, return the currently focused element
    if (typeof document !== 'undefined' && document.activeElement) {
      return document.activeElement.getAttribute('data-tag') || null;
    }
    return null;
  }
};

// Main ReactNativePrivateInterface export
const ReactNativePrivateInterface = {
  BatchedBridge,
  ReactNativeViewConfigRegistry,
  UIManager,
  TextInputState,
  
  // Additional properties that might be expected
  ReactCurrentDispatcher: null,
  ReactCurrentBatchConfig: null,
  ReactCurrentOwner: null,
  ReactDebugCurrentFrame: null,
  
  // Scheduler properties
  unstable_scheduleCallback: function(priorityLevel, callback, options) {
    console.log('🔧 SCHEDULER: unstable_scheduleCallback called');
    return setTimeout(callback, 0);
  },
  
  unstable_cancelCallback: function(callbackNode) {
    console.log('🔧 SCHEDULER: unstable_cancelCallback called');
    if (callbackNode) {
      clearTimeout(callbackNode);
    }
  },
  
  unstable_shouldYield: function() {
    console.log('🔧 SCHEDULER: unstable_shouldYield called');
    return false;
  },
  
  unstable_requestPaint: function() {
    console.log('🔧 SCHEDULER: unstable_requestPaint called');
  },
  
  unstable_now: function() {
    return performance && performance.now ? performance.now() : Date.now();
  },

  // Mock for legacySendAccessibilityEvent
  legacySendAccessibilityEvent: function(reactTag, eventType, eventData) {
    console.log('🔧 ACCESSIBILITY: legacySendAccessibilityEvent called', { reactTag, eventType, eventData });
    // Implement mock behavior if necessary, or leave empty if just to prevent resolution errors
  }
  },
  unstable_getCurrentPriorityLevel: function() {
    console.log('🔧 SCHEDULER: unstable_getCurrentPriorityLevel called');
    return 3; // Normal priority
  },
  
  unstable_ImmediatePriority: 1,
  unstable_UserBlockingPriority: 2,
  unstable_NormalPriority: 3,
  unstable_LowPriority: 4,
  unstable_IdlePriority: 5
};

export default ReactNativePrivateInterface;
export { BatchedBridge, ReactNativeViewConfigRegistry, UIManager, TextInputState };

if (typeof global !== 'undefined') {
  global.ReactNativePrivateInterface = ReactNativePrivateInterface;
  global.BatchedBridge = BatchedBridge;
  global.ReactNativeViewConfigRegistry = ReactNativeViewConfigRegistry;
  global.UIManager = UIManager;
  global.TextInputState = TextInputState;
}

if (typeof window !== 'undefined') {
  window.ReactNativePrivateInterface = ReactNativePrivateInterface;
  window.BatchedBridge = BatchedBridge;
  window.ReactNativeViewConfigRegistry = ReactNativeViewConfigRegistry;
  window.UIManager = UIManager;
  window.TextInputState = TextInputState;
}

console.log('🔧 REACT NATIVE PRIVATE INTERFACE: Polyfill ready');
