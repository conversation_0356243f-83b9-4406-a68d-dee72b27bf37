// src/polyfills/ReactNativeUtilities-web.js
// Comprehensive web polyfill for all React Native utilities

// Detect platform without importing React Native to avoid circular dependencies
const Platform = {
  OS: typeof window !== 'undefined' && typeof document !== 'undefined' ? 'web' :
      typeof navigator !== 'undefined' && navigator.product === 'ReactNative' ? 'ios' : 'android'
};

/**
 * Comprehensive polyfill for React Native utilities that might be missing
 */

// Dimensions utility
const Dimensions = {
  get: (dimension) => {
    if (Platform.OS !== 'web') return { width: 0, height: 0, scale: 1, fontScale: 1 };
    
    if (typeof window === 'undefined') {
      return { width: 1024, height: 768, scale: 1, fontScale: 1 };
    }
    
    if (dimension === 'window') {
      return {
        width: window.innerWidth,
        height: window.innerHeight,
        scale: window.devicePixelRatio || 1,
        fontScale: 1,
      };
    }
    
    if (dimension === 'screen') {
      return {
        width: window.screen.width,
        height: window.screen.height,
        scale: window.devicePixelRatio || 1,
        fontScale: 1,
      };
    }
    
    return { width: 0, height: 0, scale: 1, fontScale: 1 };
  },
  
  addEventListener: (_type, handler) => {
    if (Platform.OS !== 'web') return { remove: () => {} };
    
    const handleResize = () => {
      handler({
        window: Dimensions.get('window'),
        screen: Dimensions.get('screen'),
      });
    };
    
    window.addEventListener('resize', handleResize);
    return {
      remove: () => window.removeEventListener('resize', handleResize),
    };
  },
  
  removeEventListener: (_type, handler) => {
    if (Platform.OS === 'web') {
      window.removeEventListener('resize', handler);
    }
  },
};

// PixelRatio utility
const PixelRatio = {
  get: () => {
    if (Platform.OS !== 'web') return 1;
    return typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
  },
  
  getFontScale: () => {
    return 1; // Web doesn't have system font scaling like mobile
  },
  
  getPixelSizeForLayoutSize: (layoutSize) => {
    return Math.round(layoutSize * PixelRatio.get());
  },
  
  roundToNearestPixel: (layoutSize) => {
    const ratio = PixelRatio.get();
    return Math.round(layoutSize * ratio) / ratio;
  },
};

// StatusBar utility (web implementation)
const StatusBar = {
  setHidden: () => {
    // Not applicable on web
  },
  
  setBackgroundColor: () => {
    // Not applicable on web
  },
  
  setTranslucent: () => {
    // Not applicable on web
  },
  
  setBarStyle: () => {
    // Not applicable on web
  },
  
  setNetworkActivityIndicatorVisible: () => {
    // Not applicable on web
  },
  
  currentHeight: 0, // Web doesn't have status bar
};

// Keyboard utility
const Keyboard = {
  addListener: (_eventName, _callback) => {
    if (Platform.OS !== 'web') return { remove: () => {} };
    
    // Web doesn't have the same keyboard events as mobile
    // But we can simulate some basic functionality
    const handleFocus = () => {
      if (eventName === 'keyboardDidShow') {
        callback({ endCoordinates: { height: 0, screenX: 0, screenY: 0, width: 0 } });
      }
    };
    
    const handleBlur = () => {
      if (eventName === 'keyboardDidHide') {
        callback();
      }
    };
    
    document.addEventListener('focusin', handleFocus);
    document.addEventListener('focusout', handleBlur);
    
    return {
      remove: () => {
        document.removeEventListener('focusin', handleFocus);
        document.removeEventListener('focusout', handleBlur);
      },
    };
  },
  
  removeListener: (_eventName, _callback) => {
    // Handled by the subscription object
  },
  
  removeAllListeners: (eventName) => {
    // Not easily implementable on web
  },
  
  dismiss: () => {
    if (Platform.OS === 'web' && document.activeElement) {
      document.activeElement.blur();
    }
  },
  
  scheduleLayoutAnimation: (_event) => {
    // Not applicable on web
  },
};

// AppState utility
const AppState = {
  currentState: 'active',
  
  addEventListener: (type, handler) => {
    if (Platform.OS !== 'web') return { remove: () => {} };
    
    const handleVisibilityChange = () => {
      const state = document.hidden ? 'background' : 'active';
      AppState.currentState = state;
      if (type === 'change') {
        handler(state);
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return {
      remove: () => document.removeEventListener('visibilitychange', handleVisibilityChange),
    };
  },
  
  removeEventListener: (type, handler) => {
    if (Platform.OS === 'web') {
      document.removeEventListener('visibilitychange', handler);
    }
  },
};

// Linking utility
const Linking = {
  openURL: (url) => {
    if (Platform.OS === 'web') {
      window.open(url, '_blank');
      return Promise.resolve();
    }
    return Promise.reject(new Error('Not supported'));
  },
  
  canOpenURL: (url) => {
    // On web, we can generally open URLs
    return Promise.resolve(true);
  },
  
  getInitialURL: () => {
    if (Platform.OS === 'web') {
      return Promise.resolve(window.location.href);
    }
    return Promise.resolve(null);
  },
  
  addEventListener: (type, handler) => {
    if (Platform.OS !== 'web') return { remove: () => {} };
    
    const handlePopState = () => {
      if (type === 'url') {
        handler({ url: window.location.href });
      }
    };
    
    window.addEventListener('popstate', handlePopState);
    
    return {
      remove: () => window.removeEventListener('popstate', handlePopState),
    };
  },
  
  removeEventListener: (type, handler) => {
    if (Platform.OS === 'web') {
      window.removeEventListener('popstate', handler);
    }
  },
};

// Share utility
const Share = {
  share: (content, _options) => {
    if (Platform.OS !== 'web') return Promise.reject(new Error('Not supported'));
    
    if (navigator.share) {
      return navigator.share({
        title: content.title,
        text: content.message,
        url: content.url,
      });
    } else {
      // Fallback for browsers without Web Share API
      const text = `${content.title || ''}\n${content.message || ''}\n${content.url || ''}`.trim();
      if (navigator.clipboard) {
        return navigator.clipboard.writeText(text).then(() => ({ action: 'sharedAction' }));
      } else {
        // Final fallback
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return Promise.resolve({ action: 'sharedAction' });
      }
    }
  },
};

// Vibration utility
const Vibration = {
  vibrate: (pattern) => {
    if (Platform.OS === 'web' && navigator.vibrate) {
      navigator.vibrate(pattern);
    }
  },
  
  cancel: () => {
    if (Platform.OS === 'web' && navigator.vibrate) {
      navigator.vibrate(0);
    }
  },
};

// PanResponder utility (simplified for web)
const PanResponder = {
  create: (config) => {
    return {
      panHandlers: {
        onStartShouldSetPanResponder: () => false,
        onMoveShouldSetPanResponder: () => false,
        onPanResponderGrant: () => {},
        onPanResponderMove: () => {},
        onPanResponderRelease: () => {},
        onPanResponderTerminate: () => {},
      },
    };
  },
};

// Animated utility (basic implementation)
const Animated = {
  Value: class {
    constructor(value) {
      this._value = value;
    }
    
    setValue(value) {
      this._value = value;
    }
    
    addListener(callback) {
      return { remove: () => {} };
    }
    
    removeListener(id) {}
    
    removeAllListeners() {}
  },
  
  timing: (value, config) => ({
    start: (callback) => {
      if (callback) callback({ finished: true });
    },
    stop: () => {},
    reset: () => {},
  }),
  
  spring: (value, config) => ({
    start: (callback) => {
      if (callback) callback({ finished: true });
    },
    stop: () => {},
    reset: () => {},
  }),
  
  decay: (value, config) => ({
    start: (callback) => {
      if (callback) callback({ finished: true });
    },
    stop: () => {},
    reset: () => {},
  }),
};

// Export all utilities
export default {
  Dimensions,
  PixelRatio,
  StatusBar,
  Keyboard,
  AppState,
  Linking,
  Share,
  Vibration,
  PanResponder,
  Animated,
};

export {
  Dimensions,
  PixelRatio,
  StatusBar,
  Keyboard,
  AppState,
  Linking,
  Share,
  Vibration,
  PanResponder,
  Animated,
};
