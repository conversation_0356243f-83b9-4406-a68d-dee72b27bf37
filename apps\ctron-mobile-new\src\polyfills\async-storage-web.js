// src/polyfills/async-storage-web.js
// Web polyfill for @react-native-async-storage/async-storage

import { Platform } from 'react-native';

/**
 * Web implementation of AsyncStorage using localStorage
 * Provides the same API as @react-native-async-storage/async-storage
 */
class AsyncStorageWeb {
  /**
   * Get a value from storage
   */
  static async getItem(key) {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        const value = window.localStorage.getItem(key);
        return Promise.resolve(value);
      }
      return Promise.resolve(null);
    } catch (error) {
      console.error('AsyncStorage.getItem error:', error);
      return Promise.resolve(null);
    }
  }

  /**
   * Set a value in storage
   */
  static async setItem(key, value) {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
        return Promise.resolve();
      }
      return Promise.resolve();
    } catch (error) {
      console.error('AsyncStorage.setItem error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Remove a value from storage
   */
  static async removeItem(key) {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
        return Promise.resolve();
      }
      return Promise.resolve();
    } catch (error) {
      console.error('AsyncStorage.removeItem error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Merge a value with an existing value
   */
  static async mergeItem(key, value) {
    try {
      const existingValue = await AsyncStorageWeb.getItem(key);
      let mergedValue = value;
      
      if (existingValue) {
        try {
          const existingObject = JSON.parse(existingValue);
          const newObject = JSON.parse(value);
          mergedValue = JSON.stringify({ ...existingObject, ...newObject });
        } catch (parseError) {
          // If parsing fails, just use the new value
          mergedValue = value;
        }
      }
      
      return AsyncStorageWeb.setItem(key, mergedValue);
    } catch (error) {
      console.error('AsyncStorage.mergeItem error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Clear all storage
   */
  static async clear() {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.clear();
        return Promise.resolve();
      }
      return Promise.resolve();
    } catch (error) {
      console.error('AsyncStorage.clear error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Get all keys
   */
  static async getAllKeys() {
    try {
      if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
        const keys = [];
        for (let i = 0; i < window.localStorage.length; i++) {
          const key = window.localStorage.key(i);
          if (key) {
            keys.push(key);
          }
        }
        return Promise.resolve(keys);
      }
      return Promise.resolve([]);
    } catch (error) {
      console.error('AsyncStorage.getAllKeys error:', error);
      return Promise.resolve([]);
    }
  }

  /**
   * Get multiple values
   */
  static async multiGet(keys) {
    try {
      const results = await Promise.all(
        keys.map(async (key) => {
          const value = await AsyncStorageWeb.getItem(key);
          return [key, value];
        })
      );
      return Promise.resolve(results);
    } catch (error) {
      console.error('AsyncStorage.multiGet error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Set multiple values
   */
  static async multiSet(keyValuePairs) {
    try {
      await Promise.all(
        keyValuePairs.map(([key, value]) => AsyncStorageWeb.setItem(key, value))
      );
      return Promise.resolve();
    } catch (error) {
      console.error('AsyncStorage.multiSet error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Remove multiple values
   */
  static async multiRemove(keys) {
    try {
      await Promise.all(
        keys.map((key) => AsyncStorageWeb.removeItem(key))
      );
      return Promise.resolve();
    } catch (error) {
      console.error('AsyncStorage.multiRemove error:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Merge multiple values
   */
  static async multiMerge(keyValuePairs) {
    try {
      await Promise.all(
        keyValuePairs.map(([key, value]) => AsyncStorageWeb.mergeItem(key, value))
      );
      return Promise.resolve();
    } catch (error) {
      console.error('AsyncStorage.multiMerge error:', error);
      return Promise.reject(error);
    }
  }
}

// Export as default for compatibility with AsyncStorage import
export default AsyncStorageWeb;

// Also export named exports for compatibility
export const getItem = AsyncStorageWeb.getItem;
export const setItem = AsyncStorageWeb.setItem;
export const removeItem = AsyncStorageWeb.removeItem;
export const mergeItem = AsyncStorageWeb.mergeItem;
export const clear = AsyncStorageWeb.clear;
export const getAllKeys = AsyncStorageWeb.getAllKeys;
export const multiGet = AsyncStorageWeb.multiGet;
export const multiSet = AsyncStorageWeb.multiSet;
export const multiRemove = AsyncStorageWeb.multiRemove;
export const multiMerge = AsyncStorageWeb.multiMerge;
