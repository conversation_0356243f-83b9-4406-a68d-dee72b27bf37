// src/polyfills/react-native-platform-web.js
// Web polyfill for React Native Platform utilities

/**
 * Platform utilities for React Native Web compatibility
 * This polyfill provides missing platform utilities that React Native Web doesn't include
 */

// Mock Platform utilities that are missing in React Native Web
const PlatformUtilities = {
  /**
   * Get platform-specific constants
   */
  getConstants() {
    return {
      OS: 'web',
      Version: '1.0.0',
      isTesting: false,
      reactNativeVersion: {
        major: 0,
        minor: 71,
        patch: 8,
        prerelease: null,
      },
    };
  },

  /**
   * Check if platform is specific type
   */
  isPlatform(platform) {
    return platform === 'web';
  },

  /**
   * Get platform-specific select value
   */
  select(obj) {
    if (obj.web !== undefined) {
      return obj.web;
    }
    if (obj.default !== undefined) {
      return obj.default;
    }
    return undefined;
  },

  /**
   * Platform-specific styling
   */
  getStyleSheet() {
    return {};
  },

  /**
   * Platform-specific dimensions
   */
  getDimensions() {
    if (typeof window !== 'undefined') {
      return {
        window: {
          width: window.innerWidth,
          height: window.innerHeight,
          scale: window.devicePixelRatio || 1,
          fontScale: 1,
        },
        screen: {
          width: window.screen.width,
          height: window.screen.height,
          scale: window.devicePixelRatio || 1,
          fontScale: 1,
        },
      };
    }
    return {
      window: { width: 1024, height: 768, scale: 1, fontScale: 1 },
      screen: { width: 1024, height: 768, scale: 1, fontScale: 1 },
    };
  },

  /**
   * Platform-specific color scheme
   */
  getColorScheme() {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  },

  /**
   * Platform-specific accessibility info
   */
  getAccessibilityInfo() {
    return {
      isReduceMotionEnabled: false,
      isReduceTransparencyEnabled: false,
      isScreenReaderEnabled: false,
      isBoldTextEnabled: false,
      isGrayscaleEnabled: false,
      isInvertColorsEnabled: false,
    };
  },

  /**
   * Platform-specific network info
   */
  getNetworkInfo() {
    return {
      type: 'wifi',
      isConnected: true,
      isInternetReachable: true,
    };
  },

  /**
   * Platform-specific device info
   */
  getDeviceInfo() {
    return {
      brand: 'Web',
      model: 'Browser',
      systemName: 'Web',
      systemVersion: '1.0.0',
      buildNumber: '1',
      bundleId: 'com.ctronhome.web',
      uniqueId: 'web-device',
      deviceId: 'web-device',
      deviceType: 'Desktop',
    };
  },

  /**
   * Platform-specific keyboard info
   */
  getKeyboardInfo() {
    return {
      keyboardHeight: 0,
      keyboardShown: false,
    };
  },

  /**
   * Platform-specific safe area info
   */
  getSafeAreaInfo() {
    return {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    };
  },
};

// Export the utilities
export default PlatformUtilities;

// Mimic React Native's Platform object for compatibility
export const Platform = {
  OS: PlatformUtilities.getConstants().OS,
  Version: PlatformUtilities.getConstants().Version,
  isTesting: PlatformUtilities.getConstants().isTesting,
  select: PlatformUtilities.select,
  isPad: false, // Assuming not an iPad for web
  isTV: false, // Assuming not a TV for web
};

// Also export individual utilities for compatibility
export const getConstants = PlatformUtilities.getConstants;
export const isPlatform = PlatformUtilities.isPlatform;
export const select = PlatformUtilities.select;
export const getStyleSheet = PlatformUtilities.getStyleSheet;
export const getDimensions = PlatformUtilities.getDimensions;
export const getColorScheme = PlatformUtilities.getColorScheme;
export const getAccessibilityInfo = PlatformUtilities.getAccessibilityInfo;
export const getNetworkInfo = PlatformUtilities.getNetworkInfo;
export const getDeviceInfo = PlatformUtilities.getDeviceInfo;
export const getKeyboardInfo = PlatformUtilities.getKeyboardInfo;
export const getSafeAreaInfo = PlatformUtilities.getSafeAreaInfo;
