// Actual React Navigation elements assets
import { Asset } from 'expo-asset';

export default {
  'back-icon': Asset.fromModule(require('../assets/images/navigation/back-icon.png')).uri,
  'forward-icon': Asset.fromModule(require('../assets/images/navigation/forward-icon.png')).uri,
  'menu-icon': Asset.fromModule(require('../assets/images/navigation/menu-icon.png')).uri,
  // Add other navigation assets as needed
};