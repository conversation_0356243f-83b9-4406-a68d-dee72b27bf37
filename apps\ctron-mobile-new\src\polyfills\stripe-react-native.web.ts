export const initStripe = () => {};
export const useStripe = () => ({});
export const StripeProvider = ({ children }: { children: React.ReactNode }) => children;
export const CardField = () => null;
export const CardForm = () => null;
export const AuBECSDebitForm = () => null;
export const ApplePayButton = () => null;
export const GooglePayButton = () => null;
export const PaymentSheet = () => null;
export const usePaymentSheet = () => ({});
export const useApplePay = () => ({});
export const useGooglePay = () => ({});
export const confirmPayment = () => {};
export const confirmSetupIntent = () => {};
export const handleCardAction = () => {};
export const createToken = () => {};
export const createPaymentMethod = () => {};
export const retrievePaymentIntent = () => {};
export const retrieveSetupIntent = () => {};
export const openApplePaySetup = () => {};
export const presentApplePay = () => {};
export const confirmApplePayPayment = () => {};
export const isApplePaySupported = () => false;
export const presentGooglePay = () => {};
export const isGooglePaySupported = () => false;
export const createGooglePayPaymentMethod = () => {};
export const dangerouslyGet = () => ({});
export const collectBankAccount = () => {};
export const collectFinancialConnectionsAccounts = () => {};
export const verifyMicrodeposits = () => {};
export const useThreeDSecure = () => ({});
export const useConfirmPayment = () => ({});
export const useConfirmSetupIntent = () => ({});
export const useCardForm = () => ({});
export const useCardField = () => ({});
export const useAuBECSDebitForm = () => ({});
export const useFinancialConnections = () => ({});
export const useBankAccount = () => ({});
export const useMicrodeposits = () => ({});
export const usePaymentIntent = () => ({});
export const useSetupIntent = () => ({});
export const useCustomer = () => ({});
export const useEphemeralKey = () => ({});
export const usePaymentMethod = () => ({});
export const useSource = () => ({});
export const useToken = () => ({});
export const useConfirmCardPayment = () => ({});
export const useConfirmCardSetupIntent = () => ({});
export const useConfirmAuBECSDebitPayment = () => ({});
export const useConfirmAuBECSDebitSetupIntent = () => ({});
export const useConfirmUSBankAccountPayment = () => ({});
export const useConfirmUSBankAccountSetupIntent = () => ({});
export const useConfirmCashAppPayPayment = () => ({});
export const useConfirmCashAppPaySetupIntent = () => ({});
export const useConfirmKlarnaPayment = () => ({});
export const useConfirmKlarnaSetupIntent = () => ({});
export const useConfirmIdealPayment = () => ({});
export const useConfirmIdealSetupIntent = () => ({});
export const useConfirmBancontactPayment = () => ({});
export const useConfirmBancontactSetupIntent = () => ({});
export const useConfirmGiropayPayment = () => ({});
export const useConfirmGiropaySetupIntent = () => ({});
export const useConfirmSofortPayment = () => ({});
export const useConfirmSofortSetupIntent = () => ({});
export const useConfirmEPSPayment = () => ({});
export const useConfirmEPSSetupIntent = () => ({});
export const useConfirmP24Payment = () => ({});
export const useConfirmP24SetupIntent = () => ({});
export const useConfirmAlipayPayment = () => ({});
export const useConfirmAlipaySetupIntent = () => ({});
export const useConfirmWeChatPayPayment = () => ({});
export const useConfirmWeChatPaySetupIntent = () => ({});
export const useConfirmGrabPayPayment = () => ({});
export const useConfirmGrabPaySetupIntent = () => ({});
export const useConfirmFpxPayment = () => ({});
export const useConfirmFpxSetupIntent = () => ({});
export const useConfirmOxxoPayment = () => ({});
export const useConfirmOxxoSetupIntent = () => ({});
export const useConfirmBoletoPayment = () => ({});
export const useConfirmBoletoSetupIntent = () => ({});
export const useConfirmAfterpayClearpayPayment = () => ({});
export const useConfirmAfterpayClearpaySetupIntent = () => ({});
export const useConfirmAffirmPayment = () => ({});
export const useConfirmAffirmSetupIntent = () => ({});
export const useConfirmPaypalPayment = () => ({});
export const useConfirmPaypalSetupIntent = () => ({});
export const useConfirmCashAppPay = () => ({});
export const useConfirmKlarna = () => ({});
export const useConfirmIdeal = () => ({});
export const useConfirmBancontact = () => ({});
export const useConfirmGiropay = () => ({});
export const useConfirmSofort = () => ({});
export const useConfirmEPS = () => ({});
export const useConfirmP24 = () => ({});
export const useConfirmAlipay = () => ({});
export const useConfirmWeChatPay = () => ({});
export const useConfirmGrabPay = () => ({});
export const useConfirmFpx = () => ({});
export const useConfirmOxxo = () => ({});
export const useConfirmBoleto = () => ({});
export const useConfirmAfterpayClearpay = () => ({});
export const useConfirmAffirm = () => ({});
export const useConfirmPaypal = () => ({});
export const useConfirmCard = () => ({});
export const useConfirmUSBankAccount = () => ({});
export const useConfirmAuBECSDebit = () => ({});
export const useConfirmPaymentMethod = () => ({});
export const useConfirmSetupIntentMethod = () => ({});
export const useConfirmPaymentIntent = () => ({});
export const useConfirmSetupIntent = () => ({});
export const useConfirmPaymentIntentWithPaymentMethod = () => ({});
export const useConfirmSetupIntentWithPaymentMethod = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodId = () => ({});
export const useConfirmSetupIntentWithPaymentMethodId = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodOptions = () => ({});
export const useConfirmSetupIntentWithPaymentMethodOptions = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodData = () => ({});
export const useConfirmSetupIntentWithPaymentMethodData = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodType = () => ({});
export const useConfirmSetupIntentWithPaymentMethodType = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeData = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeData = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeOptions = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeOptions = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataAndOptions = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataAndOptions = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataAndParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataAndParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeOptionsAndParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeOptionsAndParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsAndParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsAndParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsAndClientSecret = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsAndClientSecret = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretAndReturnUrl = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretAndReturnUrl = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlAndShipping = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlAndShipping = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingAndBilling = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingAndBilling = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingAndMetadata = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingAndMetadata = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataAndDescription = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataAndDescription = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionAndStatementDescriptor = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionAndStatementDescriptor = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorAndTransferData = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorAndTransferData = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataAndOnBehalfOf = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataAndOnBehalfOf = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfAndApplicationFeeAmount = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfAndApplicationFeeAmount = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountAndCaptureMethod = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountAndCaptureMethod = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodAndConfirmationMethod = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodAndConfirmationMethod = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodAndSetupFutureUsage = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodAndSetupFutureUsage = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageAndUsage = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageAndUsage = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageAndReceiptEmail = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageAndReceiptEmail = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailAndPaymentMethodData = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailAndPaymentMethodData = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataAndPaymentMethodOptions = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataAndPaymentMethodOptions = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsAndPaymentMethodType = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsAndPaymentMethodType = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypeAndPaymentMethodTypeData = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypeAndPaymentMethodTypeData = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataAndPaymentMethodTypeOptions = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataAndPaymentMethodTypeOptions = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsAndPaymentMethodTypeParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsAndPaymentMethodTypeParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsAndPaymentMethodTypeDataOptions = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsAndPaymentMethodTypeDataOptions = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsAndPaymentMethodTypeDataParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsAndPaymentMethodTypeDataParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmPaymentIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceiptEmailPaymentMethodDataPaymentMethodOptionsPaymentMethodTypePaymentMethodTypeDataPaymentMethodTypeOptionsPaymentMethodTypeParamsPaymentMethodTypeDataOptionsPaymentMethodTypeDataParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsPaymentMethodTypeDataOptionsParamsAndPaymentMethodTypeDataOptionsParams = () => ({});
export const useConfirmSetupIntentWithPaymentMethodTypeDataOptionsParamsClientSecretReturnUrlShippingBillingMetadataDescriptionStatementDescriptorTransferDataOnBehalfOfApplicationFeeAmountCaptureMethodConfirmationMethodSetupFutureUsageUsageReceipt