// CTRON Home - Admin Dashboard Screen
// Comprehensive admin interface for mobile platform

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import api from '../../services/api';


interface DashboardMetrics {
  totalRevenue: number;
  jobsToday: number;
  onlineTechnicians: number;
  pendingJobs: number;
  totalUsers: number;
  activeJobs: number;
}

interface RecentActivity {
  id: string;
  type: 'job_created' | 'payment_completed' | 'technician_approved' | 'user_registered';
  message: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high';
}

export default function AdminDashboardScreen() {
  const navigation = useNavigation<any>();
  const { user, logout } = useAuth();
  const { colors, spacing, typography, borderRadius } = useTheme();

  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalRevenue: 0,
    jobsToday: 0,
    onlineTechnicians: 0,
    pendingJobs: 0,
    totalUsers: 0,
    activeJobs: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load dashboard metrics
      const metricsResponse = await api.get('/api/dashboard/metrics');
      setMetrics(metricsResponse.data);

      // Load recent activity (mock data for now)
      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'job_created',
          message: 'New plumbing job created by John Doe',
          timestamp: new Date().toISOString(),
          priority: 'medium',
        },
        {
          id: '2',
          type: 'payment_completed',
          message: 'Payment of £150 completed for Job #12345',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          priority: 'low',
        },
        {
          id: '3',
          type: 'technician_approved',
          message: 'Technician Mike Smith approved',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          priority: 'high',
        },
      ];
      setRecentActivity(mockActivity);

    } catch (error: any) {
      console.error('Failed to load dashboard data:', error);
      Alert.alert(
        'Error',
        'Failed to load dashboard data. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return colors.error.main || '#ef4444';
      case 'medium': return colors.warning.main || '#f59e0b';
      case 'low': return colors.success.main || '#10b981';
      default: return colors.text.secondary;
    }
  };

  const renderMetricCard = (title: string, value: string | number, icon: string, onPress?: () => void) => (
    <TouchableOpacity
      style={styles.metricCard}
      onPress={onPress}
      disabled={!onPress}
    >
      <Card style={styles.metricCardInner}>
        <Text style={styles.metricIcon}>{icon}</Text>
        <Text style={styles.metricValue}>{value}</Text>
        <Text style={styles.metricTitle}>{title}</Text>
      </Card>
    </TouchableOpacity>
  );

  const renderActivityItem = (item: RecentActivity) => (
    <View key={item.id} style={styles.activityItem}>
      <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(item.priority) }]} />
      <View style={styles.activityContent}>
        <Text style={styles.activityMessage}>{item.message}</Text>
        <Text style={styles.activityTime}>{formatTime(item.timestamp)}</Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    scrollView: {
      flex: 1,
      padding: spacing.md,
    },
    welcomeSection: {
      marginBottom: spacing.lg,
      paddingHorizontal: spacing.sm,
    },
    welcomeText: {
      ...typography.h5,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    adminName: {
      ...typography.h3,
      color: colors.text.primary,
      fontWeight: 'bold',
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: spacing.lg,
    },
    metricCard: {
      width: '48%',
      marginBottom: spacing.md,
    },
    metricCardInner: {
      padding: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 120,
    },
    metricIcon: {
      fontSize: 32,
      marginBottom: spacing.sm,
    },
    metricValue: {
      ...typography.h4,
      fontWeight: 'bold',
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    metricTitle: {
      ...typography.body2,
      color: colors.text.secondary,
      textAlign: 'center',
    },
    sectionTitle: {
      ...typography.h4,
      color: colors.text.primary,
      fontWeight: 'bold',
      marginBottom: spacing.md,
      paddingHorizontal: spacing.sm,
    },
    activityItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      padding: spacing.md,
      borderRadius: borderRadius.md,
      marginBottom: spacing.sm,
      marginHorizontal: spacing.sm,
    },
    priorityIndicator: {
      width: 8,
      height: '100%',
      borderRadius: borderRadius.sm,
      marginRight: spacing.sm,
    },
    activityContent: {
      flex: 1,
    },
    activityMessage: {
      ...typography.body1,
      color: colors.text.primary,
    },
    activityTime: {
      ...typography.caption,
      color: colors.text.tertiary,
      marginTop: spacing.xs,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      ...typography.body1,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    logoutIcon: {
      fontSize: 24,
    },
    section: {
      padding: spacing.lg,
      paddingTop: spacing.sm,
    },
    quickActions: {
      gap: spacing.md,
    },
    actionButton: {
      marginBottom: spacing.sm,
    },
    activityCard: {
      padding: spacing.lg,
    },
    emptyText: {
      textAlign: 'center',
      color: colors.text.tertiary,
      fontStyle: 'italic',
    },
  }), [];

  return ( 
    <View style={styles.container}>
      <Header
        title="Admin Dashboard"
        rightAction={{
          icon: <Text style={styles.logoutIcon}>👤</Text>,
          onPress: () => logout(true),
          accessibilityLabel: 'User menu',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Welcome back,</Text>
          <Text style={styles.adminName}>{user?.fullName || 'Admin'} 👋</Text>
        </View>

        {/* Metrics Grid */}
        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Total Revenue',
            formatCurrency(metrics.totalRevenue),
            '💰',
            () => navigation.navigate('AdminJobs')
          )}
          {renderMetricCard(
            'Jobs Today',
            metrics.jobsToday.toString(),
            '📋',
            () => navigation.navigate('AdminJobs')
          )}
          {renderMetricCard(
            'Online Techs',
            metrics.onlineTechnicians.toString(),
            '👷',
            () => navigation.navigate('AdminTechnicians')
          )}
          {renderMetricCard(
            'Pending Jobs',
            metrics.pendingJobs.toString(),
            '⏳'
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <Button
              title="Manage Jobs"
              onPress={() => navigation.navigate('AdminJobs')}
              variant="primary"
              size="lg"
              style={styles.actionButton}
            />
            <Button
              title="Approve Technicians"
              onPress={() => navigation.navigate('AdminTechnicians')}
              variant="secondary"
              size="lg"
              style={styles.actionButton}
            />
            <Button
              title="System Settings"
              onPress={() => navigation.navigate('AdminSettings')}
              variant="outline"
              size="lg"
              style={styles.actionButton}
            />
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <Card style={styles.activityCard}>
            {recentActivity.length > 0 ? (
              recentActivity.map(renderActivityItem)
            ) : (
              <Text style={styles.emptyText}>No recent activity</Text>
            )}
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}
