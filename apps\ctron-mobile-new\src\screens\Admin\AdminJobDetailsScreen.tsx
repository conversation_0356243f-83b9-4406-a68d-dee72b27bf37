// CTRON Home - Admin Job Details Screen
// Detailed job view with admin controls

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  Alert,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import api from '../../services/api';
import { colors, spacing, typography, borderRadius } from '@/theme';


interface JobDetails {
  id: string;
  issue: string;
  description?: string;
  status: 'PENDING' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'low' | 'medium' | 'high';
  scheduledAt: string;
  createdAt: string;
  completedAt?: string;
  photoUrl?: string;
  proofImageKey?: string;
  latitude?: number;
  longitude?: number;
  user: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
  };
  technician?: {
    id: string;
    user: {
      fullName: string;
      email: string;
      phone?: string;
    };
  };
  payment?: {
    id: string;
    amount: number;
    isReleased: boolean;
    isFrozen: boolean;
    createdAt: string;
  };
  chat?: {
    id: string;
  };
}

export default function AdminJobDetailsScreen() {
  const { colors } = useTheme();
  const route = useRoute<AdminJobDetailsScreenRouteProp>();
  const { jobId } = route.params;
  const [job, setJob] = useState<JobDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  const navigation = useNavigation();

  const loadJobDetails = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/jobs/${jobId}`);
      setJob(response.data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load job details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  }, [jobId, navigation]);

  useEffect(() => {
    loadJobDetails();
  }, [loadJobDetails]);



  const handlePaymentAction = useCallback(async (action: 'release' | 'freeze') => {
    if (!job?.payment) return;

    try {
      setActionLoading(true);
      const endpoint = action === 'release' 
        ? `/api/payments/${job.id}/release-manually`
        : `/api/payments/${job.id}/freeze`;
      
      await api.patch(endpoint);
      Alert.alert('Success', `Payment ${action}d successfully`);
      loadJobDetails(); // Refresh job details
    } catch (error: any) {
      Alert.alert('Error', `Failed to ${action} payment`);
    } finally {
      setActionLoading(false);
    }
  }, [job, loadJobDetails]);

  const handleOpenChat = useCallback(() => {
    if (job?.chat) {
      navigation.navigate('Chat', {
        chatId: job.chat.id,
        jobTitle: job.issue,
      });
    }
  }, [job, navigation]);

  const handleOpenLocation = useCallback(() => {
    if (job?.latitude && job?.longitude) {
      const url = `https://maps.google.com/?q=${job.latitude},${job.longitude}`;
      Linking.openURL(url);
    }
  }, [job]);

  const handleContactUser = useCallback((phone?: string) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.warning.main || '#f59e0b';
      case 'ASSIGNED': return colors.info?.main || '#3b82f6';
      case 'IN_PROGRESS': return colors.primary.main;
      case 'COMPLETED': return colors.success.main || '#10b981';
      case 'CANCELLED': return colors.error.main || '#ef4444';
      default: return colors.text.secondary;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return colors.error.main || '#ef4444';
      case 'medium': return colors.warning.main || '#f59e0b';
      case 'low': return colors.success.main || '#10b981';
      default: return colors.text.secondary;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading job details...</Text>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Job not found</Text>
        <Button
          title="Go Back"
          onPress={() => navigation.goBack()}
          variant="primary"
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Job Details"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
      />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Job Header */}
        <Card style={styles.headerCard}>
          <View style={styles.jobHeader}>
            <Text style={styles.jobTitle}>{job.issue}</Text>
            <View style={styles.badges}>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(job.status) }]}>
                <Text style={styles.badgeText}>{job.status}</Text>
              </View>
              <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(job.priority) }]}>
                <Text style={styles.badgeText}>{job.priority.toUpperCase()}</Text>
              </View>
            </View>
          </View>
          {job.description && (
            <Text style={styles.jobDescription}>{job.description}</Text>
          )}
        </Card>

        {/* Job Images */}
        {(job.photoUrl || job.proofImageKey) && (
          <Card style={styles.imagesCard}>
            <Text style={styles.sectionTitle}>Images</Text>
            <View style={styles.imagesContainer}>
              {job.photoUrl && (
                <View style={styles.imageContainer}>
                  <Text style={styles.imageLabel}>Job Photo</Text>
                  <Image source={{ uri: job.photoUrl }} style={styles.jobImage} />
                </View>
              )}
              {job.proofImageKey && (
                <View style={styles.imageContainer}>
                  <Text style={styles.imageLabel}>Proof of Work</Text>
                  <Image source={{ uri: job.proofImageKey }} style={styles.jobImage} />
                </View>
              )}
            </View>
          </Card>
        )}

        {/* Customer Information */}
        <Card style={styles.customerCard}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>{job.user.fullName}</Text>
            <Text style={styles.contactDetail}>{job.user.email}</Text>
            {job.user.phone && (
              <Button
                title={`Call ${job.user.phone}`}
                onPress={() => handleContactUser(job.user.phone)}
                variant="outline"
                size="sm"
                style={styles.contactButton}
              />
            )}
          </View>
        </Card>

        {/* Technician Information */}
        {job.technician && (
          <Card style={styles.technicianCard}>
            <Text style={styles.sectionTitle}>Assigned Technician</Text>
            <View style={styles.contactInfo}>
              <Text style={styles.contactName}>{job.technician.user.fullName}</Text>
              <Text style={styles.contactDetail}>{job.technician.user.email}</Text>
              {job.technician.user.phone && (
                <Button
                  title={`Call ${job.technician.user.phone}`}
                  onPress={() => job.technician && handleContactUser(job.technician.user.phone)}
                  variant="outline"
                  size="sm"
                  style={styles.contactButton}
                />
              )}
            </View>
          </Card>
        )}

        {/* Job Timeline */}
        <Card style={styles.timelineCard}>
          <Text style={styles.sectionTitle}>Timeline</Text>
          <View style={styles.timelineItem}>
            <Text style={styles.timelineLabel}>Created:</Text>
            <Text style={styles.timelineValue}>{formatDateTime(job.createdAt)}</Text>
          </View>
          <View style={styles.timelineItem}>
            <Text style={styles.timelineLabel}>Scheduled:</Text>
            <Text style={styles.timelineValue}>{formatDateTime(job.scheduledAt)}</Text>
          </View>
          {job.completedAt && (
            <View style={styles.timelineItem}>
              <Text style={styles.timelineLabel}>Completed:</Text>
              <Text style={styles.timelineValue}>{formatDateTime(job.completedAt)}</Text>
            </View>
          )}
        </Card>

        {/* Payment Information */}
        {job.payment && (
          <Card style={styles.paymentCard}>
            <Text style={styles.sectionTitle}>Payment Information</Text>
            <View style={styles.paymentInfo}>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Amount:</Text>
                <Text style={styles.paymentAmount}>{formatCurrency(job.payment.amount)}</Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Status:</Text>
                <Text style={[
                  styles.paymentStatus,
                  { color: job.payment.isReleased ? colors.success.main : colors.warning.main }
                ]}>
                  {job.payment.isFrozen ? 'FROZEN' : job.payment.isReleased ? 'RELEASED' : 'PENDING'}
                </Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Created:</Text>
                <Text style={styles.paymentDate}>{formatDateTime(job.payment.createdAt)}</Text>
              </View>
            </View>

            {/* Payment Actions */}
            <View style={styles.paymentActions}>
              {!job.payment.isReleased && !job.payment.isFrozen && (
                <Button
                  title="Release Payment"
                  onPress={() => handlePaymentAction('release')}
                  variant="primary"
                  loading={actionLoading}
                  disabled={actionLoading}
                  style={styles.paymentActionButton}
                />
              )}
              {!job.payment.isFrozen && (
                <Button
                  title="Freeze Payment"
                  onPress={() => handlePaymentAction('freeze')}
                  variant="outline"
                  loading={actionLoading}
                  disabled={actionLoading}
                  style={styles.paymentActionButton}
                />
              )}
            </View>
          </Card>
        )}

        {/* Quick Actions */}
        <Card style={styles.actionsCard}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            {job.chat && (
              <Button
                title="Open Chat"
                onPress={handleOpenChat}
                variant="primary"
                style={styles.actionButton}
              />
            )}
            {job.latitude && job.longitude && (
              <Button
                title="View Location"
                onPress={handleOpenLocation}
                variant="secondary"
                style={styles.actionButton}
              />
            )}
          </View>
        </Card>
      </ScrollView>
    </View>
  );
}

  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.secondary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing.lg,
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.lg,
    },
    errorText: {
      fontSize: typography.fontSize.lg,
      color: colors.text.secondary,
      marginBottom: spacing.lg,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: spacing.lg,
    },
    headerCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    jobHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: spacing.md,
    },
    jobTitle: {
      flex: 1,
      fontSize: typography.fontSize.lg,
      fontWeight: '600' as const,
      color: colors.text.primary,
      marginRight: spacing.md,
    },
    badges: {
      gap: spacing.sm,
    },
    statusBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    priorityBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    badgeText: {
      fontSize: typography.fontSize.xs,
      color: colors.background.primary,
      fontWeight: '600' as const,
      textAlign: 'center',
    },
    jobDescription: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      lineHeight: 22,
    },
    imagesCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: '600' as const,
      color: colors.text.primary,
      marginBottom: spacing.md,
    },
    imagesContainer: {
      gap: spacing.lg,
    },
    imageContainer: {
      alignItems: 'center',
    },
    imageLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    jobImage: {
      width: 200,
      height: 150,
      borderRadius: borderRadius.md,
      backgroundColor: colors.background.secondary,
    },
    customerCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    technicianCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    contactInfo: {
      gap: spacing.sm,
    },
    contactName: {
      fontSize: typography.fontSize.base,
      fontWeight: '600' as const,
      color: colors.text.primary,
    },
    contactDetail: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    contactButton: {
      alignSelf: 'flex-start',
      marginTop: spacing.sm,
    },
    timelineCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    timelineItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    timelineLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    timelineValue: {
      fontSize: typography.fontSize.sm,
      color: colors.text.primary,
      fontWeight: '500' as const,
    },
    paymentCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    paymentInfo: {
      marginBottom: spacing.lg,
    },
    paymentRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    paymentLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    paymentAmount: {
      fontSize: typography.fontSize.base,
      fontWeight: '600' as const,
      color: colors.text.primary,
    },
    paymentStatus: {
      fontSize: typography.fontSize.sm,
      fontWeight: '600' as const,
    },
    paymentDate: {
      fontSize: typography.fontSize.sm,
      color: colors.text.primary,
    },
    paymentActions: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    paymentActionButton: {
      flex: 1,
    },
    actionsCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    quickActions: {
      gap: spacing.md,
    },
    actionButton: {
      marginBottom: spacing.sm,
    },
    backIcon: {
      fontSize: 24,
      color: colors.text.secondary,
    },
  }), [colors]);
