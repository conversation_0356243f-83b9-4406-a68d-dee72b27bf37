// CTRON Home - Admin Settings Screen
// System configuration and settings management

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,

  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import api from '../../services/api';
import { colors, spacing, typography, borderRadius } from '@/theme';
// ... existing code ...

interface Settings {
  gracePeriodHours: number;
  stripeTestMode: boolean;
  statusMessage: string;
  maintenanceMode: boolean;
  maxJobsPerTechnician: number;
  commissionRate: number;
}

export default function AdminSettingsScreen() {
  const navigation = useNavigation<any>();
  const { colors } = useTheme();
  const [settings, setSettings] = useState<Settings>({
    gracePeriodHours: 24,
    stripeTestMode: false,
    statusMessage: 'System operational',
    maintenanceMode: false,
    maxJobsPerTechnician: 10,
    commissionRate: 0.15,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/dashboard/settings');
      setSettings({
        gracePeriodHours: response.data.gracePeriodHours || 24,
        stripeTestMode: response.data.stripeTestMode || false,
        statusMessage: response.data.statusMessage || 'System operational',
        maintenanceMode: response.data.maintenanceMode || false,
        maxJobsPerTechnician: response.data.maxJobsPerTechnician || 10,
        commissionRate: response.data.commissionRate || 0.15,
      });
    } catch (error: any) {
      console.error('Failed to load settings:', error);
      Alert.alert('Error', 'Failed to load settings. Using defaults.');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      await api.put('/api/dashboard/settings', settings);
      Alert.alert('Success', 'Settings updated successfully');
    } catch (error: any) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof Settings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const renderSettingCard = (
    title: string,
    description: string,
    children: React.ReactNode
  ) => (
    <Card style={styles.settingCard}>
      <View style={styles.settingHeader}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <View style={styles.settingControl}>
        {children}
      </View>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="System Settings"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
      />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Payment Settings */}
        {renderSettingCard(
          'Payment Grace Period',
          'Hours before payment is automatically released to technician',
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.numberInput}
              value={settings.gracePeriodHours.toString()}
              onChangeText={(text: string) => updateSetting('gracePeriodHours', parseInt(text) || 24)}
              keyboardType="numeric"
              placeholder="24"
            />
            <Text style={styles.inputSuffix}>hours</Text>
          </View>
        )}

        {renderSettingCard(
          'Stripe Test Mode',
          'Enable test mode for payment processing',
          <TouchableOpacity
            style={[
              styles.switch,
              { backgroundColor: settings.stripeTestMode ? colors.primary[500] : colors.neutral[300] }
            ]}
            onPress={() => updateSetting('stripeTestMode', !settings.stripeTestMode)}
          >
            <View
              style={[
                styles.switchThumb,
                {
                  backgroundColor: colors.neutral[50],
                  transform: [{ translateX: settings.stripeTestMode ? 20 : 0 }]
                }
              ]}
            />
          </TouchableOpacity>
        )}

        {/* System Settings */}
        {renderSettingCard(
          'System Status Message',
          'Message displayed to users about system status',
          <TextInput
            style={styles.textInput}
            value={settings.statusMessage}
            onChangeText={(text: any) => updateSetting('statusMessage', text)}
            placeholder="System operational"
            multiline
          />
        )}

        {renderSettingCard(
          'Maintenance Mode',
          'Temporarily disable new job creation',
          <TouchableOpacity
            style={[
              styles.switch,
              { backgroundColor: settings.maintenanceMode ? colors.warning.main : colors.neutral[300] }
            ]}
            onPress={() => updateSetting('maintenanceMode', !settings.maintenanceMode)}
          >
            <View
              style={[
                styles.switchThumb,
                {
                  backgroundColor: colors.neutral[50],
                  transform: [{ translateX: settings.maintenanceMode ? 20 : 0 }]
                }
              ]}
            />
          </TouchableOpacity>
        )}

        {/* Business Settings */}
        {renderSettingCard(
          'Max Jobs per Technician',
          'Maximum number of active jobs a technician can have',
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.numberInput}
              value={settings.maxJobsPerTechnician.toString()}
              onChangeText={(text: string) => updateSetting('maxJobsPerTechnician', parseInt(text) || 10)}
              keyboardType="numeric"
              placeholder="10"
            />
            <Text style={styles.inputSuffix}>jobs</Text>
          </View>
        )}

        {renderSettingCard(
          'Commission Rate',
          'Platform commission rate (as decimal, e.g., 0.15 = 15%)',
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.numberInput}
              value={settings.commissionRate.toString()}
              onChangeText={(text: string) => updateSetting('commissionRate', parseFloat(text) || 0.15)}
              keyboardType="decimal-pad"
              placeholder="0.15"
            />
            <Text style={styles.inputSuffix}>
              ({Math.round(settings.commissionRate * 100)}%)
            </Text>
          </View>
        )}

        {/* Save Button */}
        <View style={styles.container}>
          <Button
            title={saving ? 'Saving...' : 'Save Settings'}
            onPress={saveSettings}
            variant="primary"
            size="lg"
            disabled={saving}
            loading={saving}
            fullWidth
          />
        </View>

        {/* System Information */}
        <Card style={styles.infoCard}>
          <Text style={styles.infoTitle}>System Information</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>App Version:</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Environment:</Text>
            <Text style={styles.infoValue}>Development</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Last Updated:</Text>
            <Text style={styles.infoValue}>{new Date().toLocaleDateString()}</Text>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.md,
  },
  settingCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  settingHeader: {
    marginBottom: spacing.sm,
  },
  settingTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  settingDescription: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.normal,
    color: colors.text.secondary,
  },
  settingControl: {
    marginTop: spacing.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing.sm,
    height: 40,
  },
  numberInput: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
  },
  inputSuffix: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
  textInput: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.sm,
    padding: spacing.sm,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
    marginTop: spacing.md,
  },
  saveButtonContainer: {
    padding: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
    backgroundColor: colors.background.primary,
  },
  backIcon: {
    fontSize: 24,
    color: colors.text.primary,
  },
  infoCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
  },
  infoTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  infoLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
  },
  infoValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.normal,
    color: colors.text.primary,
  },
  switch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    padding: 2,
    justifyContent: 'center',
  },
  switchThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
  },
}), [colors, spacing, typography]);
