// CTRON Home - Admin Settings Screen
// System configuration and settings management

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import api from '../../services/api';
import { colors, spacing, typography, borderRadius } from '@/theme';
// ... existing code ...

interface Settings {
  gracePeriodHours: number;
  stripeTestMode: boolean;
  statusMessage: string;
  maintenanceMode: boolean;
  maxJobsPerTechnician: number;
  commissionRate: number;
}

export default function AdminSettingsScreen() {
  const navigation = useNavigation<any>();
  const { colors } = useTheme();
  const [settings, setSettings] = useState<Settings>({
    gracePeriodHours: 24,
    stripeTestMode: false,
    statusMessage: 'System operational',
    maintenanceMode: false,
    maxJobsPerTechnician: 10,
    commissionRate: 0.15,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/dashboard/settings');
      setSettings({
        gracePeriodHours: response.data.gracePeriodHours || 24,
        stripeTestMode: response.data.stripeTestMode || false,
        statusMessage: response.data.statusMessage || 'System operational',
        maintenanceMode: response.data.maintenanceMode || false,
        maxJobsPerTechnician: response.data.maxJobsPerTechnician || 10,
        commissionRate: response.data.commissionRate || 0.15,
      });
    } catch (error: any) {
      console.error('Failed to load settings:', error);
      Alert.alert('Error', 'Failed to load settings. Using defaults.');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      await api.put('/api/dashboard/settings', settings);
      Alert.alert('Success', 'Settings updated successfully');
    } catch (error: any) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof Settings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const renderSettingCard = (
    title: string,
    description: string,
    children: React.ReactNode
  ) => (
    <Card style={styles.settingCard}>
      <View style={styles.settingHeader}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <View style={styles.settingControl}>
        {children}
      </View>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="System Settings"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
      />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Payment Settings */}
        {renderSettingCard(
          'Payment Grace Period',
          'Hours before payment is automatically released to technician',
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.numberInput}
              value={settings.gracePeriodHours.toString()}
              onChangeText={(text: string) => updateSetting('gracePeriodHours', parseInt(text) || 24)}
              keyboardType="numeric"
              placeholder="24"
            />
            <Text style={styles.inputSuffix}>hours</Text>
          </View>
        )}

        {renderSettingCard(
          'Stripe Test Mode',
          'Enable test mode for payment processing',
          <Switch
            value={settings.stripeTestMode}
            onValueChange={(value: any) => updateSetting('stripeTestMode', value)}
            trackColor={{ false: colors.border.medium, true: colors.primary[300] }}
            thumbColor={settings.stripeTestMode ? colors.primary.main : colors.background.primary}
          />
        )}

        {/* System Settings */}
        {renderSettingCard(
          'System Status Message',
          'Message displayed to users about system status',
          <TextInput
            style={styles.textInput}
            value={settings.statusMessage}
            onChangeText={(text: any) => updateSetting('statusMessage', text)}
            placeholder="System operational"
            multiline
          />
        )}

        {renderSettingCard(
          'Maintenance Mode',
          'Temporarily disable new job creation',
          <Switch
            value={settings.maintenanceMode}
            onValueChange={(value: any) => updateSetting('maintenanceMode', value)}
            trackColor={{ false: colors.border.medium, true: colors.warning.main }}
            thumbColor={settings.maintenanceMode ? colors.warning.main : colors.background.primary}
          />
        )}

        {/* Business Settings */}
        {renderSettingCard(
          'Max Jobs per Technician',
          'Maximum number of active jobs a technician can have',
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.numberInput}
              value={settings.maxJobsPerTechnician.toString()}
              onChangeText={(text: string) => updateSetting('maxJobsPerTechnician', parseInt(text) || 10)}
              keyboardType="numeric"
              placeholder="10"
            />
            <Text style={styles.inputSuffix}>jobs</Text>
          </View>
        )}

        {renderSettingCard(
          'Commission Rate',
          'Platform commission rate (as decimal, e.g., 0.15 = 15%)',
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.numberInput}
              value={settings.commissionRate.toString()}
              onChangeText={(text: string) => updateSetting('commissionRate', parseFloat(text) || 0.15)}
              keyboardType="decimal-pad"
              placeholder="0.15"
            />
            <Text style={styles.inputSuffix}>
              ({Math.round(settings.commissionRate * 100)}%)
            </Text>
          </View>
        )}

        {/* Save Button */}
        <View style={styles.saveContainer}>
          <Button
            title={saving ? 'Saving...' : 'Save Settings'}
            onPress={saveSettings}
            variant="primary"
            size="lg"
            disabled={saving}
            loading={saving}
            fullWidth
          />
        </View>

        {/* System Information */}
        <Card style={styles.infoCard}>
          <Text style={styles.infoTitle}>System Information</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>App Version:</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Environment:</Text>
            <Text style={styles.infoValue}>Development</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Last Updated:</Text>
            <Text style={styles.infoValue}>{new Date().toLocaleDateString()}</Text>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
}

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.default,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: spacing.md,
    },
    settingCard: {
      marginBottom: spacing.md,
      padding: spacing.md,
    },
    settingHeader: {
      marginBottom: spacing.sm,
    },
    settingTitle: {
      ...typography.h6,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    settingDescription: {
      ...typography.body2,
      color: colors.text.secondary,
    },
    settingControl: {
      marginTop: spacing.sm,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      borderRadius: borderRadius.sm,
      paddingHorizontal: spacing.sm,
      height: 40,
    },
    numberInput: {
      flex: 1,
      ...typography.body1,
      color: colors.text.primary,
    },
    inputSuffix: {
      ...typography.body1,
      color: colors.text.secondary,
      marginLeft: spacing.xs,
    },
    textInput: {
      backgroundColor: colors.background.secondary,
      borderRadius: borderRadius.sm,
      padding: spacing.sm,
      ...typography.body1,
      color: colors.text.primary,
      minHeight: 80,
      textAlignVertical: 'top',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.default,
    },
    loadingText: {
      ...typography.body1,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    saveButtonContainer: {
      padding: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
      backgroundColor: colors.background.default,
    },
    backIcon: {
      fontSize: 24,
      color: colors.text.primary,
    },
  }), [colors, spacing]);
