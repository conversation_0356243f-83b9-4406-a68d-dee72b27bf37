// CTRON Home - Admin Technicians Management Screen
// Technician approval and management interface

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import api from '../../services/api';
import { colors, spacing, typography, borderRadius } from '@/theme';

// ... existing code ...

interface Technician {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  specializations: string[];
  rating: number;
  isAvailable: boolean;
  createdAt: string;
  user: {
    fullName: string;
    email: string;
    phone?: string;
  };
}

export default function AdminTechniciansScreen() {
  const navigation = useNavigation<any>();
  const { colors } = useTheme();
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');

  const loadTechnicians = useCallback(async () => {
    try {
      setLoading(true);
      const endpoint = filter === 'pending' 
        ? '/api/technicians/pending'
        : '/api/technicians';
      
      const response = await api.get(endpoint, {
        params: filter !== 'all' && filter !== 'pending' ? { status: filter } : {},
      });
      setTechnicians(response.data || []);
    } catch (error) {
      console.error('Failed to load technicians:', error);
      Alert.alert('Error', 'Failed to load technicians. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [filter]);

  useEffect(() => {
    loadTechnicians();
  }, [loadTechnicians]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTechnicians();
    setRefreshing(false);
  };

  const handleApprove = async (technicianId: string) => {
    try {
      await api.patch(`/api/technicians/${technicianId}/approve`);
      Alert.alert('Success', 'Technician approved successfully');
      loadTechnicians(); // Refresh the list
    } catch {
      Alert.alert('Error', 'Failed to approve technician');
    }
  };

  const handleReject = async (technicianId: string) => {
    Alert.alert(
      'Confirm Rejection',
      'Are you sure you want to reject this technician?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            try {
              await api.patch(`/api/technicians/${technicianId}/reject`);
              Alert.alert('Success', 'Technician rejected');
              loadTechnicians();
            } catch {
              Alert.alert('Error', 'Failed to reject technician');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.warning.main || '#f59e0b';
      case 'APPROVED': return colors.success.main || '#10b981';
      case 'REJECTED': return colors.error.main || '#ef4444';
      default: return colors.text.secondary;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const renderFilterButton = (filterType: typeof filter, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive,
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Text
        style={[
          styles.filterButtonText,
          filter === filterType && styles.filterButtonTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderTechnicianItem = ({ item }: { item: Technician }) => (
    <Card style={styles.technicianCard}>
      <View style={styles.technicianHeader}>
        <View style={styles.avatarContainer}>
          <Text style={styles.avatarText}>
            {(item.user?.fullName || item.fullName || 'T').charAt(0).toUpperCase()}
          </Text>
        </View>
        <View style={styles.technicianInfo}>
          <Text style={styles.technicianName}>
            {item.user?.fullName || item.fullName}
          </Text>
          <Text style={styles.technicianEmail}>
            {item.user?.email || item.email}
          </Text>
          {(item.user?.phone || item.phone) && (
            <Text style={styles.technicianPhone}>
              {item.user?.phone || item.phone}
            </Text>
          )}
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.kycStatus) }]}>
            <Text style={styles.statusText}>{item.kycStatus}</Text>
          </View>
          {item.isAvailable && (
            <Text style={styles.availableText}>Available</Text>
          )}
        </View>
      </View>

      {item.specializations && item.specializations.length > 0 && (
        <View style={styles.specializationsContainer}>
          <Text style={styles.specializationsLabel}>Specializations:</Text>
          <View style={styles.specializationsList}>
            {item.specializations.map((spec, index) => (
              <View key={index} style={styles.specializationTag}>
                <Text style={styles.specializationText}>{spec}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      <View style={styles.technicianDetails}>
        <Text style={styles.detailText}>
          Rating: {item.rating ? `${item.rating.toFixed(1)} ⭐` : 'No rating yet'}
        </Text>
        <Text style={styles.detailText}>
          Joined: {formatDate(item.createdAt)}
        </Text>
      </View>

      {item.kycStatus === 'PENDING' && (
        <View style={styles.actionButtons}>
          <Button
            title="Approve"
            onPress={() => handleApprove(item.id)}
            variant="primary"
            size="sm"
            style={styles.actionButton}
          />
          <Button
            title="Reject"
            onPress={() => handleReject(item.id)}
            variant="outline"
            size="sm"
            style={styles.actionButton}
          />
        </View>
      )}
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading technicians...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Technician Management"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
      />

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {renderFilterButton('pending', 'Pending')}
        {renderFilterButton('approved', 'Approved')}
        {renderFilterButton('rejected', 'Rejected')}
        {renderFilterButton('all', 'All')}
      </View>

      <FlatList
        data={technicians}
        renderItem={renderTechnicianItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {filter === 'pending' 
                ? 'No pending technicians' 
                : 'No technicians found'}
            </Text>
          </View>
        }
      />
    </View>
  );
}

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.secondary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing.lg,
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
    },
    filterContainer: {
      flexDirection: 'row',
      padding: spacing.lg,
      gap: spacing.sm,
    },
    filterButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      backgroundColor: colors.background.primary,
      borderWidth: 1,
      borderColor: colors.border.light,
    },
    filterButtonActive: {
      backgroundColor: colors.primary.main,
      borderColor: colors.primary.main,
    },
    filterButtonText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      fontWeight: '500' as const,
    },
    filterButtonTextActive: {
      color: colors.background.primary,
    },
    listContainer: {
      padding: spacing.lg,
      paddingTop: 0,
    },
    technicianCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    technicianHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: spacing.md,
    },
    avatarContainer: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    avatarText: {
      fontSize: typography.fontSize.lg,
      fontWeight: '600' as const,
      color: colors.background.primary,
    },
    technicianInfo: {
      flex: 1,
    },
    technicianName: {
      fontSize: typography.fontSize.base,
      fontWeight: '600' as const,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    technicianEmail: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    technicianPhone: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    statusContainer: {
      alignItems: 'flex-end',
    },
    statusBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
      marginBottom: spacing.xs,
    },
    statusText: {
      fontSize: typography.fontSize.xs,
      color: colors.background.primary,
      fontWeight: '600' as const,
    },
    availableText: {
      fontSize: typography.fontSize.xs,
      color: colors.success.main || '#10b981',
      fontWeight: '500' as const,
    },
    specializationsContainer: {
      marginBottom: spacing.md,
    },
    specializationsLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    specializationsList: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.sm,
    },
    specializationTag: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      backgroundColor: colors.primary[100] || colors.background.secondary,
      borderRadius: borderRadius.sm,
    },
    specializationText: {
      fontSize: typography.fontSize.xs,
      color: colors.primary.main,
      fontWeight: '500' as const,
    },
    technicianDetails: {
      marginBottom: spacing.md,
    },
    detailText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.tertiary,
      marginBottom: spacing.xs,
    },
    actionButtons: {
      flexDirection: 'row',
      gap: spacing.md,
      paddingTop: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
    },
    actionButton: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: spacing[8],
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.text.tertiary,
    },
    backIcon: {
      fontSize: 24,
      color: colors.text.secondary,
    },
  }), [colors, spacing]);
