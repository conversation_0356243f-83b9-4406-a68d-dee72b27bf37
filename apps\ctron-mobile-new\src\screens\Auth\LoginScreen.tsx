// CTRON Home - Enhanced Login Screen
// Professional authentication interface with design system integration

import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { useAuth } from '../../context/AuthContext';
import AuthAPI from '../../api/auth.api';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../hooks/useTheme';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { colors, spacing, typography, borderRadius } from '@/theme';

export default function LoginScreen() {
  const { colors } = useTheme();
  if (__DEV__) {
    console.log('🔐 LoginScreen rendering');
  }

  const { login } = useAuth();
  const navigation = useNavigation<any>();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string; general?: string }>({});
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    // Email validation
    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    setErrors({});

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (__DEV__) {
        console.log('🔐 Attempting login with:', email);
      }

      const res = await AuthAPI.login({ email, password });

      if (__DEV__) {
        console.log('✅ Login response received');
      }

      const { token } = res;
      if (!token) throw new Error('No token returned from server');

      await login(token);
      // login() already navigates correctly
    } catch (err: any) {
      if (__DEV__) {
        console.error('❌ Login failed:', err);
      }

      const errorMessage = err.response?.data?.message || err.message || 'Login failed';
      setErrors({ general: errorMessage });

      // Show alert for better user feedback
      Alert.alert(
        'Login Failed',
        errorMessage,
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <View style={styles.headerSection}>
            <Text style={styles.logo}>CTRON</Text>
            <Text style={styles.title}>Welcome Back</Text>
          </View>

          {/* Login Form */}
          <Card style={styles.formCard}>
            <View style={styles.formContainer}>
              {/* Email Input */}
              <View style={styles.inputGroup}>
                <TextInput
                  style={[
                    styles.input,
                    errors.email && styles.inputError,
                  ]}
                  placeholder="Email"
                  placeholderTextColor={colors.text.tertiary}
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (errors.email) {
                      setErrors(prev => ({ ...prev, email: undefined }));
                    }
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="email"
                />
                {errors.email && (
                  <Text style={styles.errorText}>{errors.email}</Text>
                )}
              </View>

              {/* Password Input */}
              <View style={styles.inputGroup}>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      styles.passwordInput,
                      errors.password && styles.inputError,
                    ]}
                    placeholder="Password"
                    placeholderTextColor={colors.text.tertiary}
                    value={password}
                    onChangeText={(text) => {
                      setPassword(text);
                      if (errors.password) {
                        setErrors(prev => ({ ...prev, password: undefined }));
                      }
                    }}
                    secureTextEntry={!showPassword}
                    autoComplete="password"
                  />
                  <Button
                    title={showPassword ? '👁️' : '👁️‍🗨️'}
                    onPress={() => setShowPassword(!showPassword)}
                    variant="ghost"
                    size="sm"
                    style={styles.passwordToggle}
                  />
                </View>
                {errors.password && (
                  <Text style={styles.errorText}>{errors.password}</Text>
                )}
              </View>

              {/* Login Button */}
              <Button
                title="Sign In"
                onPress={handleLogin}
                variant="primary"
                size="lg"
                fullWidth
                loading={loading}
                disabled={loading}
                style={styles.loginButton}
              />

              {/* General Error */}
              {errors.general && (
                <View style={styles.generalErrorContainer}>
                  <Text style={styles.generalErrorText}>{errors.general}</Text>
                </View>
              )}
            </View>
          </Card>

          {/* Footer Section */}
          <View style={styles.footerSection}>
            <Button
              title="Create Account"
              onPress={() => navigation.navigate('Signup')}
              variant="ghost"
              size="md"
              style={styles.signupButton}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },

  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: spacing[6],
    paddingTop: spacing[12],
    paddingBottom: spacing[8],
  },

  headerSection: {
    alignItems: 'center',
    marginBottom: spacing[10],
  },

  logo: {
    fontSize: typography.fontSize['5xl'],
    fontWeight: '800' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.primary[900],
    marginBottom: spacing.lg,
  },

  title: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    textAlign: 'center',
  },

  formCard: {
    marginBottom: spacing[6],
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  formContainer: {
    padding: spacing[6],
  },

  inputGroup: {
    marginBottom: spacing[6],
  },

  input: {
    borderWidth: 2,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
    minHeight: 48,
  },

  inputError: {
    borderColor: colors.secondary[500],
  },

  passwordContainer: {
    position: 'relative',
  },

  passwordInput: {
    paddingRight: spacing[12],
  },

  passwordToggle: {
    position: 'absolute',
    right: spacing.sm,
    top: spacing.xs,
    minWidth: 40,
    height: 40,
  },

  errorText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.secondary[600],
    marginTop: spacing.xs,
  },

  loginButton: {
    marginTop: spacing[6],
  },

  generalErrorContainer: {
    backgroundColor: colors.secondary[50],
    borderWidth: 1,
    borderColor: colors.secondary[200],
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    marginTop: spacing.lg,
  },

  generalErrorText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.secondary[700],
    textAlign: 'center',
  },

  footerSection: {
    alignItems: 'center',
  },

  signupButton: {
    minWidth: 150,
  },
}), [colors, spacing, typography, borderRadius]);
