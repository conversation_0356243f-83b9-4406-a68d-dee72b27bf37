//src/screens/Auth/SignupScreen.tsx
import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAuth } from '../../context/AuthContext';
import AuthAPI from '../../api/auth.api';

import { useTheme } from '../../context/ThemeContext';

type Role = 'HOMEOWNER' | 'TECHNICIAN';

export default function SignupScreen() {
  const { login } = useAuth();
  const { colors, spacing, typography, borderRadius } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
      backgroundColor: colors.background,
    },
    title: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.lg,
    },
    errorBox: {
      backgroundColor: colors.semantic.error,
      padding: spacing.sm,
      borderRadius: borderRadius.sm,
      marginBottom: spacing.md,
      width: '100%',
      alignItems: 'center',
    },
    errorText: {
      color: colors.background.primary,
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
    input: {
      width: '100%',
      height: 50,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      marginBottom: spacing.md,
      backgroundColor: colors.background.secondary,
      color: colors.text.primary,
    },
    roleSection: {
      width: '100%',
      marginBottom: spacing.lg,
    },
    roleLabel: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    roleSwitcher: {
      flexDirection: 'row',
      backgroundColor: colors.background.secondary,
      borderRadius: borderRadius.md,
      overflow: 'hidden',
    },
    roleButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.md,
      borderWidth: 1,
      borderColor: 'transparent',
    },
    roleButtonActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    roleIcon: {
      fontSize: typography.fontSize.lg,
      marginRight: spacing.sm,
    },
    roleText: {
      ...typography.button,
      color: colors.text.primary,
    },
    roleTextActive: {
      color: colors.background.primary,
    },
    button: {
      width: '100%',
      height: 50,
      backgroundColor: colors.primary,
      borderRadius: borderRadius.md,
      justifyContent: 'center',
      alignItems: 'center',
    },
    disabledButton: {
      backgroundColor: colors.border.medium,
    },
    buttonText: {
      color: colors.background.primary,
      ...typography.button,
    },
  }), [colors, spacing, typography, borderRadius]);

  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<Role>('HOMEOWNER');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSignup = async () => {
    setError('');
    if (!fullName || !email || !password || !phone) {
      setError('All fields are required');
      return;
    }

    if (!email.includes('@')) {
      setError('Invalid email address');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    try {
      setLoading(true);

      if (__DEV__) {
        console.log('🔐 [SignupScreen] Attempting signup with:', { fullName, email, phone, role });
      }

      const res = await AuthAPI.signup({ fullName, email, phone, password, role });

      if (__DEV__) {
        console.log('✅ [SignupScreen] Signup response received');
      }

      const { token } = res;
      if (!token) throw new Error('No token returned');

      await login(token);
    } catch (err: any) {
      if (__DEV__) {
        console.error('❌ [SignupScreen] Signup failed:', err);
        console.error('📄 [SignupScreen] Error details:', err.response?.data || err);
      }

      setError(err.response?.data?.message || err.message || 'Signup failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={(Platform.OS === 'ios' ? 'padding' : undefined) as 'padding' | undefined}
      >
        <Text style={styles.title}>Create Account</Text>

        {error && (
          <View style={styles.errorBox}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        <TextInput
          style={styles.input}
          placeholder="Full Name"
          value={fullName}
          onChangeText={setFullName}
        />
        <TextInput
          style={styles.input}
          placeholder="Phone"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
        />
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          autoCorrect={false}
          autoComplete="email"
          textContentType="emailAddress"
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />

        <View style={styles.roleSection}>
          <Text style={styles.roleLabel}>I am a:</Text>
          <View style={styles.roleSwitcher}>
            <TouchableOpacity
              style={[styles.roleButton, role === 'HOMEOWNER' && styles.roleButtonActive]}
              onPress={() => setRole('HOMEOWNER')}
              accessibilityLabel="Select Homeowner role"
              accessibilityRole="button"
            >
              <Text style={styles.roleIcon}>🏠</Text>
              <Text style={[styles.roleText, role === 'HOMEOWNER' && styles.roleTextActive]}>
                Homeowner
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.roleButton, role === 'TECHNICIAN' && styles.roleButtonActive]}
              onPress={() => setRole('TECHNICIAN')}
              accessibilityLabel="Select Technician role"
              accessibilityRole="button"
            >
              <Text style={styles.roleIcon}>🔧</Text>
              <Text style={[styles.roleText, role === 'TECHNICIAN' && styles.roleTextActive]}>
                Technician
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.button, loading && styles.disabledButton]}
          onPress={handleSignup}
          disabled={loading}
        >
          {loading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Sign Up</Text>}
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}
