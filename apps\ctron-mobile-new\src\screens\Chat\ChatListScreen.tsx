// CTRON Home - Chat List Screen
// Display all active chats for the current user

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,

  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeownerStackParamList } from '../../navigation/types';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import { ChatAPI } from '../../api/chat.api';
import { colors, spacing, typography } from '@/theme';

interface Chat {
  id: string;
  jobId: string;
  status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED';
  createdAt: string;
  updatedAt: string;
  job: {
    id: string;
    title: string;
    description: string;
    status: string;
  };
  participants: {
    id: string;
    userId: string;
    user: {
      id: string;
      fullName: string;
      role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
    };
  }[];
  lastMessage?: {
    id: string;
    content: string;
    createdAt: string;
    sender: {
      id: string;
      fullName: string;
    };
  };
  unreadCount?: number;
}

export default function ChatListScreen() {
  const { colors } = useTheme();
  const navigation = useNavigation<NativeStackNavigationProp<HomeownerStackParamList>>();
  const { user } = useAuth();

  const [chats, setChats] = useState<Chat[]>([]);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {

      const response = await ChatAPI.getUserChats();
      setChats(response.chats || []);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to load chats',
        [{ text: 'OK' }]
      );
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadChats();
    setRefreshing(false);
  };

  const handleChatPress = (chat: Chat) => {
    navigation.navigate('Chat', {
      chatId: chat.id,
      jobTitle: chat.job.title
    });
  };

  const formatLastMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getOtherParticipant = (chat: Chat) => {
    return chat.participants.find(p => p.userId !== user?.userId)?.user;
  };

  const renderChatItem = ({ item: chat }: { item: Chat }) => {
    const otherParticipant = getOtherParticipant(chat);
    const hasUnread = (chat.unreadCount || 0) > 0;

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => handleChatPress(chat)}
        accessibilityLabel={`Chat for ${chat.job.title}`}
        accessibilityRole="button"
      >
        <Card style={[styles.chatCard, hasUnread && styles.unreadChatCard] as any}>
          <View style={styles.chatHeader}>
            <View style={styles.chatInfo}>
              <Text style={[styles.jobTitle, hasUnread && styles.unreadText]}>
                {chat.job.title}
              </Text>
              <Text style={styles.participantName}>
                with {otherParticipant?.fullName || 'Unknown User'}
              </Text>
            </View>
            <View style={styles.chatMeta}>
              {chat.lastMessage && (
                <Text style={styles.timestamp}>
                  {formatLastMessageTime(chat.lastMessage.createdAt)}
                </Text>
              )}
              {hasUnread && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>
                    {chat.unreadCount! > 99 ? '99+' : chat.unreadCount}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {chat.lastMessage && (
            <Text style={styles.lastMessage} numberOfLines={2}>
              {chat.lastMessage.sender.fullName}: {chat.lastMessage.content}
            </Text>
          )}

          <View style={styles.chatStatus}>
            <View style={[
              styles.statusIndicator,
              chat.status === 'ACTIVE' && styles.activeStatus,
              chat.status === 'CLOSED' && styles.closedStatus,
              chat.status === 'ARCHIVED' && styles.archivedStatus,
            ]} />
            <Text style={styles.statusText}>
              {chat.status.toLowerCase()}
            </Text>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyTitle}>No Chats Yet</Text>
      <Text style={styles.emptyMessage}>
        Chats will appear here when you start communicating about your jobs.
      </Text>
      <Button
        title="Refresh"
        onPress={handleRefresh}
        variant="ghost"
        size="md"
        style={styles.refreshButton}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <Header title="Messages" />
      <FlatList
        data={chats}
        renderItem={renderChatItem}
        keyExtractor={(item: { id: any; }) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  listContainer: {
    flexGrow: 1,
    padding: spacing.md,
  },
  chatItem: {
    marginBottom: spacing.sm,
  },
  chatCard: {
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
  },
  unreadChatCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  chatInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  jobTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  participantName: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
  },
  chatMeta: {
    alignItems: 'flex-end',
  },
  timestamp: {
    ...typography.caption,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  unreadBadge: {
    backgroundColor: colors.primary,
    borderRadius: 10,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unreadCount: {
    color: colors.background.primary,
    ...typography.caption,
    fontWeight: 'bold',
  },
  unreadText: {
    fontWeight: 'bold',
  },
  lastMessage: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
  },
  chatStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  activeStatus: {
    backgroundColor: colors.success,
  },
  closedStatus: {
    backgroundColor: colors.error,
  },
  archivedStatus: {
    backgroundColor: colors.text.secondary,
  },
  statusText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.md,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  emptyMessage: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  refreshButton: {
    marginTop: spacing.md,
  },
}), [colors, spacing, typography]);
