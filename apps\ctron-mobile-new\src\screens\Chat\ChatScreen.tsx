// CTRON Home - Chat Screen
// Real-time messaging interface with Socket.IO integration

import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { ChatScreenRouteProp } from '../../navigation/types';

import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import { ChatAPI } from '../../api/chat.api';
import { SocketService } from '../../services/socket.service';
import { colors, typography, borderRadius, spacing } from '../../styles/theme';



// ... existing code ...

interface Message {
  id: string;
  content: string;
  senderId: string;
  chatId: string;
  createdAt: string;
  sender: {
    id: string;
    fullName: string;
    role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  };
  readBy: {
    userId: string;
    readAt: string;
  }[];
  attachments: {
    id: string;
    filename: string;
    mimeType: string;
    size: number;
    url: string;
  }[];
}

export default function ChatScreen() {
  const route = useRoute<ChatScreenRouteProp>();
  const { chatId, jobTitle } = route.params;
  const { colors } = useTheme();

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);

  const flatListRef = useRef<FlatList<any>>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    loadMessages();
    setupSocketListeners();
    joinChatRoom();

    return () => {
      leaveChatRoom();
      cleanupSocketListeners();
    };
  }, [chatId]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await ChatAPI.getChatMessages(chatId);
      setMessages(response.messages || []);

      // Mark messages as read
      await ChatAPI.markMessagesRead(chatId);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to load messages',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    SocketService.on('chat:newMessage', handleNewMessage);
    SocketService.on('chat:userTyping', handleUserTyping);
    SocketService.on('chat:messageRead', handleMessageRead);
    SocketService.on('chat:onlineUsers', handleOnlineUsers);
    SocketService.on('chat:userJoined', handleUserJoined);
    SocketService.on('chat:userLeft', handleUserLeft);
  };

  const cleanupSocketListeners = () => {
    SocketService.off('chat:newMessage', handleNewMessage);
    SocketService.off('chat:userTyping', handleUserTyping);
    SocketService.off('chat:messageRead', handleMessageRead);
    SocketService.off('chat:onlineUsers', handleOnlineUsers);
    SocketService.off('chat:userJoined', handleUserJoined);
    SocketService.off('chat:userLeft', handleUserLeft);
  };

  const joinChatRoom = () => {
    SocketService.emit('chat:join', { chatId });
    SocketService.emit('chat:getOnlineUsers', { chatId });
  };

  const leaveChatRoom = () => {
    SocketService.emit('chat:leave', { chatId });
  };

  const handleNewMessage = (data: { message: Message; chatId: string }) => {
    if (data.chatId === chatId) {
      setMessages(prev => [data.message, ...prev]);

      // Mark as read if not from current user
      if (data.message.senderId !== user?.userId) {
        ChatAPI.markMessageRead(data.message.id);
      }
    }
  };

  const handleUserTyping = (data: { userId: string; isTyping: boolean; chatId: string }) => {
    if (data.chatId === chatId && data.userId !== user?.userId) {
      setTypingUsers(prev => {
        if (data.isTyping) {
          return prev.includes(data.userId) ? prev : [...prev, data.userId];
        } else {
          return prev.filter(id => id !== data.userId);
        }
      });
    }
  };

  const handleMessageRead = (data: { messageId: string; readBy: string }) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === data.messageId
          ? {
            ...msg,
            readBy: [...(msg.readBy || []), { userId: data.readBy, readAt: new Date().toISOString() }]
          }
          : msg
      )
    );
  };

  const handleOnlineUsers = (data: { onlineUsers: string[]; chatId: string }) => {
    if (data.chatId === chatId) {
      setOnlineUsers(data.onlineUsers);
    }
  };

  const handleUserJoined = (data: { userId: string; chatId: string }) => {
    if (data.chatId === chatId) {
      setOnlineUsers(prev => prev.includes(data.userId) ? prev : [...prev, data.userId]);
    }
  };

  const handleUserLeft = (data: { userId: string; chatId: string }) => {
    if (data.chatId === chatId) {
      setOnlineUsers(prev => prev.filter(id => id !== data.userId));
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    const messageContent = newMessage.trim();
    setNewMessage('');
    setSending(true);

    try {
      // Send via Socket.IO for real-time delivery
      SocketService.emit('chat:sendMessage', {
        chatId,
        content: messageContent,
        attachments: []
      });
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to send message',
        [{ text: 'OK' }]
      );
      setNewMessage(messageContent); // Restore message on error
    } finally {
      setSending(false);
    }
  };

  const handleTyping = (text: string) => {
    setNewMessage(text);

    // Send typing indicator
    SocketService.emit('chat:typing', { chatId, isTyping: text.length > 0 });

    // Clear typing indicator after 3 seconds of inactivity
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      SocketService.emit('chat:typing', { chatId, isTyping: false });
    }, 3000);
  };

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const isMessageRead = (message: Message) => {
    return message.readBy?.some(read => read.userId !== user?.userId);
  };

  const renderMessage = ({ item: message }: { item: Message }) => {
    const isOwnMessage = message.senderId === user?.userId;
    const isRead = isMessageRead(message);

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer
      ]}>
        <Card style={[
          styles.messageCard,
          isOwnMessage ? styles.ownMessageCard : styles.otherMessageCard
        ] as any}>
          {!isOwnMessage && (
            <Text style={styles.senderName}>{message.sender.fullName}</Text>
          )}
          <Text style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText
          ]}>
            {message.content}
          </Text>
          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime
            ]}>
              {formatMessageTime(message.createdAt)}
            </Text>
            {isOwnMessage && (
              <Text style={styles.readStatus}>
                {isRead ? '✓✓' : '✓'}
              </Text>
            )}
          </View>
        </Card>
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    return (
      <View style={styles.typingContainer}>
        <Text style={styles.typingText}>
          {typingUsers.length === 1 ? 'Someone is typing...' : 'Multiple people are typing...'}
        </Text>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Header
        title={jobTitle}
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
        rightAction={{
          icon: <Text style={styles.onlineIcon}>👥 {onlineUsers.length}</Text>,
          onPress: () => {
            Alert.alert(
              'Online Users',
              `${onlineUsers.length} user(s) online`,
              [{ text: 'OK' }]
            );
          },
          accessibilityLabel: 'View online users',
        }}
      />

      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item: { id: any; }) => item.id}
        contentContainerStyle={styles.messagesContainer}
        inverted
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => flatListRef.current?.scrollToOffset({ offset: 0, animated: true })}
      />

      {renderTypingIndicator()}

      <View style={styles.inputContainer}>
        <Card style={styles.inputCard}>
          <View style={styles.inputRow}>
            <TextInput
              style={styles.textInput}
              placeholder="Type a message..."
              placeholderTextColor={colors.text.tertiary}
              value={newMessage}
              onChangeText={handleTyping}
              multiline
              maxLength={2000}
              editable={!sending}
            />
            <Button
              title="Send"
              onPress={sendMessage}
              variant="primary"
              size="sm"
              disabled={!newMessage.trim() || sending}
              loading={sending}
              style={styles.sendButton}
            />
          </View>
        </Card>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },

  messagesContainer: {
    flexGrow: 1,
    padding: spacing.lg,
  },

  messageContainer: {
    marginBottom: spacing.md,
  },

  ownMessageContainer: {
    alignItems: 'flex-end',
  },

  otherMessageContainer: {
    alignItems: 'flex-start',
  },

  messageCard: {
    maxWidth: '80%',
    padding: spacing.md,
  },

  ownMessageCard: {
    backgroundColor: colors.primary[500],
  },

  otherMessageCard: {
    backgroundColor: colors.background.primary,
    borderWidth: 1,
    borderColor: colors.border.light,
  },

  senderName: {
    fontSize: typography.fontSize.xs,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },

  messageText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    lineHeight: 20,
    marginBottom: spacing.xs,
  },

  ownMessageText: {
    color: colors.background.primary,
  },

  otherMessageText: {
    color: colors.text.primary,
  },

  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  messageTime: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
  },

  ownMessageTime: {
    color: colors.primary[100],
  },

  otherMessageTime: {
    color: colors.text.tertiary,
  },

  readStatus: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
    color: colors.primary[100],
    marginLeft: spacing.sm,
  },

  typingContainer: {
    padding: spacing.md,
    paddingHorizontal: spacing.lg,
  },

  typingText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.tertiary,
    fontStyle: 'italic',
  },

  inputContainer: {
    padding: spacing.lg,
    paddingTop: spacing.sm,
  },

  inputCard: {
    padding: spacing.md,
  },

  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing.md,
  },

  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    backgroundColor: colors.background.primary,
    maxHeight: 100,
    minHeight: 44,
  },

  sendButton: {
    minWidth: 80,
  },

  backIcon: {
    fontSize: 24,
    color: colors.text.secondary,
  },

  onlineIcon: {
    fontSize: 14,
    color: colors.text.secondary,
  },
}), [colors, typography, borderRadius, spacing]);
