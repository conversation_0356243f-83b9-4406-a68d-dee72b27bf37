// src/screens/Homeowner/BookJobScreen.tsx
import React, { useState, useMemo } from 'react';
import {
  Alert,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ScrollView,

  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { JobAPI, JobCreatePayload } from '../../api/job.api';
import { TechnicianAPI } from '../../api/technician.api';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../../components/ui/Button';
import { CustomDatePicker } from '../../components/ui/CustomDatePicker';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { colors, spacing, typography, borderRadius } from '@/theme';
// ... existing code ...

// Mock types for compilation
interface Technician {
  id: string;
  fullName: string;
  specialization: string;
  rating?: number;
  distance?: number;
  completedJobs?: number;
  hourlyRate?: number;
  isAvailable: boolean;
}

export default function BookJobScreen() {
  const navigation = useNavigation<any>();
  const { colors, typography, borderRadius, spacing } = useTheme();

  const [technicianId,] = useState('');
  const [issue, setIssue] = useState('');
  const [scheduledAt, setScheduledAt] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);

  // Additional state variables for multi-step form
  const [step, setStep] = useState<'details' | 'technicians' | 'confirmation'>('details');
  const [useLocation, setUseLocation] = useState(false);
  const [location,] = useState<any>(null);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null);
  const [loadingTechnicians, setLoadingTechnicians] = useState(false);
  const [minRating, setMinRating] = useState(false);
  const [selectedSpecialization, setSelectedSpecialization] = useState<string>('');

  // Mock data
  const specializations = [
    { label: 'Plumbing', value: 'plumbing' },
    { label: 'Electrical', value: 'electrical' },
    { label: 'HVAC', value: 'hvac' },
    { label: 'General', value: 'general' },
  ];

  const handleDateChange = (date: Date) => {
    setScheduledAt(date);
  };

  const handleNextStep = () => {
    if (step === 'details') {
      setStep('technicians');
      fetchTechnicians();
    } else if (step === 'technicians') {
      setStep('confirmation');
    }
  };

  const handlePreviousStep = () => {
    if (step === 'technicians') {
      setStep('details');
    } else if (step === 'confirmation') {
      setStep('technicians');
    }
  };

  const fetchTechnicians = async () => {
    setLoadingTechnicians(true);
    try {
      // Fetch real technicians from API
      const response = await TechnicianAPI.findTechnicians({
        specialization: 'Plumbing', // Could be dynamic based on job type
      });
      setTechnicians(response.technicians);
    } catch (error) {
      console.error('Failed to fetch technicians:', error);
      Alert.alert('Error', 'Failed to load available technicians');
      setTechnicians([]);
    } finally {
      setLoadingTechnicians(false);
    }
  };

  const handleBook = async () => {
    if (!issue) {
      Alert.alert('Validation', 'Please describe the issue.');
      return;
    }

    if (!scheduledAt) {
      Alert.alert('Validation', 'Please pick a schedule date.');
      return;
    }

    const payload: JobCreatePayload = {
      issue,
      scheduledAt: scheduledAt.toISOString(),
      ...(technicianId ? { technicianId } : {}),
    };

    try {
      setLoading(true);
      await JobAPI.createJob(payload);
      Alert.alert('Success', 'Job booked!');
      navigation.navigate('Home');
    } catch (e: any) {
      console.error(e);
      Alert.alert('Error', e?.response?.data?.message || 'Booking failed');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      <View style={[styles.step, step === 'details' && styles.activeStep]}>
        <Text style={[styles.stepText, step === 'details' && styles.activeStepText]}>1</Text>
      </View>
      <View style={styles.stepLine} />
      <View style={[styles.step, step === 'technicians' && styles.activeStep]}>
        <Text style={[styles.stepText, step === 'technicians' && styles.activeStepText]}>2</Text>
      </View>
      <View style={styles.stepLine} />
      <View style={[styles.step, step === 'confirmation' && styles.activeStep]}>
        <Text style={[styles.stepText, step === 'confirmation' && styles.activeStepText]}>3</Text>
      </View>
    </View>
  );

  const renderDetailsStep = () => (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>Describe Your Issue</Text>
      <Text style={styles.stepDescription}>
        Tell us what needs to be fixed or maintained
      </Text>

      <Text style={styles.label}>Issue Description *</Text>
      <TextInput
        style={[styles.input, styles.textArea]}
        multiline
        value={issue}
        onChangeText={setIssue}
        placeholder="Describe the problem in detail..."
        maxLength={500}
      />
      <Text style={styles.characterCount}>{issue.length}/500</Text>

      <CustomDatePicker
        label="Preferred Schedule *"
        value={scheduledAt}
        onChange={handleDateChange}
        minimumDate={new Date()}
        mode="datetime"
      />

      <View style={styles.locationSection}>
        <View style={styles.locationHeader}>
          <Text style={styles.label}>Use My Location</Text>
          <Switch
            value={useLocation}
            onValueChange={setUseLocation}
            trackColor={{ false: colors.border.medium, true: colors.primary.main[300] }}
            thumbColor={useLocation ? colors.primary.main[900] : colors.text.tertiary}
          />
        </View>
        <Text style={styles.locationDescription}>
          {useLocation
            ? 'We\'ll find technicians near you'
            : 'Browse all available technicians'
          }
        </Text>
      </View>

      <Button
        title="Find Technicians"
        onPress={handleNextStep}
        variant="primary"
        size="lg"
        fullWidth
        disabled={!issue.trim()}
        style={styles.nextButton}
      />
    </ScrollView>
  );

  const renderTechnicianCard = ({ item: technician }: { item: Technician }) => (
    <TouchableOpacity
      style={[
        styles.technicianCard,
        selectedTechnician?.id === technician.id && styles.selectedTechnicianCard
      ]}
      onPress={() => setSelectedTechnician(technician)}
    >
      <Card style={styles.technicianCardContent}>
        <View style={styles.technicianHeader}>
          <View style={styles.technicianInfo}>
            <Text style={styles.technicianName}>{technician.fullName}</Text>
            <Text style={styles.technicianSpecialization}>{technician.specialization}</Text>
          </View>
          <View style={styles.technicianMeta}>
            <Text style={styles.technicianRating}>⭐ {technician.rating?.toFixed(1) || 'N/A'}</Text>
            {useLocation && location && (
              <Text style={styles.technicianDistance}>{technician.distance?.toFixed(1)} km</Text>
            )}
          </View>
        </View>

        <View style={styles.technicianDetails}>
          <Text style={styles.technicianJobs}>
            {technician.completedJobs || 0} jobs completed
          </Text>
          {technician.hourlyRate && (
            <Text style={styles.technicianRate}>
              ${technician.hourlyRate}/hour
            </Text>
          )}
        </View>

        <View style={styles.availabilityBadge}>
          <Text style={styles.availabilityText}>
            {technician.isAvailable ? '✅ Available' : '⏰ Busy'}
          </Text>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderTechniciansStep = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Choose Your Technician</Text>
      <Text style={styles.stepDescription}>
        Select from {technicians.length} available technicians
      </Text>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <View style={styles.filterRow}>
          <Text style={styles.filterLabel}>High-rated only (4.5+)</Text>
          <Switch
            value={minRating}
            onValueChange={setMinRating}
            trackColor={{ false: colors.border.medium, true: colors.primary.main[300] }}
            thumbColor={minRating ? colors.primary.main[900] : colors.text.tertiary}
          />
        </View>

        <View style={styles.filterRow}>
          <Text style={styles.filterLabel}>Service Type</Text>
          <View style={styles.specializationButtons}>
            {specializations.map((spec) => (
              <TouchableOpacity
                key={spec.value}
                style={[
                  styles.specializationButton,
                  selectedSpecialization === spec.value && styles.selectedSpecializationButton
                ]}
                onPress={() => setSelectedSpecialization(spec.value)}
              >
                <Text style={[
                  styles.specializationButtonText,
                  selectedSpecialization === spec.value && styles.selectedSpecializationButtonText
                ]}>
                  {spec.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {loadingTechnicians ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary.main[900]} />
          <Text style={styles.loadingText}>Finding technicians...</Text>
        </View>
      ) : (
        <FlatList
          data={technicians}
          renderItem={renderTechnicianCard}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.techniciansList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyTitle}>No technicians found</Text>
              <Text style={styles.emptyText}>
                Try adjusting your filters or location settings
              </Text>
              <Button
                title="Refresh"
                onPress={fetchTechnicians}
                variant="outline"
                style={styles.refreshButton}
              />
            </View>
          }
        />
      )}

      <View style={styles.stepButtons}>
        <Button
          title="Back"
          onPress={handlePreviousStep}
          variant="outline"
          style={styles.backButton}
        />
        <Button
          title="Continue"
          onPress={handleNextStep}
          variant="primary"
          disabled={!selectedTechnician}
          style={styles.continueButton}
        />
      </View>
    </View>
  );

  const renderConfirmationStep = () => (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>Confirm Booking</Text>
      <Text style={styles.stepDescription}>
        Review your service request details
      </Text>

      <Card style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Service Details</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Issue:</Text>
          <Text style={styles.summaryValue}>{issue}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Scheduled:</Text>
          <Text style={styles.summaryValue}>
            {scheduledAt.toLocaleDateString()} at {scheduledAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      </Card>

      {selectedTechnician && (
        <Card style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Selected Technician</Text>
          <View style={styles.technicianSummary}>
            <View>
              <Text style={styles.technicianSummaryName}>{selectedTechnician.fullName}</Text>
              <Text style={styles.technicianSummarySpec}>{selectedTechnician.specialization}</Text>
              <Text style={styles.technicianSummaryRating}>
                ⭐ {selectedTechnician.rating?.toFixed(1)} • {selectedTechnician.completedJobs || 0} jobs
              </Text>
            </View>
            {selectedTechnician.hourlyRate && (
              <Text style={styles.technicianSummaryRate}>
                ${selectedTechnician.hourlyRate}/hour
              </Text>
            )}
          </View>
        </Card>
      )}

      <View style={styles.stepButtons}>
        <Button
          title="Back"
          onPress={handlePreviousStep}
          variant="outline"
          style={styles.backButton}
        />
        <Button
          title={loading ? 'Booking...' : 'Confirm & Book'}
          onPress={handleBook}
          variant="primary"
          loading={loading}
          disabled={loading}
          style={styles.continueButton}
        />
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Book a Service"
      />

      {renderStepIndicator()}

      {step === 'details' && renderDetailsStep()}
      {step === 'technicians' && renderTechniciansStep()}
      {step === 'confirmation' && renderConfirmationStep()}
    </View>
  );
}

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  content: {
    flex: 1,
    padding: spacing.lg,
  },
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.xxl,
  },
  step: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.border.medium,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStep: {
    backgroundColor: colors.primary[500],
  },
  stepText: {
    fontSize: typography.fontSize.sm,
    fontWeight: 'bold' as const,
    color: colors.text.secondary,
  },
  activeStepText: {
    color: colors.background.primary,
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: colors.border.medium,
    marginHorizontal: spacing.sm,
  },
  stepContent: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  stepTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: 'bold' as const,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  stepDescription: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    marginBottom: spacing.xl,
  },
  label: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    marginTop: spacing.md,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    backgroundColor: colors.background.secondary,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: typography.fontSize.xs,
    color: colors.text.tertiary,
    textAlign: 'right',
    marginTop: spacing.xs,
  },
  locationSection: {
    marginTop: spacing.lg,
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  locationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  locationDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  nextButton: {
    marginTop: spacing.xl,
  },
  technicianCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  technicianInfo: {
    flex: 1,
  },
  technicianName: {
    fontSize: typography.fontSize.lg,
    fontWeight: 'bold' as const,
    color: colors.text.primary,
  },
  technicianDetails: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  technicianRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  starIcon: {
    color: colors.semantic.warning,
    marginRight: spacing.xs,
  },
  ratingText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  selectButton: {
    marginLeft: spacing.md,
  },
  selectedTechnicianCard: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.primary[500],
  },
  selectedTechnicianText: {
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
  },
  noTechnicians: {
    fontSize: typography.fontSize.base,
    color: colors.text.tertiary,
    textAlign: 'center',
    marginTop: spacing.lg,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.md,
  },
  filterButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.full,
    borderWidth: 1,
    borderColor: colors.border.medium,
  },
  activeFilterButton: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
  },
  filterButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  activeFilterButtonText: {
    color: colors.background.primary,
  },
  specializationPicker: {
    height: 50,
    width: '100%',
    color: colors.text.primary,
    backgroundColor: colors.background.secondary,
    borderColor: colors.border.medium,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  confirmationDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  confirmationLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    color: colors.text.primary,
  },
  confirmationValue: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    textAlign: 'right',
    flexShrink: 1,
  },
  confirmationSection: {
    marginTop: spacing.lg,
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  confirmationSectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: 'bold' as const,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xl,
  },
  halfButton: {
    width: '48%',
  },
  backButton: {
    flex: 1,
    marginRight: spacing.sm,
  },
  continueButton: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    fontWeight: typography.fontWeight.medium,
  },
  summaryValue: {
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    fontWeight: typography.fontWeight.bold,
  },
  summaryCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
  },
  summaryTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  technicianSummary: {
    padding: spacing.sm,
  },
  technicianSummaryName: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  technicianSummarySpec: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  technicianSummaryRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  technicianSummaryRate: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
  },
  stepButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  technicianCardContent: {
    padding: spacing.md,
  },
  technicianHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  technicianSpecialization: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  technicianMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  technicianDistance: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginLeft: spacing.sm,
  },
  technicianJobs: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  technicianRate: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    fontWeight: typography.fontWeight.medium,
  },
  availabilityBadge: {
    backgroundColor: colors.semantic.success,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  availabilityText: {
    fontSize: typography.fontSize.xs,
    color: colors.background.primary,
    fontWeight: typography.fontWeight.medium,
  },
  filtersContainer: {
    marginBottom: spacing.md,
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  filterLabel: {
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    fontWeight: typography.fontWeight.medium,
  },
  specializationButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.sm,
  },
  specializationButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border.medium,
    backgroundColor: colors.background.secondary,
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  selectedSpecializationButton: {
    backgroundColor: colors.primary.main,
    borderColor: colors.primary.main,
  },
  specializationButtonText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.primary,
  },
  selectedSpecializationButtonText: {
    color: colors.background.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    marginTop: spacing.md,
  },
  techniciansList: {
    paddingBottom: spacing.xl,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.lg,
  },
  refreshButton: {
    minWidth: 120,
  },
}), [colors, spacing, typography, borderRadius]);
