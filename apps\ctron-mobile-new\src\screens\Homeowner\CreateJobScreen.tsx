//mobile/src/screens/Homeowner/CreateJobScreen.tsx
import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, TextInput, Image, ScrollView, Alert, ActivityIndicator, StyleSheet, TouchableOpacity } from 'react-native';
import { Button } from '../../components/ui/Button';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import RNPickerSelect from 'react-native-picker-select';
import axios from 'axios';
import { useNavigation } from '@react-navigation/native';

import { useTheme } from '../../context/ThemeContext';


interface Technician {
  id: string;
  fullName: string;
  rating: number;
  distance: number;
}

interface Coordinates {
  latitude: number;
  longitude: number;
}

interface SelectedImage {
  uri: string;
  type: string;
}

export default function CreateJobScreen() {
  const { colors, typography, spacing } = useTheme();
  const navigation = useNavigation();

  const [useLocation, setUseLocation] = useState(true);
  const [location, setLocation] = useState<Coordinates | null>(null);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [selectedTechnician, setSelectedTechnician] = useState<string | null>(null);
  const [minRating, setMinRating] = useState(false);
  const [issue, setIssue] = useState('');
  const [scheduledAt, setScheduledAt] = useState('');
  const [image, setImage] = useState<SelectedImage | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (useLocation) {
      (async () => {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission denied', 'Location access is required to find nearby technicians.');
          return;
        }
        const loc = await Location.getCurrentPositionAsync({});
        setLocation({ latitude: loc.coords.latitude, longitude: loc.coords.longitude });
      })();
    }
  }, [useLocation]);

  useEffect(() => {
    if (location) fetchTechnicians();
  }, [location, minRating]);

  const fetchTechnicians = async () => {
    try {
      const { data } = await axios.get('/api/technicians/nearby', {
        params: {
          lat: location?.latitude,
          lng: location?.longitude,
          minRating: minRating ? 4.5 : 0,
        },
      });
      setTechnicians(data.technicians);
    } catch (err) {
      Alert.alert('Error', 'Failed to fetch technicians');
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.7,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      const asset = result.assets[0];
      if (asset) {
        setImage({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
        });
      }
    }
  };

  const handleSubmit = async () => {
    if (!issue || !scheduledAt || !selectedTechnician) {
      Alert.alert('Missing fields', 'Please fill out all required fields.');
      return;
    }

    try {
      setLoading(true);
      let photoUrl = null;

      if (image) {
        const presign = await axios.post('/api/uploads/presign', {
          fileType: image.type,
        });

        await fetch(presign.data.url, {
          method: 'PUT',
          body: await fetch(image.uri).then(r => r.blob()),
          headers: { 'Content-Type': image.type },
        });

        photoUrl = presign.data.key;
      }

      await axios.post('/api/jobs', {
        technicianId: selectedTechnician,
        issue,
        scheduledAt,
        photoUrl,
        latitude: useLocation && location ? location.latitude : null,
        longitude: useLocation && location ? location.longitude : null,
      });

      Alert.alert('Success', 'Job created successfully');
      navigation.goBack();
    } catch (err) {
      Alert.alert('Error', 'Failed to create job');
    } finally {
      setLoading(false);
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
      padding: spacing.md,
    },
    label: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
      marginTop: spacing.md,
    },
    input: {
      borderWidth: 1,
      borderColor: colors.border.medium,
      borderRadius: 8,
      padding: spacing.sm,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      backgroundColor: colors.background.secondary,
      marginBottom: spacing.md,
    },
    switchContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
      padding: spacing.sm,
      backgroundColor: colors.background.secondary,
      borderRadius: 8,
    },
    pickerContainer: {
      borderWidth: 1,
      marginBottom: spacing.md,
      padding: spacing.sm,
      backgroundColor: colors.background.secondary,
      borderRadius: 8,
    },
    imagePreview: {
      width: 100,
      height: 100,
      resizeMode: 'cover',
      marginTop: spacing.md,
      borderRadius: 8,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: spacing.lg,
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
    },
  }), [colors, spacing, typography]);

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.switchContainer}>
        <Text style={styles.label}>Use My Location</Text>
        <TouchableOpacity
          onPress={() => setUseLocation(!useLocation)}
          style={{
            width: 50,
            height: 30,
            borderRadius: 15,
            backgroundColor: useLocation ? colors.primary.main : colors.border.medium,
            justifyContent: 'center',
            alignItems: useLocation ? 'flex-end' : 'flex-start',
            paddingHorizontal: 2,
          }}
        >
          <View style={{
            width: 26,
            height: 26,
            borderRadius: 13,
            backgroundColor: colors.background.primary,
          }} />
        </TouchableOpacity>
      </View>

      <View style={styles.switchContainer}>
        <Text style={styles.label}>Minimum 4.5★ Technicians Only</Text>
        <TouchableOpacity
          onPress={() => setMinRating(!minRating)}
          style={{
            width: 50,
            height: 30,
            borderRadius: 15,
            backgroundColor: minRating ? colors.primary.main : colors.border.medium,
            justifyContent: 'center',
            alignItems: minRating ? 'flex-end' : 'flex-start',
            paddingHorizontal: 2,
          }}
        >
          <View style={{
            width: 26,
            height: 26,
            borderRadius: 13,
            backgroundColor: colors.background.primary,
          }} />
        </TouchableOpacity>
      </View>

      <Text style={styles.label}>Select Technician</Text>
      <RNPickerSelect
        onValueChange={value => setSelectedTechnician(value)}
        items={technicians.map(t => ({
          label: `${t.fullName} (${t.rating || 'N/A'}★, ${t.distance.toFixed(1)} km)`,
          value: t.id,
        }))}
        placeholder={{ label: 'Select a technician...', value: null }}
      />

      <Text style={styles.label}>Issue Description</Text>
      <TextInput
        style={styles.input}
        placeholder="Describe the problem..."
        multiline
        value={issue}
        onChangeText={setIssue}
      />

      <Text style={styles.label}>Scheduled Date & Time</Text>
      <TextInput
        style={styles.input}
        placeholder="YYYY-MM-DDTHH:MM"
        value={scheduledAt}
        onChangeText={setScheduledAt}
      />

      <Button title="Upload Photo (optional)" onPress={pickImage} />
      {image && <Image source={{ uri: image.uri }} style={styles.imagePreview} />}

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary.main} />
          <Text style={styles.label}>Submitting...</Text>
        </View>
      ) : (
        <Button title="Create Job" onPress={handleSubmit} />
      )}
    </ScrollView>
  );
}

