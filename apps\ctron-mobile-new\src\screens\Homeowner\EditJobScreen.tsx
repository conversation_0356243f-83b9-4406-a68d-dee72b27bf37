import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';

import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Header } from '../../components/ui/Header';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { DateTimePicker } from '../../components/ui/DateTimePicker';
import { JobAPI } from '../../api/job.api';
import { lightTheme as theme } from '../../styles/theme';

import { useTheme } from '../../context/ThemeContext';
// ... existing code ...
const { colors, spacing, typography, borderRadius } = useTheme();

type EditJobScreenRouteProp = RouteProp<{
  EditJob: { jobId: string };
}, 'EditJob'>;

type EditJobScreenNavigationProp = any;

interface Job {
  id: string;
  issue: string;
  scheduledAt: string;
  status: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

const EditJobScreen: React.FC = () => {
  const route = useRoute<EditJobScreenRouteProp>();
  const navigation = useNavigation<EditJobScreenNavigationProp>();
  const { jobId } = route.params;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [job, setJob] = useState<Job | null>(null);
  const [formData, setFormData] = useState({
    issue: '',
    description: '',
    scheduledAt: new Date(),
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
  });

  useEffect(() => {
    loadJobDetails();
  }, [jobId]);

  const loadJobDetails = async () => {
    try {
      setLoading(true);
      const jobData = await JobAPI.getJobById(jobId);

      setJob(jobData);
      setFormData({
        issue: jobData.issue,
        description: jobData.description || '',
        scheduledAt: new Date(jobData.scheduledAt),
        priority: jobData.priority || 'medium',
      });
    } catch (error: any) {
      console.error('Error loading job details:', error);
      Alert.alert(
        'Error',
        error.message || 'Failed to load job details. Please try again.'
      );
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Validate form
      if (!formData.issue.trim()) {
        Alert.alert('Validation Error', 'Please describe the issue');
        return;
      }

      const updatedJob = await JobAPI.updateJob(jobId, {
        issue: formData.issue,
        description: formData.description,
        scheduledAt: formData.scheduledAt.toISOString(),
        priority: formData.priority,
      });

      Alert.alert(
        'Success',
        'Job updated successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error updating job:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to update job. Please try again.'
      );
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Discard Changes',
      'Are you sure you want to discard your changes?',
      [
        { text: 'Keep Editing', style: 'cancel' },
        {
          text: 'Discard',
          style: 'destructive',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Header title="Edit Job" />
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={colors.primary[900]} />
          <Text style={styles.loadingText}>Loading job details...</Text>
        </View>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.container}>
        <Header title="Edit Job" />
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Job Not Found</Text>
          <Text style={styles.errorText}>
            The requested job could not be found or you don't have permission to edit it.
          </Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
            style={styles.errorButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Edit Job"
        rightAction={{
          icon: <Text style={styles.saveIcon}>✓</Text>,
          onPress: handleSave,
          accessibilityLabel: 'Save changes',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Card style={styles.formCard}>
          <Text style={styles.cardTitle}>Job Details</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Issue Description *</Text>
            <Input
              value={formData.issue}
              onChangeText={(text) => setFormData(prev => ({ ...prev, issue: text }))}
              placeholder="Describe the issue that needs to be fixed"
              multiline
              numberOfLines={3}
              style={styles.textArea}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Additional Details</Text>
            <Input
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Provide any additional details or special instructions"
              multiline
              numberOfLines={4}
              style={styles.textArea}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Scheduled Date & Time *</Text>
            <DateTimePicker
              value={formData.scheduledAt}
              onChange={(date: Date) => setFormData(prev => ({ ...prev, scheduledAt: date }))}
              mode="datetime"
              minimumDate={new Date()}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Priority Level</Text>
            <View style={styles.priorityContainer}>
              {(['low', 'medium', 'high', 'urgent'] as const).map((priority) => (
                <Button
                  key={priority}
                  title={priority.charAt(0).toUpperCase() + priority.slice(1)}
                  onPress={() => setFormData(prev => ({ ...prev, priority }))}
                  variant={formData.priority === priority ? 'primary' : 'secondary'}
                  size="sm"
                  style={styles.priorityButton}
                />
              ))}
            </View>
          </View>
        </Card>

        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={handleCancel}
            variant="secondary"
            style={styles.cancelButton}
          />
          <Button
            title={saving ? 'Saving...' : 'Save Changes'}
            onPress={handleSave}
            variant="primary"
            loading={saving}
            disabled={saving}
            style={styles.saveButton}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  errorButton: {
    width: '100%',
    maxWidth: 200,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.md,
  },
  saveIcon: {
    fontSize: typography.fontSize.lg,
    color: colors.primary[500],
    fontWeight: typography.fontWeight.bold,
  },
  formCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.md,
    // Shadow styles for web compatibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  formGroup: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  priorityPicker: {
    borderWidth: 1,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text.primary,
    backgroundColor: colors.background.secondary,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.lg,
  },
  buttonSpacer: {
    width: spacing.md,
  },
  priorityContainer: {
    marginBottom: spacing.md,
  },
  priorityButton: {
    marginHorizontal: spacing.xs,
    minWidth: 80,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  cancelButton: {
    flex: 1,
    marginRight: spacing.sm,
    backgroundColor: colors.border.medium,
  },
  saveButton: {
    flex: 1,
    marginLeft: spacing.sm,
    backgroundColor: colors.primary.main,
  },
}), [colors, spacing, typography, borderRadius]);


export default EditJobScreen;
