// CTRON Home - Enhanced Job Details Screen
// Job details with chat integration and status management

import React, { useCallback, useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  ScrollView,
  TouchableOpacity
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { JobAPI } from '../../api/job.api';
import { ChatAPI } from '../../api/chat.api';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';

interface Job {
  id: string;
  issue: string;
  status: string;
  scheduledAt: string;
  createdAt: string;
  serviceType: string;
  address: string;
  description?: string;
  technicianId?: string;
  technician?: {
    id: string;
    user: {
      fullName: string;
      phone?: string;
    };
    specialization: string;
    rating?: number;
  };
}

interface RouteParams {
  jobId: string;
}

const JobDetailsScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAuth();
  const { colors, spacing, typography, borderRadius } = useTheme();

  const { jobId } = route.params as RouteParams;

  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chatLoading, setChatLoading] = useState(false);

  const fetchJobDetails = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const jobData = await JobAPI.getJobById(jobId);
      setJob(jobData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load job details');
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    fetchJobDetails();
  }, [fetchJobDetails]);

  const handleStartChat = useCallback(async () => {
    if (!job?.technicianId) return;

    try {
      setChatLoading(true);
      const chat = await ChatAPI.createOrGetChat(job.id, job.technicianId);
      (navigation as any).navigate('Chat', { chatId: chat.id });
    } catch (err) {
      Alert.alert('Error', 'Failed to start chat with technician');
    } finally {
      setChatLoading(false);
    }
  }, [job, navigation]);

  const handleCancelJob = useCallback(async () => {
    if (!job) return;

    try {
      await JobAPI.cancelJob(job.id);
      Alert.alert('Success', 'Job has been cancelled');
      navigation.goBack();
    } catch (err) {
      Alert.alert('Error', 'Failed to cancel job');
    }
  }, [job, navigation]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.semantic.warning;
      case 'ACCEPTED': return colors.semantic.info;
      case 'IN_PROGRESS': return colors.semantic.info;
      case 'COMPLETED': return colors.semantic.success;
      case 'CANCELLED': return colors.semantic.error;
      default: return colors.neutral[400];
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    loaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
    },
    errorTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    errorText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing.md,
    },
    errorButton: {
      marginTop: spacing.md,
    },
    content: {
      padding: spacing.md,
    },
    section: {
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    detailLabel: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
    },
    detailValue: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    statusContainer: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      borderRadius: borderRadius.sm,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    actionContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
    },
    actionButton: {
      flex: 1,
      marginHorizontal: spacing.xs,
    },
    cancelButton: {
      backgroundColor: colors.semantic.error,
    },
  }), [colors, typography, spacing, borderRadius]);

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={colors.primary.main} />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Error Loading Job</Text>
          <Text style={styles.errorText}>{error}</Text>
          <Button
            title="Try Again"
            onPress={fetchJobDetails}
            style={styles.errorButton}
          />
        </View>
      ) : !job ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Job Not Found</Text>
          <Text style={styles.errorText}>The requested job could not be found.</Text>
        </View>
      ) : (
        <ScrollView style={styles.content}>
          <Card>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Job Details</Text>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Service:</Text>
                <Text style={styles.detailValue}>{job.serviceType}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Status:</Text>
                <View style={[styles.statusContainer, { backgroundColor: getStatusColor(job.status) }]}>
                  <Text style={styles.statusText}>{job.status}</Text>
                </View>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Scheduled:</Text>
                <Text style={styles.detailValue}>
                  {job.scheduledAt ? new Date(job.scheduledAt).toLocaleDateString() : 'Not scheduled'}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Address:</Text>
                <Text style={styles.detailValue}>{job.address}</Text>
              </View>
              {job.description && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Description:</Text>
                  <Text style={styles.detailValue}>{job.description}</Text>
                </View>
              )}
            </View>

            <View style={styles.actionContainer}>
              <Button
                title="Chat with Technician"
                onPress={handleStartChat}
                style={styles.actionButton}
                disabled={job.status === 'COMPLETED' || job.status === 'CANCELLED'}
              />
              <Button
                title="Cancel Job"
                onPress={() => {
                  Alert.alert(
                    'Cancel Job',
                    'Are you sure you want to cancel this job? This action cannot be undone.',
                    [
                      { text: 'No', style: 'cancel' },
                      {
                        text: 'Yes, Cancel',
                        style: 'destructive',
                        onPress: handleCancelJob
                      },
                    ]
                  );
                }}
                variant="secondary"
                style={[styles.actionButton, styles.cancelButton] as any}
                disabled={job.status === 'COMPLETED' || job.status === 'CANCELLED'}
              />
            </View>
          </Card>
        </ScrollView>
      )}
    </View>
  );
};

export default JobDetailsScreen;
