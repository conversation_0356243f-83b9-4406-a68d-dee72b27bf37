import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../hooks/useTheme';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';

interface Job {
  id: string;
  issue: string;
  status: string;
  scheduledAt: string;
  createdAt: string;
  technician?: {
    user: {
      fullName: string;
    };
    specialization: string;
    rating?: number;
  };
  technicianId?: string;
}

export default function JobDetailsScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { colors, spacing, typography, borderRadius } = useTheme();
  const [loading, setLoading] = useState(true);
  const [job, setJob] = useState<Job | null>(null);
  const [chatLoading, setChatLoading] = useState(false);

  const { jobId } = route.params as { jobId: string };

  useEffect(() => {
    const fetchJobDetails = async () => {
      try {
        setLoading(true);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock job data
        const mockJob: Job = {
          id: jobId,
          issue: 'Plumbing repair needed',
          status: 'PENDING',
          scheduledAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          technician: {
            user: {
              fullName: 'John Smith'
            },
            specialization: 'Plumbing',
            rating: 4.8
          }
        };
        
        setJob(mockJob);
      } catch (error) {
        console.error('Failed to fetch job details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetails();
  }, [jobId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.warning.main;
      case 'IN_PROGRESS': return colors.primary.main;
      case 'COMPLETED': return colors.success.main;
      case 'CANCELLED': return colors.error.main;
      default: return colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return 'Pending';
      case 'IN_PROGRESS': return 'In Progress';
      case 'COMPLETED': return 'Completed';
      case 'CANCELLED': return 'Cancelled';
      default: return status;
    }
  };

  const handleStartChat = async () => {
    setChatLoading(true);
    try {
      // Simulate chat initialization
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigation.navigate('Chat', { jobId: job?.id });
    } catch (error) {
      Alert.alert('Error', 'Failed to start chat');
    } finally {
      setChatLoading(false);
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    loaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing[4],
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
    },
    scrollContent: {
      padding: spacing[4],
    },
    statusCard: {
      marginBottom: spacing[4],
    },
    statusHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statusLabel: {
      fontSize: typography.fontSize.lg,
      fontWeight: '600',
      color: colors.text.primary,
    },
    statusBadge: {
      paddingHorizontal: spacing[3],
      paddingVertical: spacing[1],
      borderRadius: borderRadius.sm,
    },
    statusText: {
      fontSize: typography.fontSize.sm,
      fontWeight: '500',
      color: colors.background.primary,
    },
    detailsCard: {
      marginBottom: spacing[4],
    },
    cardTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing[4],
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing[2],
    },
    detailLabel: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      flex: 1,
    },
    detailValue: {
      fontSize: typography.fontSize.base,
      color: colors.text.primary,
      fontWeight: '500',
      flex: 2,
      textAlign: 'right',
    },
    technicianCard: {
      marginBottom: spacing[4],
    },
    technicianInfo: {
      marginBottom: spacing[4],
    },
    technicianDetails: {
      flex: 1,
    },
    technicianName: {
      fontSize: typography.fontSize.base,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing[1],
    },
    technicianSpecialization: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing[1],
    },
    technicianRating: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    technicianActions: {
      flexDirection: 'row',
      gap: spacing[2],
    },
    actionsCard: {
      marginBottom: spacing[4],
    },
    actionButtons: {
      flexDirection: 'row',
      gap: spacing[2],
    },
    actionButton: {
      flex: 1,
    },
    cancelButton: {
      backgroundColor: colors.error.main,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing[4],
    },
    errorTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing[2],
    },
    errorText: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing[4],
    },
    backIcon: {
      fontSize: typography.fontSize.xl,
      color: colors.text.primary,
    },
    chatIcon: {
      fontSize: typography.fontSize.xl,
      color: colors.text.primary,
    },
  }), [colors, spacing, typography, borderRadius]);

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading job details...</Text>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.container}>
        <Header
          title="Job Details"
          leftAction={{
            icon: <Text style={styles.backIcon}>←</Text>,
            onPress: () => navigation.goBack(),
            accessibilityLabel: 'Go back',
          }}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Job Not Found</Text>
          <Text style={styles.errorText}>
            The requested job could not be found or you don't have access to it.
          </Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
            style={styles.actionButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title={`Job #${job.id.slice(0, 8)}`}
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
        rightAction={job.technicianId ? {
          icon: <Text style={styles.chatIcon}>💬</Text>,
          onPress: handleStartChat,
          accessibilityLabel: 'Chat with technician',
        } : undefined}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Status Card */}
        <Card style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusLabel}>Status</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(job.status) as string }]}>
              <Text style={styles.statusText}>{getStatusText(job.status)}</Text>
            </View>
          </View>
        </Card>

        {/* Job Details Card */}
        <Card style={styles.detailsCard}>
          <Text style={styles.cardTitle}>Service Request</Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Issue Description:</Text>
            <Text style={styles.detailValue}>{job.issue}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Scheduled Date:</Text>
            <Text style={styles.detailValue}>
              {new Date(job.scheduledAt).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>
              {new Date(job.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
}
