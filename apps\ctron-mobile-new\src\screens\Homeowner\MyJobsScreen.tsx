// CTRON Home - My Jobs Screen
// List of user's jobs with filtering and status management

import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

import { JobAPI } from '../../api/job.api';
import Toast from 'react-native-toast-message';
import { useTheme } from '../../context/ThemeContext';

import { Button } from '../../components/ui/Button';
import { StatusBadge } from '../../components/ui/StatusBadge';

interface Job {
  id: string;
  title: string;
  description: string;
  status: 'PENDING' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  scheduledDate?: string;
  address: string;
  serviceType: string;
  price?: number;
  technician?: {
    id: string;
    user: {
      fullName: string;
    };
  };
}

const MyJobsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { colors, spacing, typography } = useTheme();
  
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');

  const loadJobs = async () => {
    try {
      setLoading(true);
      const response = await JobAPI.getMyJobs();
      setJobs(response || []);
    } catch (error) {
      console.error('Failed to load jobs:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load your jobs',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadJobs();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJobs();
    setRefreshing(false);
  };

  const filteredJobs = jobs.filter(job => {
    if (filter === 'active') {
      return ['PENDING', 'ACCEPTED', 'IN_PROGRESS'].includes(job.status);
    }
    if (filter === 'completed') {
      return ['COMPLETED', 'CANCELLED'].includes(job.status);
    }
    return true;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.semantic.warning;
      case 'ACCEPTED': return colors.semantic.info;
      case 'IN_PROGRESS': return colors.semantic.info;
      case 'COMPLETED': return colors.semantic.success;
      case 'CANCELLED': return colors.semantic.error;
      default: return colors.neutral.main;
    }
  };

  const renderJob = ({ item }: { item: Job }) => (
    <TouchableOpacity
      style={styles.jobItem}
      onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
    >
      <View style={styles.jobHeader}>
        <Text style={styles.jobTitle}>{item.title}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      
      <View style={styles.jobDetails}>
        <Text style={styles.jobDetailText}>Service: {item.serviceType}</Text>
        <Text style={styles.jobDetailText}>Address: {item.address}</Text>
        {item.scheduledDate && (
          <Text style={styles.jobDetailText}>
            Scheduled: {new Date(item.scheduledDate).toLocaleDateString()}
          </Text>
        )}
        {item.price && (
          <Text style={styles.jobDetailText}>Price: £{item.price.toFixed(2)}</Text>
        )}
        {item.technician && (
          <Text style={styles.jobDetailText}>
            Technician: {item.technician.user.fullName}
          </Text>
        )}
      </View>

      <View style={styles.jobActions}>
        <Button
          title="View Details"
          onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
          style={styles.actionButton}
          variant="outline"
        />
        {item.status === 'IN_PROGRESS' && (
          <Button
            title="Track"
            onPress={() => navigation.navigate('TrackTechnician', { jobId: item.id })}
            style={styles.actionButton}
          />
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📋</Text>
      <Text style={styles.emptyTitle}>No jobs found</Text>
      <Text style={styles.emptyText}>
        {filter === 'active' 
          ? "You don't have any active jobs at the moment."
          : filter === 'completed'
          ? "You haven't completed any jobs yet."
          : "You haven't created any jobs yet. Start by booking a service!"
        }
      </Text>
      {filter === 'all' && (
        <Button
          title="Book a Service"
          onPress={() => navigation.navigate('CreateJob')}
          style={styles.emptyButton}
        />
      )}
    </View>
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    headerContainer: {
      padding: spacing.md,
      backgroundColor: colors.background.primary,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.light,
    },
    title: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      marginBottom: spacing.xxs,
      color: colors.text.primary,
    },
    subtitle: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
    },
    filterContainer: {
      flexDirection: 'row',
      padding: spacing.xxs,
      backgroundColor: colors.background.secondary,
    },
    filterButton: {
      flex: 1,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      marginHorizontal: spacing.xs,
      borderRadius: 8,
      backgroundColor: colors.background.primary,
      alignItems: 'center',
    },
    filterButtonActive: {
      backgroundColor: colors.primary.main,
    },
    filterButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.secondary,
    },
    filterButtonTextActive: {
      color: colors.text.inverse,
    },
    jobItem: {
      backgroundColor: colors.background.secondary,
      marginHorizontal: spacing.md,
      marginVertical: spacing.xs,
      borderRadius: 12,
      padding: spacing.md,
      shadowColor: colors.shadows?.sm || '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    jobHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: spacing.sm,
    },
    jobTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      flex: 1,
      marginRight: spacing.sm,
    },
    statusBadge: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      borderRadius: 16,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    jobDetails: {
      marginBottom: spacing.sm,
    },
    jobDetailText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    jobActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    actionButton: {
      flex: 1,
      marginHorizontal: spacing.xs,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyIcon: {
      fontSize: 64,
      color: colors.text.tertiary,
      marginBottom: spacing.md,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: spacing.lg,
    },
    emptyButton: {
      minWidth: 120,
    },
    loader: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
  }), [colors, spacing, typography]);

  if (loading) {
    return (
      <View style={styles.loader}>
        <ActivityIndicator size="large" color={colors.primary.main} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.title}>My Jobs</Text>
        <Text style={styles.subtitle}>Manage your service requests</Text>
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'all' && styles.filterButtonActive
          ]}
          onPress={() => setFilter('all')}
        >
          <Text style={[
            styles.filterButtonText,
            filter === 'all' && styles.filterButtonTextActive
          ]}>
            All ({jobs.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'active' && styles.filterButtonActive
          ]}
          onPress={() => setFilter('active')}
        >
          <Text style={[
            styles.filterButtonText,
            filter === 'active' && styles.filterButtonTextActive
          ]}>
            Active ({jobs.filter(j => ['PENDING', 'ACCEPTED', 'IN_PROGRESS'].includes(j.status)).length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'completed' && styles.filterButtonActive
          ]}
          onPress={() => setFilter('completed')}
        >
          <Text style={[
            styles.filterButtonText,
            filter === 'completed' && styles.filterButtonTextActive
          ]}>
            Completed ({jobs.filter(j => ['COMPLETED', 'CANCELLED'].includes(j.status)).length})
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredJobs}
        renderItem={renderJob}
        keyExtractor={(item: Job) => item.id}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={filteredJobs.length === 0 ? { flex: 1 } : undefined}
      />
    </View>
  );
};

export default MyJobsScreen;
