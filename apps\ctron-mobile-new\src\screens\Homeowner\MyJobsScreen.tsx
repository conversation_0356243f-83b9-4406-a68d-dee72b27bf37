// CTRON Home - My Jobs Screen
// List of user's jobs with filtering and status management

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

import { JobAPI } from '../../api/job.api';
import Toast from 'react-native-toast-message';
import { useTheme } from '../../context/ThemeContext';

import { Button } from '../../components/ui/Button';
import { JobCard } from '../../components/ui/JobCard';
import { Header } from '../../components/ui/Header';


type Job = {
  id: string;
  issue: string;
  status: string;
  scheduledAt: string;
  createdAt: string;
  technician?: {
    user: {
      fullName: string;
    };
  };
};

const MyJobsScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const { colors, spacing, typography, borderRadius } = useTheme();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'completed'>('all');

  const fetchJobs = async () => {
    try {
      console.log('🔄 MyJobsScreen: Fetching jobs...');
      const jobs = await JobAPI.getUserJobs();
      console.log('✅ MyJobsScreen: Jobs fetched:', jobs.length);
      setJobs(jobs);
    } catch (err: any) {
      console.error('❌ MyJobsScreen: Error fetching jobs:', err);
      console.error('❌ MyJobsScreen: Error response:', err.response?.data);
      console.error('❌ MyJobsScreen: Error status:', err.response?.status);
      Toast.show({
        type: 'error',
        text1: 'Failed to fetch jobs',
        text2: err.response?.data?.message || err.message || 'Unknown error',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchJobs();
  };

  const mapJobStatus = (status: string) => {
    const statusMap: { [key: string]: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' } = {
      'PENDING': 'pending',
      'ASSIGNED': 'assigned',
      'IN_PROGRESS': 'active',
      'COMPLETED': 'completed',
      'CANCELLED': 'cancelled',
    };
    return statusMap[status] || 'pending';
  };

  const getFilteredJobs = () => {
    switch (selectedFilter) {
      case 'active':
        return jobs.filter(job =>
          job.status === 'PENDING' ||
          job.status === 'ASSIGNED' ||
          job.status === 'IN_PROGRESS'
        );
      case 'completed':
        return jobs.filter(job => job.status === 'COMPLETED');
      default:
        return jobs;
    }
  };

  const filteredJobs = getFilteredJobs();

  const renderJob = ({ item }: { item: Job }) => {
    const jobData = {
      id: item.id,
      title: `Job #${item.id.slice(0, 8)}`,
      description: item.issue || 'Service request',
      status: mapJobStatus(item.status),
      scheduledAt: item.scheduledAt,
      location: 'Service location',
      technician: item.technician ? {
        name: item.technician.user.fullName,
        rating: 4.8, // Mock rating
      } : undefined,
    };

    return (
      <View>
        <JobCard
          job={jobData}
          onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
          showTechnician={!!item.technician}
          showPrice={false}
        />
      </View>
    );
  };

  const filterOptions = [
    { key: 'all', label: 'All Jobs', count: jobs.length },
    {
      key: 'active',
      label: 'Active',
      count: jobs.filter(job =>
        job.status === 'PENDING' ||
        job.status === 'ASSIGNED' ||
        job.status === 'IN_PROGRESS'
      ).length
    },
    {
      key: 'completed',
      label: 'Completed',
      count: jobs.filter(job => job.status === 'COMPLETED').length
    },
  ];

  const renderFilterButton = (option: typeof filterOptions[0]) => (
    <TouchableOpacity
      key={option.key}
      style={[
        styles.filterButton,
        selectedFilter === option.key && styles.filterButtonActive,
      ]}
      onPress={() => setSelectedFilter(option.key as any)}
    >
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === option.key && styles.filterButtonTextActive,
        ]}
      >
        {option.label} ({option.count})
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.welcomeText}>My Service Requests</Text>
      <Text style={styles.subtitleText}>
        Track and manage your home service jobs
      </Text>

      <View style={styles.filterContainer}>
        {filterOptions.map(renderFilterButton)}
      </View>
    </View>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>
        {selectedFilter === 'all'
          ? 'No jobs yet'
          : `No ${selectedFilter} jobs`
        }
      </Text>
      <Text style={styles.emptyText}>
        {selectedFilter === 'all'
          ? 'Book your first service to get started with CTRON Home'
          : `You don't have any ${selectedFilter} jobs at the moment`
        }
      </Text>
      {selectedFilter === 'all' && (
        <Button
          title="Book a Service"
          onPress={() => navigation.navigate('BookJob')}
          variant="primary"
          style={styles.emptyButton}
        />
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loader}>
        <ActivityIndicator size="large" color={colors.primary[900]} />
      </View>
    );
  }

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    headerContainer: {
      padding: spacing.md,
      paddingBottom: spacing.sm,
    },
    welcomeText: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.xxs,
    },
    subtitleText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.md,
    },
    filterContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      backgroundColor: colors.background.secondary,
      borderRadius: borderRadius.lg,
      padding: spacing.xxs,
      marginBottom: spacing.md,
    },
    filterButton: {
      flex: 1,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      alignItems: 'center',
    },
    filterButtonActive: {
      backgroundColor: colors.primary.main,
    },
    filterButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
    },
    filterButtonTextActive: {
      color: colors.text.inverse,
    },
    contentContainer: {
      paddingHorizontal: spacing.md,
      paddingBottom: spacing.xxl,
    },
    loaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing.sm,
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
      marginTop: spacing.xxl,
    },
    emptyTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    emptyText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing.lg,
    },
    emptyButton: {
      marginTop: spacing.md,
    },
  }), [colors, spacing, typography, borderRadius]);



  return (
    <View style={styles.container}>
      <Header
        title="My Jobs"
        rightAction={{
          icon: <Text style={styles.addIcon}>+</Text>,
          onPress: () => navigation.navigate('BookJob'),
          accessibilityLabel: 'Book new service',
        }}
      />

      <FlatList
        data={filteredJobs}
        keyExtractor={(item) => item.id}
        renderItem={renderJob}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};



export default MyJobsScreen;
