// src/screens/Homeowner/PaymentScreen.tsx

import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import PaymentAPI from '../../api/payment.api';

// import { colors, typography, borderRadius } from '../../styles/theme';
import { useTheme } from '../../context/ThemeContext'; // ✅ Default export



interface RouteParams {
  jobId: string;
}

export default function PaymentScreen() {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const { jobId } = route.params;

  const [amount, setAmount] = useState('');
  const [currency] = useState('GBP');
  const [loading, setLoading] = useState(false);

  const handlePayment = async () => {
    if (!amount || isNaN(Number(amount))) {
      Alert.alert('Validation Error', 'Please enter a valid amount.');
      return;
    }

    try {
      setLoading(true);
      await PaymentAPI.createPayment(jobId, parseFloat(amount), currency); // ✅ Assumes this endpoint is wired
      Alert.alert('Success', 'Payment initiated successfully.');
    } catch (err: any) {
      console.error(err);
      Alert.alert('Error', err?.response?.data?.message || 'Payment failed.');
    } finally {
      setLoading(false);
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      padding: spacing.xl,
      justifyContent: 'center',
      backgroundColor: colors.background.primary,
    },
    title: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      marginBottom: spacing.xl,
      textAlign: 'center',
      color: colors.text.primary,
    },
    input: {
      backgroundColor: colors.background.secondary,
      padding: spacing.md,
      borderRadius: borderRadius.md,
      marginBottom: spacing.md,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
    },
    button: {
      backgroundColor: colors.primary.main,
      padding: spacing.md,
      borderRadius: borderRadius.md,
      alignItems: 'center',
    },
    buttonText: {
      color: colors.text.inverse,
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize.md,
    },
  }), [colors, spacing, typography, borderRadius]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Payment for Job #{jobId?.slice(0, 8)}</Text>

      <TextInput
        style={styles.input}
        placeholder="Amount"
        keyboardType="numeric"
        value={amount}
        onChangeText={setAmount}
      />

      <TouchableOpacity
        style={[styles.button, loading && { opacity: 0.6 }]}
        onPress={handlePayment}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Pay Now</Text>
        )}
      </TouchableOpacity>
    </View>
  );
}
