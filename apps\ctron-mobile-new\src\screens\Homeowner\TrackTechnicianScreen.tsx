// CTRON Home - Track Technician Screen
// Real-time tracking of technician location and job status

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, Alert, ScrollView } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { JobAPI } from '../../api/job.api';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import * as Linking from 'expo-linking';
import { formatDistanceToNow } from 'date-fns';

interface Job {
  id: string;
  status: string;
  scheduledDate?: string;
  address: string;
  serviceType: string;
  description?: string;
  technicianId?: string;
  technician?: {
    id: string;
    user: {
      fullName: string;
      phone?: string;
    };
    specialization: string;
    rating?: number;
  };
}

interface RouteParams {
  jobId: string;
}

const TrackTechnicianScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAuth();
  const { colors, spacing, typography } = useTheme();
  
  const { jobId } = route.params as RouteParams;
  
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [technicianLocation, setTechnicianLocation] = useState<any>(null);
  const [estimatedArrival, setEstimatedArrival] = useState<string>('');

  const loadJobDetails = useCallback(async () => {
    try {
      setLoading(true);
      const jobData = await JobAPI.getJobById(jobId);
      setJob(jobData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load job details');
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  const calculateEstimatedArrival = useCallback(() => {
    // This is a simplified calculation - in production you'd use a mapping service
    // For now, we'll estimate based on distance and average speed
    if (job?.scheduledDate) {
      const scheduledTime = new Date(job.scheduledDate);
      const now = new Date();

      if (scheduledTime > now) {
        setEstimatedArrival(formatDistanceToNow(scheduledTime, { addSuffix: true }));
      } else {
        setEstimatedArrival('Arrived');
      }
    }
  }, [job?.scheduledDate]);

  const handleLocationUpdate = useCallback((data: any) => {
    setTechnicianLocation(data);
    calculateEstimatedArrival();
  }, [calculateEstimatedArrival]);

  const handleStatusChange = useCallback((data: any) => {
    if (data.jobId === jobId && job) {
      setJob({ ...job, status: data.status });
    }
  }, [jobId, job]);

  const handleJobUpdate = useCallback((data: any) => {
    if (data.id === jobId) {
      setJob(data);
    }
  }, [jobId]);

  useEffect(() => {
    loadJobDetails();
  }, [loadJobDetails, handleLocationUpdate, handleStatusChange, handleJobUpdate]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJobDetails();
    setRefreshing(false);
  };

  const handleCallTechnician = () => {
    if (job?.technician?.user?.phone) {
      Linking.openURL(`tel:${job.technician.user.phone}`);
    } else {
      Alert.alert('Error', 'Technician phone number not available');
    }
  };

  const handleCancelJob = () => {
    Alert.alert(
      'Cancel Job',
      'Are you sure you want to cancel this job? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await JobAPI.cancelJob(jobId);
              Alert.alert('Success', 'Job has been cancelled');
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel job');
            }
          }
        }
      ]
    );
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing.md,
      color: colors.text.secondary,
      fontSize: typography.fontSize.base,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.lg,
      backgroundColor: colors.background.primary,
    },
    errorText: {
      color: colors.text.primary,
      fontSize: typography.fontSize.lg,
      textAlign: 'center',
      marginBottom: spacing.lg,
    },
    content: {
      padding: spacing.md,
    },
    section: {
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    detailLabel: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
    },
    detailValue: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    statusContainer: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      borderRadius: 4,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    actionContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
    },
    actionButton: {
      flex: 1,
      marginHorizontal: spacing.xs,
    },
  }), [colors, spacing, typography]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner size="large" />
        <Text style={styles.loadingText}>Loading tracking information...</Text>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Job not found</Text>
        <Button title="Go Back" onPress={() => navigation.goBack()} variant="outline" />
      </View>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.semantic.warning;
      case 'ACCEPTED': return colors.semantic.info;
      case 'IN_PROGRESS': return colors.semantic.info;
      case 'COMPLETED': return colors.semantic.success;
      case 'CANCELLED': return colors.semantic.error;
      default: return colors.neutral[400];
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Card>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Job Details</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Service:</Text>
              <Text style={styles.detailValue}>{job.serviceType}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Status:</Text>
              <View style={[styles.statusContainer, { backgroundColor: getStatusColor(job.status) }]}>
                <Text style={styles.statusText}>{job.status}</Text>
              </View>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Address:</Text>
              <Text style={styles.detailValue}>{job.address}</Text>
            </View>
            {job.scheduledDate && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Scheduled:</Text>
                <Text style={styles.detailValue}>
                  {new Date(job.scheduledDate).toLocaleDateString()}
                </Text>
              </View>
            )}
            {estimatedArrival && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Estimated Arrival:</Text>
                <Text style={styles.detailValue}>{estimatedArrival}</Text>
              </View>
            )}
          </View>

          {job.technician && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Technician</Text>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Name:</Text>
                <Text style={styles.detailValue}>{job.technician.user.fullName}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Specialization:</Text>
                <Text style={styles.detailValue}>{job.technician.specialization}</Text>
              </View>
              {job.technician.rating && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Rating:</Text>
                  <Text style={styles.detailValue}>{job.technician.rating}/5</Text>
                </View>
              )}
            </View>
          )}

          <View style={styles.actionContainer}>
            {job.technician?.user?.phone && (
              <Button
                title="Call Technician"
                onPress={handleCallTechnician}
                style={styles.actionButton}
              />
            )}
            <Button
              title="Cancel Job"
              onPress={handleCancelJob}
              variant="secondary"
              style={styles.actionButton}
              disabled={job.status === 'COMPLETED' || job.status === 'CANCELLED'}
            />
          </View>
        </Card>
      </View>
    </ScrollView>
  );
};

export default TrackTechnicianScreen;
