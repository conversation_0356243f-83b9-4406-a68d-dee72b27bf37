// CTRON Home - Notifications Screen
// Display and manage user notifications

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { HomeownerStackParamList } from '../../types/navigation';
import type { StackNavigationProp } from '@react-navigation/stack';
import { api } from '../../api/api';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { Header } from '../../components/ui/Header';

interface Notification {
  id: string;
  title: string;
  body: string;
  type: 'job' | 'chat' | 'payment' | 'system';
  isRead: boolean;
  createdAt: string;
  data?: {
    jobId?: string;
    chatId?: string;
    paymentId?: string;
  };
}

type NavigationProp = StackNavigationProp<HomeownerStackParamList>;

const NotificationsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const { colors, spacing, typography } = useTheme();

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const loadNotifications = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/notifications', {
        params: filter === 'unread' ? { unread: true } : {},
      });
      setNotifications(response.data || []);
    } catch (error: any) {
      console.error('Failed to load notifications:', error);
      Alert.alert('Error', 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  }, [filter]);

  useEffect(() => {
    loadNotifications();
  }, [filter, loadNotifications]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await api.patch(`/api/notifications/${notificationId}/read`);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleNotificationPress = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    // Navigate based on notification type
    if (notification.type === 'job' && notification.data?.jobId) {
      navigation.navigate('JobDetails', { jobId: notification.data.jobId });
    } else if (notification.type === 'chat' && notification.data?.chatId) {
      navigation.navigate('Chat', {
        chatId: notification.data.chatId,
        jobTitle: notification.title
      });
    }
  };

  const renderNotification = ({ item }: { item: Notification }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        !item.isRead && styles.unreadNotification
      ]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={styles.notificationHeader}>
        <Text style={styles.notificationTitle}>{item.title}</Text>
        <Text style={styles.notificationTime}>
          {new Date(item.createdAt).toLocaleDateString()}
        </Text>
      </View>
      <Text style={styles.notificationBody}>{item.body}</Text>
      <View style={styles.notificationActions}>
        <Text style={styles.notificationType}>{item.type.toUpperCase()}</Text>
        {!item.isRead && (
          <TouchableOpacity
            style={styles.markReadButton}
            onPress={() => markAsRead(item.id)}
          >
            <Text style={styles.markReadButtonText}>Mark as Read</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>🔔</Text>
      <Text style={styles.emptyTitle}>No notifications</Text>
      <Text style={styles.emptyText}>
        {filter === 'unread'
          ? "You're all caught up! No unread notifications."
          : "You don't have any notifications yet."
        }
      </Text>
    </View>
  );

  const filteredNotifications = filter === 'unread'
    ? notifications.filter(n => !n.isRead)
    : notifications;

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing.md,
      color: colors.text.secondary,
      fontSize: typography.fontSize.base,
    },
    filterContainer: {
      flexDirection: 'row',
      padding: spacing.md,
      gap: spacing.sm,
    },
    filterButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: 20,
      backgroundColor: colors.background.secondary,
    },
    filterButtonActive: {
      backgroundColor: colors.primary.main,
    },
    filterButtonText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    filterButtonTextActive: {
      color: colors.text.inverse,
      fontWeight: typography.fontWeight.bold,
    },
    notificationItem: {
      backgroundColor: colors.background.secondary,
      marginHorizontal: spacing.md,
      marginVertical: spacing.xs,
      borderRadius: 8,
      padding: spacing.md,
    },
    unreadNotification: {
      backgroundColor: colors.background.primary,
      borderLeftWidth: 4,
      borderLeftColor: colors.primary.main,
    },
    notificationHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: spacing.xs,
    },
    notificationTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      flex: 1,
      marginRight: spacing.sm,
    },
    notificationTime: {
      fontSize: typography.fontSize.xs,
      color: colors.text.tertiary,
    },
    notificationBody: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    notificationActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    notificationType: {
      fontSize: typography.fontSize.xs,
      color: colors.text.tertiary,
      fontWeight: typography.fontWeight.bold,
    },
    markReadButton: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      backgroundColor: colors.primary.main,
      borderRadius: 4,
    },
    markReadButtonText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.inverse,
      fontWeight: typography.fontWeight.bold,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyIcon: {
      fontSize: 64,
      color: colors.text.tertiary,
      marginBottom: spacing.md,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 24,
    },
    backIcon: {
      fontSize: 20,
      color: colors.text.primary,
    },
  }), [colors, spacing, typography]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading notifications...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Notifications"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: "Go back"
        }}
        rightAction={unreadCount > 0 ? {
          icon: <Text style={styles.backIcon}>{unreadCount}</Text>,
          onPress: () => { },
          accessibilityLabel: `${unreadCount} unread notifications`
        } : undefined}
      />

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'all' && styles.filterButtonActive
          ]}
          onPress={() => setFilter('all')}
        >
          <Text style={[
            styles.filterButtonText,
            filter === 'all' && styles.filterButtonTextActive
          ]}>
            All ({notifications.length})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'unread' && styles.filterButtonActive
          ]}
          onPress={() => setFilter('unread')}
        >
          <Text style={[
            styles.filterButtonText,
            filter === 'unread' && styles.filterButtonTextActive
          ]}>
            Unread ({unreadCount})
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredNotifications}
        renderItem={renderNotification}
        keyExtractor={(item: Notification) => item.id}
        ListEmptyComponent={renderEmptyState}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default NotificationsScreen;
