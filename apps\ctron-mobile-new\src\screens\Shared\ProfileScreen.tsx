// src/screens/Shared/ProfileScreen.tsx

import React, { useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAuth } from '../../context/AuthContext';

const ProfileScreen = () => {
  const { user } = useAuth();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Profile</Text>

      <Text style={styles.label}>Full Name:</Text>
      <Text style={styles.value}>{user?.fullName}</Text>

      <Text style={styles.label}>Email:</Text>
      <Text style={styles.value}>{user?.email}</Text>

      <Text style={styles.label}>Role:</Text>
      <Text style={styles.value}>{user?.role}</Text>
    </View>
  );
};

export default ProfileScreen;

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: '#fff',
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 24,
    },
    label: {
      marginTop: 12,
      fontWeight: '600',
      color: '#444',
    },
    value: {
      fontSize: 16,
      marginTop: 4,
      color: '#222',
    },
  }), []);
