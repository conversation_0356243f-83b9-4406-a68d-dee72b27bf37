// src/screens/Shared/SettingsScreen.tsx

import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from '../../context/AuthContext';

const SettingsScreen = () => {
  const { logout } = useAuth();

  const handleLogout = () => {
    Alert.alert('Confirm Logout', 'Are you sure you want to log out?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Log Out', onPress: () => logout() },
    ]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Settings</Text>

      <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
        <Text style={styles.logoutText}>Log out</Text>
      </TouchableOpacity>
    </View>
  );
};

export default SettingsScreen;

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: '#fff',
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 16,
    },
    logoutButton: {
      marginTop: 24,
      padding: 16,
      backgroundColor: '#f44336',
      borderRadius: 8,
      alignItems: 'center',
    },
    logoutText: {
      color: '#fff',
      fontWeight: '600',
      fontSize: 16,
    },
  }), []);
