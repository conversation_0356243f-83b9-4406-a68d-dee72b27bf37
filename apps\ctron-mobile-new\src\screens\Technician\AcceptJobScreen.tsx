// src/screens/Technician/AcceptJobScreen.tsx

import React, { useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { JobAPI } from '../../api/job.api'; // ✅ default export

interface Job {
  id: string;
  issue: string;
  scheduledAt: string;
}

type RouteParams = {
  job: Job;
};

const AcceptJobScreen = () => {
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const navigation = useNavigation<any>();
  const { job } = route.params;

  const handleAccept = async () => {
    try {
      await JobAPI.updateStatus(job.id, 'IN_PROGRESS'); // ✅ FIXED: valid status
      Alert.alert('Success', 'You accepted the job');
      navigation.goBack();
    } catch (err: any) {
      Alert.alert('Error', err?.response?.data?.message || 'Failed to accept job');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Accept Job #{job.id.slice(0, 8)}</Text>

      <Text style={styles.label}>Issue:</Text>
      <Text style={styles.value}>{job.issue}</Text>

      <Text style={styles.label}>Scheduled At:</Text>
      <Text style={styles.value}>
        {new Date(job.scheduledAt).toLocaleString()}
      </Text>

      <TouchableOpacity style={styles.button} onPress={handleAccept}>
        <Text style={styles.buttonText}>Accept Job</Text>
      </TouchableOpacity>
    </View>
  );
};

export default AcceptJobScreen;

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: '#fff',
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 16,
    },
    label: {
      marginTop: 12,
      fontWeight: '600',
    },
    value: {
      fontSize: 16,
      marginTop: 4,
      color: '#333',
    },
    button: {
      marginTop: 32,
      backgroundColor: '#004AAD',
      padding: 16,
      borderRadius: 8,
      alignItems: 'center',
    },
    buttonText: {
      color: '#fff',
      fontWeight: '600',
      fontSize: 16,
    },
  }), []);
