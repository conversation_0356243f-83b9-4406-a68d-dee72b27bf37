// src/screens/Technician/ActiveJobScreen.tsx
import React, { useMemo, useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { JobAPI } from '../../api/job.api';
import { getPresignedUrl, uploadToS3 } from '../../services/uploadService';
import { socket } from '../../utils/socket';
import type { Job } from '../../types/job';
import { withScreenErrorBoundary } from '../../components/ScreenErrorBoundary';

type RouteParams = {
  jobId: string;
};

const ActiveJobScreen = () => {
    const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
    const navigation = useNavigation<any>();
    const { jobId } = route.params;

    const [job, setJob] = useState<Job | null>(null);
    const [loading, setLoading] = useState(true);
    const [updating, setUpdating] = useState(false);
    const [proofImage, setProofImage] = useState<string | null>(null);

    const fetchJob = useCallback(async () => {
      try {
        const res = await JobAPI.getJobDetails(jobId);
        setJob(res);
      } catch (err: any) {
        Alert.alert('Error', err?.response?.data?.message || 'Failed to load job');
      } finally {
        setLoading(false);
      }
    }, [jobId]);

    useEffect(() => {
      fetchJob();
    }, [fetchJob]);

    const styles = useMemo(() => StyleSheet.create({
      loaderContainer: {
        flex: 1,
        justifyContent: 'center',
      },
      container: {
        flex: 1,
        padding: 24,
        backgroundColor: '#fff',
      },
      title: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 16,
      },
      label: {
        marginTop: 12,
        fontWeight: '600',
      },
      value: {
        fontSize: 16,
        marginTop: 4,
        color: '#333',
      },
      uploadBtn: {
        marginTop: 20,
        padding: 14,
        backgroundColor: '#888',
        borderRadius: 8,
        alignItems: 'center',
      },
      uploadBtnText: {
        color: '#fff',
        fontWeight: '600',
      },
      preview: {
        marginTop: 16,
        height: 180,
        borderRadius: 10,
        resizeMode: 'cover',
      },
      button: {
        marginTop: 24,
        backgroundColor: '#004AAD',
        padding: 16,
        borderRadius: 8,
        alignItems: 'center',
      },
      buttonText: {
        color: '#fff',
        fontWeight: '600',
        fontSize: 16
      },
    }), []);





    const selectProofImage = async () => {
      const permission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permission.granted) {
        return Alert.alert('Permission needed', 'Camera roll access is required.');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.5,
      });

      if (!result.canceled && result.assets.length > 0) {
        setProofImage(result.assets[0].uri);
      }
    };

    const updateStatus = async (status: 'COMPLETED' | 'IN_PROGRESS') => {
      if (status === 'COMPLETED' && !proofImage) {
        return Alert.alert('Proof Required', 'Please upload a photo before completing.');
      }

      try {
        setUpdating(true);
        let photoUrl: string | undefined;

        if (status === 'COMPLETED' && proofImage) {
          const fileType = 'image/jpeg';
          const { url, key } = await getPresignedUrl(fileType);
          await uploadToS3(url, proofImage, fileType);

          // Construct public S3 URL
          photoUrl = `https://${process.env.EXPO_PUBLIC_S3_BUCKET}.s3.${process.env.EXPO_PUBLIC_AWS_REGION}.amazonaws.com/${key}`;
        }

        // Send update to backend
        await JobAPI.updateStatus(jobId, status, { photoUrl });

        // Emit socket update
        socket.emit('jobStatusUpdated', jobId, status);

        Alert.alert('Success', `Marked as ${status}`);
        navigation.goBack();
      } catch (err: any) {
        Alert.alert('Error', err?.response?.data?.message || 'Update failed');
      } finally {
        setUpdating(false);
      }
    };

    if (loading || !job) {
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#004AAD" />
        </View>
      );
    }

    return (
      <View style={styles.container}>
        <Text style={styles.title}>Job #{job.id.slice(0, 8)}</Text>
        <Text style={styles.label}>Status:</Text>
        <Text style={styles.value}>{job.status}</Text>

        <Text style={styles.label}>Issue:</Text>
        <Text style={styles.value}>{job.issue}</Text>

        <Text style={styles.label}>Scheduled At:</Text>
        <Text style={styles.value}>
          {new Date(job.scheduledAt).toLocaleString()}
        </Text>

        <TouchableOpacity style={styles.uploadBtn} onPress={selectProofImage}>
          <Text style={styles.uploadBtnText}>
            {proofImage ? 'Change Photo' : 'Select Proof Photo'}
          </Text>
        </TouchableOpacity>

        {proofImage && (
          <Image source={{ uri: proofImage }} style={styles.preview} />
        )}

        {job.status !== 'COMPLETED' && (
          <TouchableOpacity
            style={[styles.button, updating && { opacity: 0.6 }]}
            onPress={() => updateStatus('COMPLETED')}
            disabled={updating}
          >
            <Text style={styles.buttonText}>
              {updating ? 'Uploading & Completing...' : 'Mark as Completed'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };





export default withScreenErrorBoundary(ActiveJobScreen, 'ActiveJob');
