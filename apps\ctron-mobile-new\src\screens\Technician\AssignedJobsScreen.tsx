// src/screens/Technician/AssignedJobsScreen.tsx

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  Alert,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { TechnicianStackParamList } from '../../navigation/TechnicianStack';
import { JobAPI } from '../../api/job.api';
import { Job } from '../../types/job';

export default function AssignedJobsScreen() {
  const navigation = useNavigation<NativeStackNavigationProp<TechnicianStackParamList>>();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error] = useState<string | null>(null);

  const fetchJobs = useCallback(async () => {
    try {
      const jobs = await JobAPI.getTechnicianJobs();
      setJobs(jobs);
    } catch (e) {
      console.error('Error fetching assigned jobs:', e);
      Alert.alert('Error', 'Failed to load assigned jobs.');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  const renderItem = ({ item }: { item: Job }) => (
    <TouchableOpacity
      style={styles.card}
      onPress={() => navigation.navigate('JobDetailsScreen', { jobId: item.id })}
    >
      <Text style={styles.title}>{item.issue}</Text>
      <Text>Status: {item.status}</Text>
      <Text>Scheduled: {new Date(item.scheduledAt).toLocaleString()}</Text>
    </TouchableOpacity>
  );

  if (loading) return <ActivityIndicator style={{ marginTop: 20 }} />;
  if (error) return <Text style={{ marginTop: 20, color: 'red' }}>{error}</Text>;

  return (
    <FlatList
      contentContainerStyle={{ padding: 16 }}
      data={jobs}
      keyExtractor={(item) => item.id}
      renderItem={renderItem}
      ListEmptyComponent={<Text>No jobs assigned.</Text>}
    />
  );
}

const styles = useMemo(() => StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 6,
  },
}), []);
