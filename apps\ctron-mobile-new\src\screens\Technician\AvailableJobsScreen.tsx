// CTRON Home - Available Jobs Screen for Technicians
// Browse and accept available job opportunities

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,

  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useJobs } from '../../context/JobContext';
import { useAuth } from '../../context/AuthContext';

import { useTheme } from '../../context/ThemeContext';
// ... existing code ...

import { Button } from '../../components/ui/Button';
import { JobCard } from '../../components/ui/JobCard';
import { Header } from '../../components/ui/Header';
import { Card } from '../../components/ui/Card';
import { StatusBadge } from '../../components/ui/StatusBadge';
import { colors, spacing, typography, borderRadius } from '@/theme';



const AvailableJobsScreen = () => {
  const navigation = useNavigation<any>();
  const { } = useTheme();
  const { } = useAuth();
  const { assignedJobs = [], refreshJobs, loading } = useJobs();
  const availableJobs = assignedJobs; // Use assignedJobs as available jobs for technicians
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'urgent' | 'nearby'>('all');

  useEffect(() => {
    refreshJobs();
  }, [refreshJobs]);





  const getFilteredJobs = () => {
    switch (selectedFilter) {
      case 'urgent':
        return availableJobs.filter((job: any) =>
          // Mock urgent logic - in real app, check priority or scheduled time
          new Date(job.scheduledAt) <= new Date(Date.now() + 24 * 60 * 60 * 1000)
        );
      case 'nearby':
        // Mock nearby logic - in real app, use geolocation
        return availableJobs.slice(0, Math.ceil(availableJobs.length / 2));
      default:
        return availableJobs;
    }
  };

  const filteredJobs = getFilteredJobs();

  const filterOptions = [
    { key: 'all', label: 'All Jobs', count: availableJobs.length },
    {
      key: 'urgent',
      label: 'Urgent',
      count: availableJobs.filter((job: any) =>
        new Date(job.scheduledAt) <= new Date(Date.now() + 24 * 60 * 60 * 1000)
      ).length
    },
    {
      key: 'nearby',
      label: 'Nearby',
      count: Math.ceil(availableJobs.length / 2)
    },
  ];

  const renderFilterButton = (option: typeof filterOptions[0]) => (
    <TouchableOpacity
      key={option.key}
      style={[
        styles.filterButton,
        selectedFilter === option.key && styles.filterButtonActive,
      ]}
      onPress={() => setSelectedFilter(option.key as any)}
    >
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === option.key && styles.filterButtonTextActive,
        ]}
      >
        {option.label} ({option.count})
      </Text>
    </TouchableOpacity>
  );

  const renderJobItem = ({ item }: { item: any }) => {
    const isUrgent = new Date(item.scheduledAt) <= new Date(Date.now() + 24 * 60 * 60 * 1000);

    const jobData = {
      id: item.id,
      title: `Job #${item.id.slice(0, 8)}`,
      description: item.issue || 'Service request',
      status: 'pending' as const,
      scheduledAt: item.scheduledAt,
      location: 'Customer location',
      price: undefined,
    };

    return (
      <View style={styles.jobItemContainer}>
        <JobCard
          job={jobData}
          onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
          showTechnician={false}
          showPrice={false}
        />
        {isUrgent && (
          <StatusBadge
            status="emergency"
            size="sm"
            style={styles.urgentBadge}
          />
        )}
        <View style={styles.jobActions}>
          <Button
            title="View Details"
            onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
            variant="ghost"
            size="sm"
            style={styles.actionButton}
          />
          <Button
            title="Accept Job"
            onPress={() => {/* Handle accept job */ }}
            variant="primary"
            size="sm"
            style={styles.actionButton}
          />
        </View>
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.welcomeText}>Available Jobs</Text>
      <Text style={styles.subtitleText}>
        Browse and accept job opportunities in your area
      </Text>

      <View style={styles.filterContainer}>
        {filterOptions.map(renderFilterButton)}
      </View>

      <Card style={styles.statsCard}>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{availableJobs.length}</Text>
            <Text style={styles.statLabel}>Available</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {availableJobs.filter((job: any) =>
                new Date(job.scheduledAt) <= new Date(Date.now() + 24 * 60 * 60 * 1000)
              ).length}
            </Text>
            <Text style={styles.statLabel}>Urgent</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Math.ceil(availableJobs.length / 2)}
            </Text>
            <Text style={styles.statLabel}>Nearby</Text>
          </View>
        </View>
      </Card>
    </View>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>
        {selectedFilter === 'all'
          ? 'No jobs available'
          : `No ${selectedFilter} jobs`
        }
      </Text>
      <Text style={styles.emptyText}>
        {selectedFilter === 'all'
          ? 'Check back later for new job opportunities'
          : `No ${selectedFilter} jobs available at the moment`
        }
      </Text>
      <Button
        title="Refresh"
        onPress={refreshJobs}
        variant="primary"
        style={styles.emptyButton}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Available Jobs"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
        rightAction={{
          icon: <Text style={styles.refreshIcon}>🔄</Text>,
          onPress: refreshJobs,
          accessibilityLabel: 'Refresh jobs',
        }}
      />

      <FlatList
        data={filteredJobs}
        keyExtractor={(item: any) => item.id}
        renderItem={renderJobItem}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        // Pull to refresh functionality can be added here if needed
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.systemBackground,
  },

  contentContainer: {
    padding: spacing.md,
    paddingBottom: spacing.xxxl,
  },

  headerContainer: {
    marginBottom: spacing.xl,
  },

  welcomeText: {
    ...typography.largeTitle,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  subtitleText: {
    ...typography.body,
    color: colors.text.secondary,
    marginBottom: spacing.xl,
  },

  filterContainer: {
    flexDirection: 'row',
    marginBottom: spacing.xl,
    gap: spacing.xs,
  },

  filterButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.separator,
    backgroundColor: colors.systemBackground,
    alignItems: 'center',
  },

  filterButtonActive: {
    backgroundColor: colors.primary.main,
    borderColor: colors.primary.main,
  },

  filterButtonText: {
    ...typography.footnote,
    fontWeight: '500' as const,
    color: colors.text.secondary,
  },

  filterButtonTextActive: {
    color: colors.white,
    fontWeight: '600' as const,
  },

  statsCard: {
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },

  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  statItem: {
    flex: 1,
    alignItems: 'center',
  },

  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.separator,
    marginHorizontal: spacing.sm,
  },

  statValue: {
    ...typography.largeTitle,
    color: colors.primary.main,
    marginBottom: spacing.xs,
  },

  statLabel: {
    ...typography.footnote,
    color: colors.tertiaryLabel,
    textAlign: 'center',
  },

  jobItemContainer: {
    marginBottom: spacing.md,
    position: 'relative',
  },

  urgentBadge: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
    zIndex: 1,
  },

  jobActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.sm,
    gap: spacing.sm,
  },

  actionButton: {
    flex: 1,
  },

  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
    paddingHorizontal: spacing.xl,
  },

  emptyTitle: {
    ...typography.title1,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },

  emptyText: {
    ...typography.body,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },

  emptyButton: {
    minWidth: 150,
  },

  backIcon: {
    fontSize: 24,
    color: colors.text.secondary,
  },

  refreshIcon: {
    fontSize: 20,
    color: colors.text.secondary,
  },
}), [colors, spacing, typography, borderRadius]);

export default AvailableJobsScreen;
