import * as React from "react";
import  { useEffect, useState, useMemo } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Linking, Alert, SafeAreaView } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../hooks/useAuth';
import { TechnicianAPI } from '../../api/technician.api';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { TechnicianStackParamList } from '../../navigation/TechnicianStack';
// ... existing code ...

type KycScreenNavigationProp = StackNavigationProp<TechnicianStackParamList, 'KycScreen'>;

export const KycScreen: React.FC = () => {
  const { colors, typography, spacing } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<KycScreenNavigationProp>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.secondary,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
    },
    title: {
      fontSize: typography.fontSize['2xl'],
      fontWeight: typography.fontWeight.bold,
      marginBottom: spacing.md,
      color: colors.text.primary,
    },
    description: {
      fontSize: typography.fontSize.base,
      textAlign: 'center',
      marginBottom: spacing.lg,
      color: colors.text.secondary,
      lineHeight: typography.fontSize.lg,
    },
    button: {
      marginTop: spacing.md,
      width: '80%',
    },
    spinner: {
      marginTop: spacing.md,
    },
    errorText: {
      color: colors.error.main,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    note: {
      fontSize: typography.fontSize.sm,
      textAlign: 'center',
      marginTop: spacing.lg,
      color: colors.text.tertiary,
      fontStyle: 'italic',
    },
  }), [colors, spacing, typography]);

  useEffect(() => {
    if (!user || user.role !== 'TECHNICIAN') {
      Alert.alert('Unauthorized', 'You must be a technician to access this page.');
      navigation.goBack();
    }
  }, [user, navigation]);

  const handleStartVerification = async () => {
    if (!user?.technicianProfile?.id) {
      setError('Technician profile not found.');
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const response = await TechnicianAPI.createVerificationSession(user.technicianProfile.id);
      if (response.url) {
        Linking.openURL(response.url);
      } else {
        setError('Failed to get verification URL.');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred during verification initiation.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>KYC Verification</Text>
        <Text style={styles.description}>
          To ensure the safety and trust of our platform, we require all technicians to complete a Know Your Customer (KYC) verification process.
          This involves verifying your identity through a secure third-party service.
        </Text>

        {error && <Text style={styles.errorText}>{error}</Text>}

        <Button
          title={loading ? 'Starting Verification...' : 'Start Verification'}
          onPress={handleStartVerification}
          disabled={loading}
          style={styles.button}
        />

        {loading && <ActivityIndicator size="large" color="#007AFF" style={styles.spinner} />}

        <Text style={styles.note}>
          You will be redirected to a secure page to complete the verification. Please follow the instructions carefully.
          After successful verification, you will be redirected back to the app.
        </Text>
      </View>
    </SafeAreaView>
  );
};