// CTRON Home - Enhanced Technician Settings Screen
// Comprehensive settings management with permissions and preferences

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';

import { colors, typography, borderRadius } from '../../styles/theme';
import { useTheme } from '../../context/ThemeContext';

// Platform-aware AsyncStorage import to prevent Android bundling issues
import AsyncStorage from '@react-native-async-storage/async-storage';
// ... existing code ...



/**
 * Settings data interface
 */
interface SettingsData {
  notifications: {
    jobAlerts: boolean;
    messageAlerts: boolean;
    paymentAlerts: boolean;
    marketingEmails: boolean;
  };
  location: {
    shareLocation: boolean;
    backgroundTracking: boolean;
  };
  preferences: {
    darkMode: boolean;
    autoAcceptJobs: boolean;
    showEarningsPublicly: boolean;
    receiveJobRecommendations: boolean;
  };
  privacy: {
    profileVisible: boolean;
    showContactInfo: boolean;
    allowDirectMessages: boolean;
  };
}

/**
 * Default settings configuration
 */
const DEFAULT_SETTINGS: SettingsData = {
  notifications: {
    jobAlerts: true,
    messageAlerts: true,
    paymentAlerts: true,
    marketingEmails: false,
  },
  location: {
    shareLocation: true,
    backgroundTracking: false,
  },
  preferences: {
    darkMode: false,
    autoAcceptJobs: false,
    showEarningsPublicly: false,
    receiveJobRecommendations: true,
  },
  privacy: {
    profileVisible: true,
    showContactInfo: true,
    allowDirectMessages: true,
  },
};

/**
 * Enhanced settings screen for technician preferences
 */
export default function SettingsScreen() {
    const { spacing, colors } = useTheme();
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary || '#f8f9fa',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary || '#f8f9fa',
    },
    loadingText: {
      ...typography.body.medium,
      color: colors.text.primary || '#6c757d',
      marginTop: spacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: colors.neutral?.light || '#e9ecef',
    },
    title: {
      ...typography.heading.h3,
      color: colors.text.primary || '#212529',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: spacing.lg,
      paddingBottom: spacing.xl * 2,
    },
    section: {
      backgroundColor: '#fff',
      borderRadius: borderRadius.md,
      marginBottom: spacing.lg,
      padding: spacing.lg,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    sectionTitle: {
      ...typography.heading.h4,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    sectionDescription: {
      ...typography.body.small,
      color: colors.text.secondary,
      marginBottom: spacing.md,
    },
    settingItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.neutral?.light,
    },
    settingItemLast: {
      borderBottomWidth: 0,
    },
    settingInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      marginRight: spacing.md,
    },
    settingText: {
      marginLeft: spacing.md,
      flex: 1,
    },
    settingLabel: {
      ...typography.body.large,
      color: colors.text.primary,
    },
    permissionButton: {
      backgroundColor: colors.primary.main,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borderRadius.sm,
    },
    permissionButtonGranted: {
      backgroundColor: colors.success.main,
    },
    permissionButtonText: {
      ...typography.button.small,
      color: '#fff',
    },
    permissionButtonTextGranted: {
      color: '#fff',
    },
    resetButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.error.light,
      padding: spacing.md,
      borderRadius: borderRadius.md,
      marginTop: spacing.lg,
    },
    resetButtonText: {
      ...typography.button.medium,
      color: colors.error.main,
      marginLeft: spacing.sm,
    },
  }), [spacing, colors]);
  const [settings, setSettings] = useState<SettingsData>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [permissions, setPermissions] = useState({
    location: false,
    notifications: false,
  });

  /**
   * Load settings from AsyncStorage on component mount
   */
  useEffect(() => {
    loadSettings();
    checkPermissions();
  }, []);

  /**
   * Load saved settings from local storage
   */
  const loadSettings = async () => {
    try {
      setLoading(true);
      const savedSettings = await AsyncStorage.getItem('technician_settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsedSettings });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save settings to local storage
   */
  const saveSettings = async (newSettings: SettingsData) => {
    try {
      setSaving(true);
      await AsyncStorage.setItem('technician_settings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  /**
   * Check current permissions status
   */
  const checkPermissions = async () => {
    try {
      // Check location permission
      const locationStatus = await Location.getForegroundPermissionsAsync();

      // Check notification permission
      const notificationStatus = await Notifications.getPermissionsAsync();

      setPermissions({
        location: locationStatus.status === 'granted',
        notifications: notificationStatus.status === 'granted',
      });
    } catch (error) {
      console.error('Failed to check permissions:', error);
    }
  };

  /**
   * Request location permission
   */
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setPermissions(prev => ({ ...prev, location: true }));
        Alert.alert('Success', 'Location permission granted successfully.');
      } else {
        Alert.alert('Permission Denied', 'Location access is required for job matching and navigation.');
      }
    } catch (error) {
      console.error('Failed to request location permission:', error);
      Alert.alert('Error', 'Failed to request location permission.');
    }
  };

  /**
   * Request notification permission
   */
  const requestNotificationPermission = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status === 'granted') {
        setPermissions(prev => ({ ...prev, notifications: true }));
        Alert.alert('Success', 'Notification permission granted successfully.');
      } else {
        Alert.alert('Permission Denied', 'Notifications are required to receive job alerts and updates.');
      }
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      Alert.alert('Error', 'Failed to request notification permission.');
    }
  };

  /**
   * Update a specific setting
   */
  const updateSetting = async (category: keyof SettingsData, key: string, value: boolean) => {
    const newSettings = {
      ...settings,
      [category]: {
        ...settings[category],
        [key]: value,
      },
    };
    await saveSettings(newSettings);
  };

  /**
   * Reset all settings to defaults
   */
  const resetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to their default values?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => saveSettings(DEFAULT_SETTINGS)
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main || '#007AFF'} />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        {saving && <ActivityIndicator size="small" color={colors.primary.main} />}
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Permissions Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Permissions</Text>
          <Text style={styles.sectionDescription}>
            Manage app permissions for optimal functionality
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="location" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Location Access</Text>
                <Text style={styles.settingDescription}>
                  Required for job matching and navigation
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={[
                styles.permissionButton,
                permissions.location && styles.permissionButtonGranted,
              ]}
              onPress={requestLocationPermission}
              disabled={permissions.location}
            >
              <Text style={[
                styles.permissionButtonText,
                permissions.location && styles.permissionButtonTextGranted,
              ]}>
                {permissions.location ? 'Granted' : 'Grant'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="notifications" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Notifications</Text>
                <Text style={styles.settingDescription}>
                  Receive job alerts and important updates
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={[
                styles.permissionButton,
                permissions.notifications && styles.permissionButtonGranted,
              ]}
              onPress={requestNotificationPermission}
              disabled={permissions.notifications}
            >
              <Text style={[
                styles.permissionButtonText,
                permissions.notifications && styles.permissionButtonTextGranted,
              ]}>
                {permissions.notifications ? 'Granted' : 'Grant'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          <Text style={styles.sectionDescription}>
            Choose which notifications you want to receive
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="briefcase" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Job Alerts</Text>
                <Text style={styles.settingDescription}>New job opportunities near you</Text>
              </View>
            </View>
            <Switch
              value={settings.notifications.jobAlerts}
              onValueChange={(value: boolean) => updateSetting('notifications', 'jobAlerts', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.notifications.jobAlerts ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="chatbubble" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Message Alerts</Text>
                <Text style={styles.settingDescription}>New messages from customers</Text>
              </View>
            </View>
            <Switch
              value={settings.notifications.messageAlerts}
              onValueChange={(value: boolean) => updateSetting('notifications', 'messageAlerts', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.notifications.messageAlerts ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="card" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Payment Alerts</Text>
                <Text style={styles.settingDescription}>Payment confirmations and updates</Text>
              </View>
            </View>
            <Switch
              value={settings.notifications.paymentAlerts}
              onValueChange={(value: boolean) => updateSetting('notifications', 'paymentAlerts', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.notifications.paymentAlerts ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="mail" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Marketing Emails</Text>
                <Text style={styles.settingDescription}>Tips, updates, and promotions</Text>
              </View>
            </View>
            <Switch
              value={settings.notifications.marketingEmails}
              onValueChange={(value: boolean) => updateSetting('notifications', 'marketingEmails', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.notifications.marketingEmails ? colors.primary.main : colors.neutral?.main}
            />
          </View>
        </View>

        {/* Location Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>
          <Text style={styles.sectionDescription}>
            Control how your location is used and shared
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="location-outline" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Share Location</Text>
                <Text style={styles.settingDescription}>Allow customers to see your location</Text>
              </View>
            </View>
            <Switch
              value={settings.location.shareLocation}
              onValueChange={(value: boolean) => updateSetting('location', 'shareLocation', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.location.shareLocation ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="navigate" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Background Tracking</Text>
                <Text style={styles.settingDescription}>Track location when app is closed</Text>
              </View>
            </View>
            <Switch
              value={settings.location.backgroundTracking}
              onValueChange={(value: boolean) => updateSetting('location', 'backgroundTracking', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.location.backgroundTracking ? colors.primary.main : colors.neutral?.main}
            />
          </View>
        </View>

        {/* Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <Text style={styles.sectionDescription}>
            Customize your app experience
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="moon" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Dark Mode</Text>
                <Text style={styles.settingDescription}>Use dark theme for the app</Text>
              </View>
            </View>
            <Switch
              value={settings.preferences.darkMode}
              onValueChange={(value: boolean) => updateSetting('preferences', 'darkMode', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.preferences.darkMode ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="checkmark-circle" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Auto Accept Jobs</Text>
                <Text style={styles.settingDescription}>Automatically accept matching jobs</Text>
              </View>
            </View>
            <Switch
              value={settings.preferences.autoAcceptJobs}
              onValueChange={(value: boolean) => updateSetting('preferences', 'autoAcceptJobs', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.preferences.autoAcceptJobs ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="eye" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Show Earnings Publicly</Text>
                <Text style={styles.settingDescription}>Display earnings on your profile</Text>
              </View>
            </View>
            <Switch
              value={settings.preferences.showEarningsPublicly}
              onValueChange={(value: boolean) => updateSetting('preferences', 'showEarningsPublicly', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.preferences.showEarningsPublicly ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="bulb" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Job Recommendations</Text>
                <Text style={styles.settingDescription}>Receive personalized job suggestions</Text>
              </View>
            </View>
            <Switch
              value={settings.preferences.receiveJobRecommendations}
              onValueChange={(value: boolean) => updateSetting('preferences', 'receiveJobRecommendations', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.preferences.receiveJobRecommendations ? colors.primary.main : colors.neutral?.main}
            />
          </View>
        </View>

        {/* Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy</Text>
          <Text style={styles.sectionDescription}>
            Control your privacy and visibility
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="person" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Profile Visible</Text>
                <Text style={styles.settingDescription}>Show your profile to customers</Text>
              </View>
            </View>
            <Switch
              value={settings.privacy.profileVisible}
              onValueChange={(value: boolean) => updateSetting('privacy', 'profileVisible', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.privacy.profileVisible ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="call" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Show Contact Info</Text>
                <Text style={styles.settingDescription}>Display phone and email publicly</Text>
              </View>
            </View>
            <Switch
              value={settings.privacy.showContactInfo}
              onValueChange={(value: boolean) => updateSetting('privacy', 'showContactInfo', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.privacy.showContactInfo ? colors.primary.main : colors.neutral?.main}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="chatbubbles" size={24} color={colors.primary.main} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Allow Direct Messages</Text>
                <Text style={styles.settingDescription}>Let customers message you directly</Text>
              </View>
            </View>
            <Switch
              value={settings.privacy.allowDirectMessages}
              onValueChange={(value: boolean) => updateSetting('privacy', 'allowDirectMessages', value)}
              trackColor={{ false: colors.neutral?.light, true: colors.primary.light }}
              thumbColor={settings.privacy.allowDirectMessages ? colors.primary.main : colors.neutral?.main}
            />
          </View>
        </View>

        {/* Reset Settings */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.resetButton} onPress={resetSettings}>
            <Ionicons name="refresh" size={24} color={colors.error.main} />
            <Text style={styles.resetButtonText}>Reset All Settings</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
