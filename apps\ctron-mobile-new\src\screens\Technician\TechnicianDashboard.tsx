// src/screens/Technician/TechnicianDashboard.tsx
import { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useJobs } from '../../context/JobContext';
import { useAuth } from '../../context/AuthContext';
import { useNavigation } from '@react-navigation/native';

const TechnicianDashboard = () => {
  const { assignedJobs = [], refreshJobs, loading } = useJobs();
  const {  } = useAuth();
  const navigation = useNavigation<any>();
  const [buttonScale] = useState(new Animated.Value(1));



  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  const pendingJobs = assignedJobs.filter(j => j.status === 'PENDING');
  const inProgressJobs = assignedJobs.filter(j => j.status === 'IN_PROGRESS');
  const completedJobs = assignedJobs.filter(j => j.status === 'COMPLETED');

  const completedThisMonth = completedJobs.filter(job => {
    const date = new Date(job.scheduledAt);
    return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
  });

  const totalEarnings = completedThisMonth.length * 50;

  const overdueJobs = assignedJobs.filter(
    job => new Date(job.scheduledAt) < now &&
    (job.status === 'PENDING' || job.status === 'IN_PROGRESS')
  );

  const nextJob = assignedJobs
    .filter(job => job.scheduledAt)
    .sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())[0];

  const handlePressIn = () => {
    Animated.spring(buttonScale, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(buttonScale, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={<RefreshControl refreshing={loading} onRefresh={refreshJobs} />}
    >

      {/* Overdue Alert */}
      {overdueJobs.length > 0 && (
        <View style={styles.alertBox}>
          <Text style={styles.alertText}>⚠️ {overdueJobs.length} job(s) overdue! Please take action.</Text>
        </View>
      )}

      {/* Job Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statsCard}>
          <Text style={styles.statsTitle}>Earnings This Month</Text>
          <Text style={styles.statsValue}>£{totalEarnings}</Text>
        </View>

        <View style={styles.statsCard}>
          <Text style={styles.statsTitle}>Jobs Completed</Text>
          <Text style={styles.statsValue}>{completedThisMonth.length}</Text>
        </View>

        <View style={styles.statsRow}>
          <View style={styles.smallCard}>
            <Text style={styles.smallCardTitle}>Pending</Text>
            <Text style={styles.smallCardValue}>{pendingJobs.length}</Text>
          </View>
          <View style={styles.smallCard}>
            <Text style={styles.smallCardTitle}>In Progress</Text>
            <Text style={styles.smallCardValue}>{inProgressJobs.length}</Text>
          </View>
        </View>
      </View>

      {/* Next Job Section */}
      {nextJob ? (
        <View style={styles.nextJob}>
          <Text style={styles.nextJobTitle}>Next Job:</Text>
          <Text style={styles.nextJobDetails}>{nextJob.issue}</Text>
          <Text style={styles.nextJobDetails}>
            {new Date(nextJob.scheduledAt).toLocaleDateString()} at{' '}
            {new Date(nextJob.scheduledAt).toLocaleTimeString()}
          </Text>
        </View>
      ) : (
        <Text style={styles.noJobText}>No upcoming jobs scheduled.</Text>
      )}

      {/* Assigned Jobs Button */}
      <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
        <TouchableOpacity
          style={styles.button}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          onPress={() => navigation.navigate('AssignedJobs')}
        >
          <Text style={styles.buttonText}>View All Assigned Jobs</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
};

export default TechnicianDashboard;

  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: '#f4f6f8', padding: 20 },
    welcomeText: { fontSize: 20, fontWeight: 'bold', marginBottom: 10 },
    profileContainer: { alignItems: 'center', marginBottom: 10 },
    profileName: { fontSize: 20, fontWeight: 'bold' },
    profileEmail: { fontSize: 14, color: '#666' },
    alertBox: {
      backgroundColor: '#FFE4E1',
      padding: 12,
      borderRadius: 8,
      marginBottom: 16,
    },
    alertText: { color: '#B00020', fontWeight: 'bold', textAlign: 'center' },
    statsContainer: { marginBottom: 20 },
    statsCard: {
      backgroundColor: '#ffffff',
      borderRadius: 12,
      padding: 20,
      marginBottom: 12,
      alignItems: 'center',
      elevation: 3,
    },
    statsTitle: { fontSize: 16, color: '#666' },
    statsValue: { fontSize: 30, fontWeight: 'bold', marginTop: 6 },
    statsRow: { flexDirection: 'row', justifyContent: 'space-between' },
    smallCard: {
      backgroundColor: '#ffffff',
      flex: 1,
      marginHorizontal: 6,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      elevation: 3,
    },
    smallCardTitle: { fontSize: 14, color: '#666' },
    smallCardValue: { fontSize: 24, fontWeight: 'bold' },
    nextJob: {
      backgroundColor: '#ffffff',
      borderRadius: 12,
      padding: 20,
      alignItems: 'center',
      marginBottom: 20,
      elevation: 3,
    },
    nextJobTitle: { fontSize: 18, fontWeight: 'bold' },
    nextJobDetails: { fontSize: 14, marginTop: 4, color: '#666' },
    noJobText: { textAlign: 'center', color: '#999', marginVertical: 20 },
    button: {
      marginTop: 10,
      backgroundColor: '#007AFF',
      padding: 14,
      borderRadius: 12,
      alignItems: 'center',
    },
    buttonText: { color: '#ffffff', fontWeight: 'bold', fontSize: 16 },
  }), []);
