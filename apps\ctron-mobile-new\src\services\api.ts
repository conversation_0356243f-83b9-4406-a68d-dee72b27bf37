// CTRON Home - API Service
// Centralized API service for mobile app

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { API_BASE_URL, REQUEST_TIMEOUT } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';
import { debugLogger } from '../utils/debugLogger';
import { performanceMonitor } from '../utils/performanceMonitor';

class APIService {
  private static instance: APIService;
  private client: AxiosInstance;

  private constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  static getInstance(): APIService {
    if (!APIService.instance) {
      APIService.instance = new APIService();
    }
    return APIService.instance;
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token and start performance tracking
    this.client.interceptors.request.use(
      async (config) => {
        const token = await getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Start performance tracking for this request
        const requestId = `${config.method?.toUpperCase()}_${config.url}_${Date.now()}`;
        config.metadata = {
          requestId,
          networkTracker: performanceMonitor.trackNetworkRequest(
            config.url || 'unknown',
            config.method?.toUpperCase() || 'GET'
          )
        };

        return config;
      },
      (error) => {
        debugLogger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling and performance tracking
    this.client.interceptors.response.use(
      (response) => {
        // Complete performance tracking for successful requests
        const networkTracker = response.config?.metadata?.networkTracker;
        if (networkTracker) {
          const responseSize = JSON.stringify(response.data).length;
          networkTracker.end(response.status, responseSize);
        }
        return response;
      },
      (error) => {
        // Complete performance tracking for failed requests
        const networkTracker = error.config?.metadata?.networkTracker;
        if (networkTracker) {
          networkTracker.end(
            error.response?.status || 0,
            undefined,
            error.message
          );
        }

        if (__DEV__) {
          debugLogger.error('API Error:', {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            message: error.message,
            data: error.response?.data,
          });
        }

        // Transform error for consistent handling
        const transformedError = {
          ...error,
          message: error.response?.data?.message || error.message || 'Network error occurred',
          status: error.response?.status,
        };

        return Promise.reject(transformedError);
      }
    );
  }

  // Generic HTTP methods
  async get<T = any>(url: string, config?: any): Promise<AxiosResponse<T>> {
    return this.client.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return this.client.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return this.client.put(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return this.client.patch(url, data, config);
  }

  async delete<T = any>(url: string, config?: any): Promise<AxiosResponse<T>> {
    return this.client.delete(url, config);
  }

  // Admin API methods
  async getAdminDashboard(): Promise<AxiosResponse<any>> {
    return this.get('/api/admin/dashboard');
  }

  async getAdminJobs(params?: any): Promise<AxiosResponse<any>> {
    return this.get('/api/admin/jobs', { params });
  }

  async getAdminJobDetails(jobId: string): Promise<AxiosResponse<any>> {
    return this.get(`/api/admin/jobs/${jobId}`);
  }

  async updateAdminJob(jobId: string, data: any): Promise<AxiosResponse<any>> {
    return this.put(`/api/admin/jobs/${jobId}`, data);
  }

  async getAdminTechnicians(params?: any): Promise<AxiosResponse<any>> {
    return this.get('/api/admin/technicians', { params });
  }

  async updateAdminTechnician(technicianId: string, data: any): Promise<AxiosResponse<any>> {
    return this.put(`/api/admin/technicians/${technicianId}`, data);
  }

  async getAdminSettings(): Promise<AxiosResponse<any>> {
    return this.get('/api/admin/settings');
  }

  async updateAdminSettings(data: any): Promise<AxiosResponse<any>> {
    return this.put('/api/admin/settings', data);
  }

  // Notification API methods
  async getNotifications(params?: any): Promise<AxiosResponse<any>> {
    return this.get('/api/notifications', { params });
  }

  async markNotificationRead(notificationId: string): Promise<AxiosResponse<any>> {
    return this.patch(`/api/notifications/${notificationId}/read`);
  }

  async markAllNotificationsRead(): Promise<AxiosResponse<any>> {
    return this.patch('/api/notifications/read-all');
  }

  async deleteNotification(notificationId: string): Promise<AxiosResponse<any>> {
    return this.delete(`/api/notifications/${notificationId}`);
  }

  // Job API methods
  async getJobs(params?: any): Promise<AxiosResponse<any>> {
    return this.get('/api/jobs', { params });
  }

  async getJobDetails(jobId: string): Promise<AxiosResponse<any>> {
    return this.get(`/api/jobs/${jobId}`);
  }

  async createJob(data: any): Promise<AxiosResponse<any>> {
    return this.post('/api/jobs', data);
  }

  async updateJob(jobId: string, data: any): Promise<AxiosResponse<any>> {
    return this.put(`/api/jobs/${jobId}`, data);
  }

  async deleteJob(jobId: string): Promise<AxiosResponse<any>> {
    return this.delete(`/api/jobs/${jobId}`);
  }

  // User API methods
  async getUserProfile(): Promise<AxiosResponse<any>> {
    return this.get('/api/users/profile');
  }

  async updateUserProfile(data: any): Promise<AxiosResponse<any>> {
    return this.put('/api/users/profile', data);
  }

  // Technician API methods
  async getTechnicians(params?: any): Promise<AxiosResponse<any>> {
    return this.get('/api/technicians', { params });
  }

  async getTechnicianDetails(technicianId: string): Promise<AxiosResponse<any>> {
    return this.get(`/api/technicians/${technicianId}`);
  }

  // Payment API methods
  async getPayments(params?: any): Promise<AxiosResponse<any>> {
    return this.get('/api/payments', { params });
  }

  async createPayment(data: any): Promise<AxiosResponse<any>> {
    return this.post('/api/payments', data);
  }

  // Health check
  async healthCheck(): Promise<AxiosResponse<any>> {
    return this.get('/api/health');
  }
}

// Export singleton instance
const api = APIService.getInstance();
export default api;

// Export class for testing
export { APIService };
