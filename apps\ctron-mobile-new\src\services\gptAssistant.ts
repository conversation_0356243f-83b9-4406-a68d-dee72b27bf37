// src/services/gptAssistant.ts

import axios from 'axios';
import { getAuthToken } from '../utils/auth.utils';
import { API_BASE_URL } from '../config/api.config';

export const GPTAssistant = {
  ask: async (prompt: string) => {
    const token = await getAuthToken();
    const response = await axios.post(`${API_BASE_URL}/api/gpt`,
      { prompt },
      {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
      }
    );
    return response.data.reply;
  },
};
