// src/services/location.service.web.ts

import { LocationObjectCoords } from 'expo-location';

export const requestForegroundPermissionsAsync = async (): Promise<{ granted: boolean }> => {
  // For web, permissions are typically handled by the browser's geolocation API directly
  // We can assume it's granted if the user allows it when prompted by the browser.
  return { granted: true };
};

export const getCurrentPositionAsync = async (): Promise<LocationObjectCoords> => {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy || 0,
            altitude: position.coords.altitude || 0,
            altitudeAccuracy: position.coords.altitudeAccuracy || 0,
            heading: position.coords.heading || 0,
            speed: position.coords.speed || 0,
          });
        },
        (error) => {
          console.error('Geolocation error:', error);
          reject(error);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 1000 }
      );
    } else {
      reject(new Error('Geolocation is not supported by this browser.'));
    }
  });
};

export const watchPositionAsync = (
  options: any,
  callback: (location: LocationObjectCoords) => void
): (() => void) => {
  if (navigator.geolocation) {
    const watchId = navigator.geolocation.watchPosition(
      (position) => {
        callback({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy || 0,
          altitude: position.coords.altitude || 0,
          altitudeAccuracy: position.coords.altitudeAccuracy || 0,
          heading: position.coords.heading || 0,
          speed: position.coords.speed || 0,
        });
      },
      (error) => {
        console.error('Geolocation watch error:', error);
      },
      options
    );
    return () => navigator.geolocation.clearWatch(watchId);
  } else {
    console.warn('Geolocation watch is not supported by this browser.');
    return () => {};
  }
};