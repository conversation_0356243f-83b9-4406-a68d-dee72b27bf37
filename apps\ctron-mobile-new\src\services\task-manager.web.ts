export function defineTask(taskName: string) {
  console.warn(`TaskManager.defineTask is not supported on web. Task: ${taskName}`);
}

export function isRegistered(taskName: string) {
  console.warn(`TaskManager.isRegistered is not supported on web. Task: ${taskName}`);
  return false;
}

export function unregisterAllTasksAsync() {
  console.warn('TaskManager.unregisterAllTasksAsync is not supported on web.');
  return Promise.resolve();
}