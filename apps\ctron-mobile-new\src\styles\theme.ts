// CTRON Home Design System - Theme Configuration
// Mobile App Theme Implementation

export interface Theme {
  colors: typeof colors;
  spacing: typeof spacing;
  typography: typeof typography;
  borderRadius: typeof borderRadius;
  shadows: typeof shadows;
  sizes: typeof sizes;
}

const colors = {
  // Primary Color Palette
  primary: {
    50: '#F0F4F8',
    100: '#D9E2EC',
    200: '#BCCCDC',
    300: '#9FB3C8',
    400: '#829AB1',
    500: '#627D98',
    600: '#486581',
    700: '#334E68',
    800: '#243B53',
    900: '#1B365D', // Primary Blue
    main: '#1B365D',
    light: '#F0F4F8',
    dark: '#243B53',
    contrast: '#FFFFFF',
  },

  // Secondary Color Palette
  secondary: {
    50: '#FFF5F0',
    100: '#FFE4D6',
    200: '#FFCAB0',
    300: '#FFAB7A',
    400: '#FF8C42', // Secondary Orange
    500: '#FF6B35', // Primary Orange
    600: '#E55A2B',
    700: '#CC4A1F',
    800: '#B33A13',
    900: '#992A07',
    main: '#FF6B35',
    light: '#FF8C42',
    dark: '#E55A2B',
    contrast: '#FFFFFF',
  },

  // Status Colors
  status: {
    pending: '#F59E0B',
    assigned: '#3B82F6',
    active: '#EF4444',
    completed: '#10B981',
    cancelled: '#6B7280',
    overdue: '#DC2626',
    error: '#EF4444',
  },

  // Semantic Colors
  error: {
    main: '#EF4444',
    light: '#FEE2E2',
    dark: '#DC2626',
  },
  warning: {
    main: '#F59E0B',
    light: '#FEF3C7',
    dark: '#D97706',
  },
  info: {
    main: '#3B82F6',
    light: '#DBEAFE',
    dark: '#2563EB',
  },
  success: {
    main: '#10B981',
    light: '#D1FAE5',
    dark: '#059669',
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
    800: '#166534',
    900: '#14532D',
    contrast: '#FFFFFF',
  },

  // Neutral Colors
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },

  neutral: {
    light: '#F3F4F6',
    main: '#6B7280',
    dark: '#374151',
  },

  // Background Colors
  background: {
    primary: '#FFFFFF',
    secondary: '#F9FAFB',
    tertiary: '#F3F4F6',
  },

  // Text Colors
  text: {
    primary: '#111827',
    secondary: '#4B5563',
    tertiary: '#6B7280',
    inverse: '#FFFFFF',
  },

  // Border Colors
  border: {
    light: '#E5E7EB',
    medium: '#D1D5DB',
    dark: '#9CA3AF',
  },

  // Additional color aliases for compatibility
  white: '#FFFFFF',
  black: '#000000',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',

  // Label colors for iOS compatibility
  label: '#000000',
  secondaryLabel: '#6B7280',

  // Error color for compatibility
  destructive: '#EF4444',

  // iOS-style colors for compatibility
  systemBackground: '#FFFFFF',
  secondarySystemBackground: '#F2F2F7',
  separator: '#C6C6C8',
  tertiaryLabel: '#8E8E93',
};



const spacing = {
  0: 0,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  8: 32,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
  24: 96,
  // Named spacing for easier use
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
  // Additional spacing aliases for compatibility
  m: 16,
};

const typography = {
  fontFamily: {
    primary: 'Inter',
    secondary: 'Source Sans Pro',
    mono: 'JetBrains Mono',
  },

  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
      bold: '700' as const,
    extrabold: '800' as const,
  },

  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },

  // Structured typography for components
  heading: {
    h1: {
      fontSize: 32,
      fontWeight: '700' as const,
      lineHeight: 1.25,
    },
    h2: {
      fontSize: 28,
      fontWeight: '600' as const,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: 24,
      fontWeight: '600' as const,
      lineHeight: 1.35,
    },
    h4: {
      fontSize: 20,
      fontWeight: '600' as const,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: 18,
      fontWeight: '500' as const,
      lineHeight: 1.45,
    },
    h6: {
      fontSize: 16,
      fontWeight: '500' as const,
      lineHeight: 1.5,
    },
  },

  body: {
    large: {
      fontSize: 18,
      fontWeight: '400' as const,
      lineHeight: 1.5,
    },
    medium: {
      fontSize: 16,
      fontWeight: '400' as const,
      lineHeight: 1.5,
    },
    small: {
      fontSize: 14,
      fontWeight: '400' as const,
      lineHeight: 1.5,
    },
  },

  // Additional typography styles for compatibility
  subheading: {
    fontSize: 18,
    fontWeight: '600' as const,
    lineHeight: 1.4,
  },

  caption: {
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 1.3,
  },

  caption2: {
    fontSize: 11,
    fontWeight: '400' as const,
    lineHeight: 1.2,
  },

  // iOS-style typography
  largeTitle: {
    fontSize: 34,
    fontWeight: '700' as const,
    lineHeight: 1.2,
  },

  title1: {
    fontSize: 28,
    fontWeight: '700' as const,
    lineHeight: 1.2,
  },

  title2: {
    fontSize: 22,
    fontWeight: '700' as const,
    lineHeight: 1.3,
  },

  title3: {
    fontSize: 20,
    fontWeight: '600' as const,
    lineHeight: 1.3,
  },

  headline: {
    fontSize: 17,
    fontWeight: '600' as const,
    lineHeight: 1.4,
  },

  callout: {
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 1.4,
  },

  calloutEmphasized: {
    fontSize: 16,
    fontWeight: '600' as const,
    lineHeight: 1.4,
  },

  footnote: {
    fontSize: 13,
    fontWeight: '400' as const,
    lineHeight: 1.3,
  },

  caption1: {
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 1.3,
  },

  // Additional typography for compatibility
  button: {
    fontSize: 16,
    fontWeight: '600' as const,
    lineHeight: 1.2,
  },

  h2: {
    fontSize: 28,
    fontWeight: '600' as const,
    lineHeight: 1.3,
  },

  // Fix for Input component
  subheadline: {
    fontSize: 18,
    fontWeight: '600' as const,
    lineHeight: 1.4,
  },
};

const borderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  full: 9999,
};

const sizes = {
  inputHeight: {
    sm: 36,
    md: 44,
    lg: 52,
  },
  buttonHeight: {
    sm: 32,
    md: 40,
    lg: 48,
  },
  iconSize: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
  },
  touchTarget: 44, // iOS recommended minimum touch target size
};

const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Theme Variants
const lightTheme: Theme = {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
  sizes,
};


const darkTheme: Theme = {
  colors: {
    ...colors,
    background: {
      primary: '#111827',
      secondary: '#1F2937',
      tertiary: '#374151',
    },
    text: {
      primary: '#F9FAFB',
      secondary: '#D1D5DB',
      tertiary: '#9CA3AF',
      inverse: '#111827',
    },
    border: {
      light: '#374151',
      medium: '#4B5563',
      dark: '#6B7280',
    },
  },
  spacing,
  typography,
  borderRadius,
  shadows,
  sizes,
};

const professionalTheme: Theme = {
  colors: {
    ...colors,
    background: {
      primary: '#F4F6F9',
      secondary: '#E8EDF5',
      tertiary: '#D1DCE8',
    },
    text: {
      primary: '#1B365D',
      secondary: '#2E5984',
      tertiary: '#4A90B8',
      inverse: '#FFFFFF',
    },
  },
  spacing,
  typography,
  borderRadius,
  shadows,
  sizes,
};

export type ThemeColors = typeof colors;
export type ThemeSpacing = typeof spacing;

export { lightTheme, darkTheme, professionalTheme };

export default lightTheme; // Default export for convenience
