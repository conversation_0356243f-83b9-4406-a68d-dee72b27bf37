declare module 'expo-constants' {
  export interface ExpoConfig {
    extra?: {
      API_BASE_URL?: string;
      STRIPE_PUBLIC_KEY?: string;
      AWS_REGION?: string;
      AWS_BUCKET_NAME?: string;
      BACKEND_IP?: string;
      BACKEND_PORT?: string;
      SOCKET_URL?: string;
      ENABLE_DEBUG?: boolean;
      EXPO_PROJECT_ID?: string;
      MERCHANT_IDENTIFIER?: string;
      API_TIMEOUT?: string;
      SOCKET_TIMEOUT?: string;
      SOCKET_RECONNECT_ATTEMPTS?: string;
      SOCKET_RECONNECT_DELAY?: string;
      eas?: {
        projectId?: string;
      };
    };
    version?: string;
  }

  interface Constants {
    expoConfig?: ExpoConfig;
    deviceId?: string;
  }

  const Constants: Constants;
  export default Constants;
}

// Global type declarations
declare global {
  const __DEV__: boolean;
  const __ANDROID_FIX_VERIFICATION__: (() => void) | undefined;
  const __fbBatchedBridgeConfig: {
    remoteModuleConfig: any[];
    localModulesConfig: any[];
  } | undefined;
  const nativeModules: {
    [key: string]: any;
  } | undefined;
  const BatchedBridge: {
    registerCallableModule: (name: string, module: any) => void;
    callFunctionReturnFlushedQueue: (module: string, method: string, args: any[]) => any;
    invokeCallbackAndReturnFlushedQueue: (cbID: number, args: any[]) => any;
    flushedQueue: () => any;
    getEventLoopRunningTime: () => number;
  } | undefined;
  const _RCTDeviceEventEmitter: {
    emit: (eventType: string, ...args: any[]) => void;
    addListener: (eventType: string, listener: (...args: any[]) => void) => void;
    removeListener: (eventType: string, listener: (...args: any[]) => void) => void;
  } | undefined;
  const nativeEventEmitter: {
    emit: (eventType: string, ...args: any[]) => void;
    addListener: (eventType: string, listener: (...args: any[]) => void) => void;
    removeListener: (eventType: string, listener: (...args: any[]) => void) => void;
  } | undefined;
}
