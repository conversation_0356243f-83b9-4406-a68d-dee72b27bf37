// Type declarations for global properties added by React Native Web bridge setup

declare global {
  interface Window {
    __fbBatchedBridgeConfig: any;
    __fbBatchedBridge: any;
    NativeModules: any;
    installConsoleErrorReporter: any;
    nativeModuleProxy: any;
    RCTDeviceEventEmitter: any;
    UIManager: any;
    __DEV__: boolean;
    nativePerformanceNow: () => number;
    ErrorUtils: any;
    Systrace: any;
    HMRClient: any;
    __REACT_NATIVE_WEB_INITIALIZED__: boolean;
    __webErrorHandler: any;
    XMLHttpRequest: any;
    __REACT_DEVTOOLS_GLOBAL_HOOK__: any;
    PlatformColorValueTypes: any;
    isPlatformColor: any;
    platformColorToWebColor: any;
    BatchedBridge: any;
    ReactNativeViewConfigRegistry: any;
    TextInputState: any;
    ReactNativePrivateInterface: any;
  }

  namespace NodeJS {
    interface Global {
      gc: () => void;
      legacySendAccessibilityEvent: any;
      __REACT_NATIVE_VIEW_CONFIG_REGISTRY__: Map<string, any>;
      nativeFabricUIManager: any;
      RCTEventEmitter: any;
      ReactNativeViewConfigRegistry: any;
      ReactNativeRenderer: any;
      ReactFabric: any;
      RN$Bridgeless: boolean;
      __fbBatchedBridgeConfig: any;
      __fbBatchedBridge: any;
      NativeModules: any;
      installConsoleErrorReporter: any;
      __REACT_NATIVE_WEB_INITIALIZED__: boolean;
      __webErrorHandler: any;
      __consoleErrorReporterPolyfill: any;
      BatchedBridge: any;
      UIManager: any;
      TextInputState: any;
      ReactNativePrivateInterface: any;
      PlatformColorValueTypes: any;
      isPlatformColor: any;
      platformColorToWebColor: any;
    }
  }
}