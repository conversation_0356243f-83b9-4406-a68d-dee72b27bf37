// src/types/job.ts

// src/types/job.ts

export type JobStatus =
  | 'PENDING'
  | 'ACCEPTED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';

export interface Job {
  id: string;
  userId: string;
  technicianId?: string;
  title: string; // Added missing title property
  serviceType: string; // Added missing serviceType property
  issue: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  photoUrl?: string;
  status: JobStatus;
  scheduledAt: string;
  scheduledDate?: string; // Added for compatibility
  createdAt: string;
  updatedAt: string;
  latitude?: number;
  longitude?: number;
  address: string; // Made required and added address property
  price?: number; // Added price property
  technician?: {
    id: string;
    user: {
      fullName: string;
      phone?: string;
    };
    specialization: string;
    rating?: number;
  };
}
