import { Platform } from 'react-native';

// AccessibilityInfo is not available on web
const AccessibilityInfo = Platform.OS !== 'web' ? require('react-native').AccessibilityInfo : null;

export interface AccessibilityProps {
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?:
  | 'none'
  | 'button'
  | 'link'
  | 'search'
  | 'image'
  | 'keyboardkey'
  | 'text'
  | 'adjustable'
  | 'imagebutton'
  | 'header'
  | 'summary'
  | 'alert'
  | 'checkbox'
  | 'combobox'
  | 'menu'
  | 'menubar'
  | 'menuitem'
  | 'progressbar'
  | 'radio'
  | 'radiogroup'
  | 'scrollbar'
  | 'spinbutton'
  | 'switch'
  | 'tab'
  | 'tablist'
  | 'timer'
  | 'toolbar';
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean | 'mixed';
    busy?: boolean;
    expanded?: boolean;
  };
  accessibilityValue?: {
    min?: number;
    max?: number;
    now?: number;
    text?: string;
  };
  accessibilityActions?: Array<{
    name: string;
    label?: string;
  }>;
  onAccessibilityAction?: (event: { nativeEvent: { actionName: string } }) => void;
  testID?: string;
}

/**
 * Enhanced accessibility utilities for CTRON Home
 */
class AccessibilityManager {
  private static instance: AccessibilityManager;
  private isScreenReaderEnabled = false;
  private isReduceMotionEnabled = false;
  private announceQueue: string[] = [];
  private isAnnouncing = false;

  static getInstance(): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager();
    }
    return AccessibilityManager.instance;
  }

  constructor() {
    this.initializeAccessibilityInfo();
  }

  private async initializeAccessibilityInfo(): Promise<void> {
    try {
      if (Platform.OS !== 'web') {
        this.isScreenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();
        this.isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();

        // Listen for changes
        AccessibilityInfo.addEventListener('screenReaderChanged', this.handleScreenReaderChange);
        AccessibilityInfo.addEventListener('reduceMotionChanged', this.handleReduceMotionChange);
      } else {
        // Web accessibility detection
        this.detectWebAccessibilityFeatures();
      }
    } catch (error) {
      console.warn('Failed to initialize accessibility info:', error);
    }
  }

  private detectWebAccessibilityFeatures(): void {
    if (typeof window !== 'undefined') {
      // Check for reduced motion preference
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      this.isReduceMotionEnabled = mediaQuery.matches;

      mediaQuery.addEventListener('change', (e) => {
        this.isReduceMotionEnabled = e.matches;
      });

      // Basic screen reader detection (not foolproof)
      this.isScreenReaderEnabled = window.navigator.userAgent.includes('NVDA') ||
        window.navigator.userAgent.includes('JAWS') ||
        window.speechSynthesis !== undefined;
    }
  }

  private handleScreenReaderChange = (enabled: boolean): void => {
    this.isScreenReaderEnabled = enabled;
    if (__DEV__) {
      console.log('Screen reader status changed:', enabled);
    }
  };

  private handleReduceMotionChange = (enabled: boolean): void => {
    this.isReduceMotionEnabled = enabled;
    if (__DEV__) {
      console.log('Reduce motion status changed:', enabled);
    }
  };

  /**
   * Check if screen reader is enabled
   */
  getScreenReaderEnabled(): boolean {
    return this.isScreenReaderEnabled;
  }

  /**
   * Check if reduce motion is enabled
   */
  getReduceMotionEnabled(): boolean {
    return this.isReduceMotionEnabled;
  }

  /**
   * Announce text to screen readers with queue management
   */
  async announce(text: string, priority: 'low' | 'medium' | 'high' = 'medium'): Promise<void> {
    if (!text.trim()) return;

    if (priority === 'high') {
      // Clear queue for high priority announcements
      this.announceQueue = [text];
    } else {
      this.announceQueue.push(text);
    }

    if (!this.isAnnouncing) {
      await this.processAnnounceQueue();
    }
  }

  private async processAnnounceQueue(): Promise<void> {
    this.isAnnouncing = true;

    while (this.announceQueue.length > 0) {
      const text = this.announceQueue.shift();
      if (text) {
        try {
          if (Platform.OS !== 'web') {
            AccessibilityInfo.announceForAccessibility(text);
          } else {
            // Web announcement using ARIA live regions
            this.announceForWeb(text);
          }

          // Wait a bit between announcements
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          console.warn('Failed to announce text:', error);
        }
      }
    }

    this.isAnnouncing = false;
  }

  private announceForWeb(text: string): void {
    if (typeof document === 'undefined') return;

    // Create or get existing live region
    let liveRegion = document.getElementById('accessibility-live-region');
    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.id = 'accessibility-live-region';
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.style.position = 'absolute';
      liveRegion.style.left = '-10000px';
      liveRegion.style.width = '1px';
      liveRegion.style.height = '1px';
      liveRegion.style.overflow = 'hidden';
      document.body.appendChild(liveRegion);
    }

    // Clear and set new text
    liveRegion.textContent = '';
    setTimeout(() => {
      liveRegion!.textContent = text;
    }, 100);
  }

  /**
   * Generate comprehensive accessibility props for components
   */
  generateAccessibilityProps(config: {
    label: string;
    hint?: string;
    role?: AccessibilityProps['accessibilityRole'];
    state?: AccessibilityProps['accessibilityState'];
    value?: AccessibilityProps['accessibilityValue'];
    actions?: AccessibilityProps['accessibilityActions'];
    testID?: string;
  }): AccessibilityProps {
    const { label, hint, role, state, value, actions, testID } = config;

    return {
      accessible: true,
      accessibilityLabel: label,
      accessibilityHint: hint,
      accessibilityRole: role || 'button',
      accessibilityState: state,
      accessibilityValue: value,
      accessibilityActions: actions,
      testID: testID,
    };
  }

  /**
   * Create accessibility props for form inputs
   */
  createInputAccessibilityProps(config: {
    label: string;
    value?: string;
    placeholder?: string;
    error?: string;
    required?: boolean;
    testID?: string;
  }): AccessibilityProps {
    const { label, value, placeholder, error, required, testID } = config;

    let hint = placeholder || '';
    if (required) {
      hint += hint ? ', Required field' : 'Required field';
    }
    if (error) {
      hint += hint ? `, Error: ${error}` : `Error: ${error}`;
    }

    return this.generateAccessibilityProps({
      label,
      hint,
      role: 'none', // Let the TextInput handle its own role
      state: {
        disabled: false,
        busy: false,
      },
      value: value ? { text: value } : undefined,
      testID,
    });
  }

  /**
   * Create accessibility props for buttons
   */
  createButtonAccessibilityProps(config: {
    label: string;
    hint?: string;
    disabled?: boolean;
    loading?: boolean;
    pressed?: boolean;
    testID?: string;
  }): AccessibilityProps {
    const { label, hint, disabled, loading, pressed, testID } = config;

    let finalHint = hint || '';
    if (loading) {
      finalHint = 'Loading, please wait';
    } else if (disabled) {
      finalHint += finalHint ? ', Currently disabled' : 'Currently disabled';
    }

    return this.generateAccessibilityProps({
      label,
      hint: finalHint,
      role: 'button',
      state: {
        disabled: disabled || loading,
        busy: loading,
        selected: pressed,
      },
      testID,
    });
  }

  /**
   * Create accessibility props for cards/list items
   */
  createCardAccessibilityProps(config: {
    title: string;
    subtitle?: string;
    description?: string;
    actionHint?: string;
    testID?: string;
  }): AccessibilityProps {
    const { title, subtitle, description, actionHint, testID } = config;

    let label = title;
    if (subtitle) label += `, ${subtitle}`;
    if (description) label += `, ${description}`;

    return this.generateAccessibilityProps({
      label,
      hint: actionHint || 'Double tap to open',
      role: 'button',
      testID,
    });
  }

  /**
   * Cleanup accessibility listeners
   */
  cleanup(): void {
    if (Platform.OS !== 'web') {
      AccessibilityInfo.removeEventListener('screenReaderChanged', this.handleScreenReaderChange);
      AccessibilityInfo.removeEventListener('reduceMotionChanged', this.handleReduceMotionChange);
    }
  }
}

// Export singleton instance
export const accessibilityManager = AccessibilityManager.getInstance();

// Export convenience functions
export const announce = (text: string, priority?: 'low' | 'medium' | 'high') =>
  accessibilityManager.announce(text, priority);

export const isScreenReaderEnabled = () =>
  accessibilityManager.getScreenReaderEnabled();

export const isReduceMotionEnabled = () =>
  accessibilityManager.getReduceMotionEnabled();

export const createAccessibilityProps = (config: Parameters<typeof accessibilityManager.generateAccessibilityProps>[0]) =>
  accessibilityManager.generateAccessibilityProps(config);

export const createInputA11yProps = (config: Parameters<typeof accessibilityManager.createInputAccessibilityProps>[0]) =>
  accessibilityManager.createInputAccessibilityProps(config);

export const createButtonA11yProps = (config: Parameters<typeof accessibilityManager.createButtonAccessibilityProps>[0]) =>
  accessibilityManager.createButtonAccessibilityProps(config);

export const createCardA11yProps = (config: Parameters<typeof accessibilityManager.createCardAccessibilityProps>[0]) =>
  accessibilityManager.createCardAccessibilityProps(config);

export default accessibilityManager;
