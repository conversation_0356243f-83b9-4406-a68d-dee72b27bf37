// CTRON Home - Debug Logger Utility
// Comprehensive debug logging for development

import { API_BASE_URL, SOCKET_URL, APP_CONFIG } from '../config';

export class DebugLogger {
  private static instance: DebugLogger;
  private logs: { timestamp: Date; level: string; message: string; data?: any }[] = [];

  static getInstance(): DebugLogger {
    if (!DebugLogger.instance) {
      DebugLogger.instance = new DebugLogger();
    }
    return DebugLogger.instance;
  }

  private log(level: string, message: string, data?: any) {
    if (__DEV__) {
      const timestamp = new Date();
      const logEntry = { timestamp, level, message, data };
      this.logs.push(logEntry);

      // Keep only last 100 logs
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(-100);
      }

      // Console output with emojis
      const emoji = {
        INFO: '🔍',
        SUCCESS: '✅',
        ERROR: '❌',
        WARNING: '⚠️',
        NETWORK: '🌐',
        API: '📡',
        AUTH: '🔐',
        CONFIG: '🔧'
      }[level] || '📝';

      console.log(`${emoji} [${level}] ${message}`, data ? data : '');
    }
  }

  info(message: string, data?: any) {
    this.log('INFO', message, data);
  }

  success(message: string, data?: any) {
    this.log('SUCCESS', message, data);
  }

  error(message: string, data?: any) {
    this.log('ERROR', message, data);
  }

  warning(message: string, data?: any) {
    this.log('WARNING', message, data);
  }

  warn(message: string, data?: any) {
    this.log('WARNING', message, data);
  }

  network(message: string, data?: any) {
    this.log('NETWORK', message, data);
  }

  api(message: string, data?: any) {
    this.log('API', message, data);
  }

  auth(message: string, data?: any) {
    this.log('AUTH', message, data);
  }

  config(message: string, data?: any) {
    this.log('CONFIG', message, data);
  }

  // Get all logs
  getLogs() {
    return this.logs;
  }

  // Clear logs
  clearLogs() {
    this.logs = [];
  }

  // Log current configuration
  logConfiguration() {
    if (__DEV__) {
      this.config('Current Configuration:', {
        API_BASE_URL,
        SOCKET_URL,
        APP_CONFIG,
        environment: process.env.NODE_ENV,
        isDev: __DEV__
      });
    }
  }

  // Test network connectivity
  async testConnectivity() {
    if (!__DEV__) return;

    this.network('Testing network connectivity...');

    try {
      // Test basic connectivity to backend with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${API_BASE_URL}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.status === 200) {
        this.success('Backend health check passed');
      } else if (response.status === 401) {
        this.success('Backend is accessible (401 Unauthorized expected)');
      } else {
        this.success(`Backend responded with status: ${response.status}`);
      }

      return true;
    } catch (error: any) {
      this.error('Network connectivity test failed:', {
        message: error.message,
        code: error.code,
        url: `${API_BASE_URL}/api/health`
      });
      return false;
    }
  }

  // Log environment variables
  logEnvironmentVariables() {
    if (__DEV__) {
      const envVars = Object.keys(process.env)
        .filter(key => key.startsWith('EXPO_PUBLIC'))
        .reduce((acc, key) => {
          acc[key] = process.env[key];
          return acc;
        }, {} as Record<string, any>);

      this.config('Environment Variables:', envVars);
    }
  }
}

// Export singleton instance
export const debugLogger = DebugLogger.getInstance();

// Auto-log configuration on import in development
if (__DEV__) {
  debugLogger.logConfiguration();
  debugLogger.logEnvironmentVariables();
}
