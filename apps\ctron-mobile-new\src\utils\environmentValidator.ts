// src/utils/environmentValidator.ts

import { Alert } from 'react-native';

interface EnvironmentConfig {
  EXPO_PUBLIC_API_URL?: string;
  EXPO_PUBLIC_API_BASE_URL?: string;
  EXPO_PUBLIC_SOCKET_URL?: string;
  EXPO_PUBLIC_STRIPE_PUBLIC_KEY?: string;
  NODE_ENV?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates that all required environment variables are present and properly formatted
 */
export const validateEnvironmentConfig = (): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const config: EnvironmentConfig = {
    EXPO_PUBLIC_API_URL: process.env.EXPO_PUBLIC_API_URL,
    EXPO_PUBLIC_API_BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL,
    EXPO_PUBLIC_SOCKET_URL: process.env.EXPO_PUBLIC_SOCKET_URL,
    EXPO_PUBLIC_STRIPE_PUBLIC_KEY: process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY,
    NODE_ENV: process.env.NODE_ENV,
  };

  // Required environment variables
  const requiredVars = [
    'EXPO_PUBLIC_API_URL',
    'EXPO_PUBLIC_API_BASE_URL',
    'EXPO_PUBLIC_SOCKET_URL',
  ];

  // Check for missing required variables
  requiredVars.forEach(varName => {
    const value = config[varName as keyof EnvironmentConfig];
    if (!value || value.trim() === '') {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  });

  // Validate URL formats
  const urlVars = [
    'EXPO_PUBLIC_API_URL',
    'EXPO_PUBLIC_API_BASE_URL',
    'EXPO_PUBLIC_SOCKET_URL',
  ];

  urlVars.forEach(varName => {
    const value = config[varName as keyof EnvironmentConfig];
    if (value && !isValidUrl(value)) {
      errors.push(`Invalid URL format for ${varName}: ${value}`);
    }
  });

  // Check for development-specific issues
  if (config.NODE_ENV === 'development') {
    // Check for localhost URLs that might not work on physical devices
    urlVars.forEach(varName => {
      const value = config[varName as keyof EnvironmentConfig];
      if (value && value.includes('localhost')) {
        warnings.push(
          `${varName} uses localhost - this may not work on physical devices. Consider using your IP address instead.`
        );
      }
    });
  }

  // Check for Stripe configuration
  if (!config.EXPO_PUBLIC_STRIPE_PUBLIC_KEY) {
    warnings.push('EXPO_PUBLIC_STRIPE_PUBLIC_KEY is not set - payment functionality will not work');
  } else if (config.EXPO_PUBLIC_STRIPE_PUBLIC_KEY.includes('your_stripe_public_key_here')) {
    warnings.push('EXPO_PUBLIC_STRIPE_PUBLIC_KEY appears to be a placeholder - update with real Stripe key');
  }

  // Check for consistent base URLs
  if (config.EXPO_PUBLIC_API_URL && config.EXPO_PUBLIC_API_BASE_URL) {
    const apiUrl = config.EXPO_PUBLIC_API_URL.replace('/api', '');
    if (apiUrl !== config.EXPO_PUBLIC_API_BASE_URL) {
      warnings.push('API_URL and API_BASE_URL appear to be inconsistent');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Validates a URL string
 */
const isValidUrl = (urlString: string): boolean => {
    // Regex to validate http/https URLs, including those with IP addresses or hostnames and ports
    const urlRegex = /^(https?:\/\/)?([a-zA-Z0-9.-]+|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(:\d+)?(\/\S*)?$/;
    return urlRegex.test(urlString);
  };

/**
 * Displays environment validation results to the user
 */
export const displayValidationResults = (result: ValidationResult): void => {
  if (!result.isValid) {
    const errorMessage = `Environment Configuration Errors:\n\n${result.errors.join('\n')}`;
    
    if (__DEV__) {
      console.error('🚨 Environment Validation Failed:', result.errors);
      Alert.alert(
        'Configuration Error',
        errorMessage + '\n\nPlease check your environment variables.',
        [{ text: 'OK' }]
      );
    } else {
      // In production, log errors but don't show detailed error messages to users
      console.error('Environment validation failed:', result.errors);
      Alert.alert(
        'Configuration Error',
        'The app is not properly configured. Please contact support.',
        [{ text: 'OK' }]
      );
    }
  }

  if (result.warnings.length > 0 && __DEV__) {
    console.warn('⚠️ Environment Validation Warnings:', result.warnings);
    
    const warningMessage = `Environment Configuration Warnings:\n\n${result.warnings.join('\n')}`;
    Alert.alert(
      'Configuration Warnings',
      warningMessage,
      [{ text: 'OK' }]
    );
  }
};

/**
 * Performs environment validation and displays results
 * Should be called during app initialization
 */
export const validateAndDisplayEnvironment = (): boolean => {
  const result = validateEnvironmentConfig();
  
  if (__DEV__) {
    console.log('🔍 Environment Validation Results:', {
      isValid: result.isValid,
      errorCount: result.errors.length,
      warningCount: result.warnings.length,
    });
  }

  displayValidationResults(result);
  
  return result.isValid;
};

/**
 * Gets environment configuration with fallbacks
 */
export const getEnvironmentConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development' || __DEV__;

  const config = {
    apiUrl: process.env.EXPO_PUBLIC_API_URL || (isDevelopment ? 'http://localhost:3001/api' : ''),
    apiBaseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || (isDevelopment ? 'http://localhost:3001' : ''),
    socketUrl: process.env.EXPO_PUBLIC_SOCKET_URL || (isDevelopment ? 'http://localhost:3001' : ''),
    stripePublicKey: process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY || '',
    nodeEnv: process.env.NODE_ENV || 'development',
    isDevelopment,
  };

  if (__DEV__) {
    console.log('🔧 Environment Configuration:', {
      ...config,
      stripePublicKey: config.stripePublicKey ? '[SET]' : '[NOT SET]',
    });
  }

  return config;
};

export default {
  validateEnvironmentConfig,
  validateAndDisplayEnvironment,
  displayValidationResults,
  getEnvironmentConfig,
};
