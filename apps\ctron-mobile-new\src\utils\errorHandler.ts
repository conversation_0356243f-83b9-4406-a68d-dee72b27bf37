// CTRON Home - Error Handler Utility
// Centralized error handling for the mobile app

import { Alert } from 'react-native';
import Toast from 'react-native-toast-message';
import { debugLogger } from './debugLogger';

export interface AppError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
  timestamp: Date;
}

export class ErrorHandler {
  private static instance: ErrorHandler;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle API errors with user-friendly messages
   */
  handleApiError(error: any, context?: string): AppError {
    const appError: AppError = {
      message: this.getErrorMessage(error),
      code: error.code || error.response?.status?.toString(),
      status: error.response?.status,
      details: error.response?.data,
      timestamp: new Date(),
    };

    // Log error for debugging
    debugLogger.error(`API Error${context ? ` in ${context}` : ''}:`, appError);

    // Show user-friendly message
    this.showErrorToast(appError.message);

    return appError;
  }

  /**
   * Handle network errors
   */
  handleNetworkError(error: any, context?: string): AppError {
    const appError: AppError = {
      message: 'Network connection failed. Please check your internet connection.',
      code: 'NETWORK_ERROR',
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Network Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorToast(appError.message);

    return appError;
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error: any, context?: string): AppError {
    const appError: AppError = {
      message: 'Authentication failed. Please login again.',
      code: 'AUTH_ERROR',
      status: 401,
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Auth Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorAlert('Authentication Error', appError.message);

    return appError;
  }

  /**
   * Handle validation errors
   */
  handleValidationError(error: any, context?: string): AppError {
    const appError: AppError = {
      message: this.getValidationMessage(error),
      code: 'VALIDATION_ERROR',
      status: 400,
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Validation Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorToast(appError.message);

    return appError;
  }

  /**
   * Handle unexpected errors
   */
  handleUnexpectedError(error: any, context?: string): AppError {
    const appError: AppError = {
      message: 'An unexpected error occurred. Please try again.',
      code: 'UNEXPECTED_ERROR',
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Unexpected Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorAlert('Error', appError.message);

    return appError;
  }

  /**
   * Extract user-friendly error message
   */
  private getErrorMessage(error: any): string {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    if (error.message) {
      return error.message;
    }

    if (error.response?.status) {
      switch (error.response.status) {
        case 400:
          return 'Invalid request. Please check your input.';
        case 401:
          return 'Authentication required. Please login.';
        case 403:
          return 'Access denied. You do not have permission.';
        case 404:
          return 'Resource not found.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return 'An error occurred. Please try again.';
      }
    }

    return 'An unexpected error occurred.';
  }

  /**
   * Extract validation error message
   */
  private getValidationMessage(error: any): string {
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors;
      if (Array.isArray(errors) && errors.length > 0) {
        return errors[0].message || errors[0];
      }
    }

    return this.getErrorMessage(error);
  }

  /**
   * Show error toast
   */
  private showErrorToast(message: string): void {
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: message,
      position: 'bottom',
      visibilityTime: 4000,
    });
  }

  /**
   * Show error alert
   */
  private showErrorAlert(title: string, message: string): void {
    Alert.alert(
      title,
      message,
      [{ text: 'OK', style: 'default' }],

    );
  }

  /**
   * Determine error type and handle accordingly
   */
  handleError(error: any, context?: string): AppError {
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
      return this.handleNetworkError(error, context);
    }

    if (error.response?.status === 401) {
      return this.handleAuthError(error, context);
    }

    if (error.response?.status === 400) {
      return this.handleValidationError(error, context);
    }

    if (error.response?.status >= 400 && error.response?.status < 500) {
      return this.handleApiError(error, context);
    }

    return this.handleUnexpectedError(error, context);
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Export convenience functions
export const handleApiError = (error: any, context?: string) => errorHandler.handleApiError(error, context);
export const handleNetworkError = (error: any, context?: string) => errorHandler.handleNetworkError(error, context);
export const handleAuthError = (error: any, context?: string) => errorHandler.handleAuthError(error, context);
export const handleValidationError = (error: any, context?: string) => errorHandler.handleValidationError(error, context);
export const handleUnexpectedError = (error: any, context?: string) => errorHandler.handleUnexpectedError(error, context);
export const handleError = (error: any, context?: string) => errorHandler.handleError(error, context);
