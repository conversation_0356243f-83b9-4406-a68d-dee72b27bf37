// Global Error Handler - Based on research findings
import { Alert } from 'react-native';

// Global error handler for unhandled promise rejections
export const setupGlobalErrorHandling = () => {
  // Handle unhandled promise rejections
  const originalHandler = global.Promise.prototype.catch;
  global.Promise.prototype.catch = function(onRejected) {
    return originalHandler.call(this, (error) => {
      console.error('🚨 Unhandled Promise Rejection:', error);
      if (__DEV__) {
        Alert.alert(
          'Unhandled Promise Rejection',
          error?.message || 'Unknown error',
          [{ text: 'OK' }]
        );
      }
      if (onRejected) {
        return onRejected(error);
      }
      throw error;
    });
  };

  // Handle global errors
  const originalConsoleError = console.error;
  console.error = (...args) => {
    originalConsoleError(...args);
    
    // Check if this is a React error
    const errorMessage = args.join(' ');
    if (errorMessage.includes('React') || errorMessage.includes('Component')) {
      console.log('🚨 React Error Detected:', errorMessage);
      if (__DEV__) {
        // Don't show alert for every console.error, just log it
        console.log('🔍 This might be causing the blank screen');
      }
    }
  };

  // Handle JavaScript errors
  if (typeof ErrorUtils !== 'undefined') {
    const originalGlobalHandler = ErrorUtils.getGlobalHandler();
    ErrorUtils.setGlobalHandler((error, isFatal) => {
      console.error('🚨 Global JavaScript Error:', error, 'Fatal:', isFatal);
      
      if (__DEV__) {
        Alert.alert(
          'JavaScript Error',
          `${error.message}\n\nFatal: ${isFatal}`,
          [{ text: 'OK' }]
        );
      }
      
      // Call original handler
      if (originalGlobalHandler) {
        originalGlobalHandler(error, isFatal);
      }
    });
  }

  console.log('✅ Global error handling setup complete');
};

// Function to test error handling
export const testErrorHandling = () => {
  if (__DEV__) {
    console.log('🧪 Testing error handling...');
    
    // Test 1: Promise rejection
    setTimeout(() => {
      Promise.reject(new Error('Test promise rejection'));
    }, 1000);
    
    // Test 2: JavaScript error
    setTimeout(() => {
      try {
        throw new Error('Test JavaScript error');
      } catch (e) {
        console.error('Test error:', e);
      }
    }, 2000);
  }
};
