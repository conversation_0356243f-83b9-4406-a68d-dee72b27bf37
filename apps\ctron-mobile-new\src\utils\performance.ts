// CTRON Home - Performance Optimization Utilities
// Tools for improving app performance and memory management

import React, { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { Platform } from 'react-native';

/**
 * Debounce hook for performance optimization
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Throttle hook for performance optimization
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  return useCallback(
    (...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    },
    [callback, delay]
  );
}

/**
 * Memoized callback with dependency optimization
 */
export function useOptimizedCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  return useCallback(callback, [...deps, callback]);
}

/**
 * Memoized value with dependency optimization
 */
export function useOptimizedMemo<T>(
  factory: () => T,
  deps: React.DependencyList
): T {
  return useMemo(factory, [...deps, factory]);
}

/**
 * Hook for running expensive operations after interactions
 */
export function useAfterInteractions(callback: () => void, deps: React.DependencyList): void {
  useEffect(() => {
    // Use setTimeout as alternative to InteractionManager for web compatibility
    const timeoutId = setTimeout(() => {
      callback();
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [callback, deps]);
}

/**
 * Hook for cleanup on unmount
 */
export function useCleanup(cleanup: () => void): void {
  useEffect(() => {
    return cleanup;
  }, [cleanup]);
}

/**
 * Performance monitoring utility
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private timers: Map<string, number> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing an operation
   */
  startTimer(label: string): void {
    if (__DEV__) {
      this.timers.set(label, Date.now());
    }
  }

  /**
   * End timing and log result
   */
  endTimer(label: string): number {
    if (__DEV__) {
      const startTime = this.timers.get(label);
      if (startTime) {
        const duration = Date.now() - startTime;
        console.log(`⏱️ Performance: ${label} took ${duration}ms`);
        this.timers.delete(label);
        return duration;
      }
    }
    return 0;
  }

  /**
   * Measure function execution time
   */
  measure<T>(label: string, fn: () => T): T {
    this.startTimer(label);
    const result = fn();
    this.endTimer(label);
    return result;
  }

  /**
   * Measure async function execution time
   */
  async measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(label);
    const result = await fn();
    this.endTimer(label);
    return result;
  }
}

/**
 * Memory management utilities
 */
export class MemoryManager {
  private static instance: MemoryManager;
  private cleanupTasks: Set<() => void> = new Set();

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  /**
   * Register cleanup task
   */
  registerCleanup(task: () => void): void {
    this.cleanupTasks.add(task);
  }

  /**
   * Unregister cleanup task
   */
  unregisterCleanup(task: () => void): void {
    this.cleanupTasks.delete(task);
  }

  /**
   * Run all cleanup tasks
   */
  cleanup(): void {
    this.cleanupTasks.forEach(task => {
      try {
        task();
      } catch (error) {
        if (__DEV__) {
          console.error('Cleanup task failed:', error);
        }
      }
    });
    this.cleanupTasks.clear();
  }

  /**
   * Force garbage collection (development only)
   */
  forceGC(): void {
    if (__DEV__ && global.gc) {
      global.gc();
      console.log('🗑️ Forced garbage collection');
    }
  }
}

/**
 * Image optimization utilities
 */
export const ImageOptimizer = {
  /**
   * Get optimized image dimensions
   */
  getOptimizedDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    const aspectRatio = originalWidth / originalHeight;

    let width = originalWidth;
    let height = originalHeight;

    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return { width: Math.round(width), height: Math.round(height) };
  },

  /**
   * Get image quality based on device capabilities
   */
  getOptimalQuality(): number {
    if (Platform.OS === 'ios') {
      return 0.8; // iOS handles compression well
    }
    return 0.7; // Android needs more aggressive compression
  },
};

// Export singleton instances
export const performanceMonitor = PerformanceMonitor.getInstance();
export const memoryManager = MemoryManager.getInstance();

// Export convenience functions
export const startTimer = (label: string) => performanceMonitor.startTimer(label);
export const endTimer = (label: string) => performanceMonitor.endTimer(label);
export const measure = <T>(label: string, fn: () => T) => performanceMonitor.measure(label, fn);
export const measureAsync = <T>(label: string, fn: () => Promise<T>) => performanceMonitor.measureAsync(label, fn);
