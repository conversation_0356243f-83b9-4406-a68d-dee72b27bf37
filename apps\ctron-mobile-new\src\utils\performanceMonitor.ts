import { Platform } from 'react-native';

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface MemoryInfo {
  usedJSHeapSize?: number;
  totalJSHeapSize?: number;
  jsHeapSizeLimit?: number;
  timestamp: number;
}

interface NetworkMetric {
  url: string;
  method: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  status?: number;
  size?: number;
  error?: string;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetric> = new Map();
  private memorySnapshots: MemoryInfo[] = [];
  private networkMetrics: NetworkMetric[] = [];
  private isEnabled: boolean = __DEV__;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Enable or disable performance monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Start measuring a performance metric
   */
  startMeasure(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      startTime: this.getCurrentTime(),
      metadata,
    };

    this.metrics.set(name, metric);
    
    if (__DEV__) {
      console.log(`🚀 Performance: Started measuring "${name}"`);
    }
  }

  /**
   * End measuring a performance metric
   */
  endMeasure(name: string): PerformanceMetric | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`⚠️ Performance: No metric found for "${name}"`);
      return null;
    }

    metric.endTime = this.getCurrentTime();
    metric.duration = metric.endTime - metric.startTime;

    if (__DEV__) {
      console.log(`✅ Performance: "${name}" took ${metric.duration.toFixed(2)}ms`);
    }

    // Store completed metric for analysis
    this.logMetric(metric);

    // Clean up active metric
    this.metrics.delete(name);

    return metric;
  }

  /**
   * Measure a function execution time
   */
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    this.startMeasure(name, metadata);
    try {
      const result = await fn();
      this.endMeasure(name);
      return result;
    } catch (error) {
      this.endMeasure(name);
      throw error;
    }
  }

  /**
   * Measure a synchronous function execution time
   */
  measureSync<T>(
    name: string,
    fn: () => T,
    metadata?: Record<string, any>
  ): T {
    this.startMeasure(name, metadata);
    try {
      const result = fn();
      this.endMeasure(name);
      return result;
    } catch (error) {
      this.endMeasure(name);
      throw error;
    }
  }

  /**
   * Take a memory snapshot
   */
  takeMemorySnapshot(): MemoryInfo | null {
    if (!this.isEnabled) return null;

    let memoryInfo: MemoryInfo = {
      timestamp: Date.now(),
    };

    // Web platform memory info
    if (Platform.OS === 'web' && 'memory' in performance) {
      const memory = (performance as any).memory;
      memoryInfo = {
        ...memoryInfo,
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      };
    }

    this.memorySnapshots.push(memoryInfo);

    // Keep only last 100 snapshots
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots = this.memorySnapshots.slice(-100);
    }

    if (__DEV__) {
      console.log('📊 Memory snapshot taken:', memoryInfo);
    }

    return memoryInfo;
  }

  /**
   * Track network request performance
   */
  trackNetworkRequest(
    url: string,
    method: string,
    metadata?: Record<string, any>
  ): {
    end: (status?: number, size?: number, error?: string) => void;
  } {
    if (!this.isEnabled) {
      return { end: () => {} };
    }

    const metric: NetworkMetric = {
      url,
      method,
      startTime: this.getCurrentTime(),
    };

    return {
      end: (status?: number, size?: number, error?: string) => {
        metric.endTime = this.getCurrentTime();
        metric.duration = metric.endTime - metric.startTime;
        metric.status = status;
        metric.size = size;
        metric.error = error;

        this.networkMetrics.push(metric);

        // Keep only last 50 network metrics
        if (this.networkMetrics.length > 50) {
          this.networkMetrics = this.networkMetrics.slice(-50);
        }

        if (__DEV__) {
          const statusText = error ? 'ERROR' : status;
          console.log(`🌐 Network: ${method} ${url} - ${statusText} (${metric.duration?.toFixed(2)}ms)`);
        }
      },
    };
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    activeMetrics: string[];
    memorySnapshots: MemoryInfo[];
    networkMetrics: NetworkMetric[];
    averageNetworkTime: number;
    slowestNetworkRequests: NetworkMetric[];
  } {
    const activeMetrics = Array.from(this.metrics.keys());
    
    const completedNetworkMetrics = this.networkMetrics.filter(m => m.duration !== undefined);
    const averageNetworkTime = completedNetworkMetrics.length > 0
      ? completedNetworkMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / completedNetworkMetrics.length
      : 0;

    const slowestNetworkRequests = [...completedNetworkMetrics]
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 5);

    return {
      activeMetrics,
      memorySnapshots: [...this.memorySnapshots],
      networkMetrics: [...this.networkMetrics],
      averageNetworkTime,
      slowestNetworkRequests,
    };
  }

  /**
   * Clear all metrics and snapshots
   */
  clear(): void {
    this.metrics.clear();
    this.memorySnapshots = [];
    this.networkMetrics = [];
    
    if (__DEV__) {
      console.log('🧹 Performance metrics cleared');
    }
  }

  private getCurrentTime(): number {
    if (Platform.OS === 'web' && 'performance' in window) {
      return performance.now();
    }
    return Date.now();
  }

  private logMetric(metric: PerformanceMetric): void {
    // In production, you might want to send this to an analytics service
    if (!__DEV__) {
      // Example: Analytics.track('performance_metric', metric);
    }
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Export convenience functions
export const startMeasure = (name: string, metadata?: Record<string, any>) => 
  performanceMonitor.startMeasure(name, metadata);

export const endMeasure = (name: string) => 
  performanceMonitor.endMeasure(name);

export const measureAsync = <T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>) => 
  performanceMonitor.measureAsync(name, fn, metadata);

export const measureSync = <T>(name: string, fn: () => T, metadata?: Record<string, any>) => 
  performanceMonitor.measureSync(name, fn, metadata);

export const takeMemorySnapshot = () => 
  performanceMonitor.takeMemorySnapshot();

export const trackNetworkRequest = (url: string, method: string, metadata?: Record<string, any>) => 
  performanceMonitor.trackNetworkRequest(url, method, metadata);

export default performanceMonitor;
