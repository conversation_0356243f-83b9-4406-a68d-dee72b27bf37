// TypeError Prevention Utilities
// Comprehensive utilities to prevent common TypeError issues

/**
 * Safe array operations to prevent TypeError on undefined/null arrays
 */
export const safeArray = {
  /**
   * Safely filter an array, returns empty array if input is null/undefined
   */
  filter: <T>(array: T[] | null | undefined, predicate: (item: T) => boolean): T[] => {
    if (!Array.isArray(array)) return [];
    try {
      return array.filter(predicate);
    } catch (error) {
      console.warn('🛡️ Safe array filter error:', error);
      return [];
    }
  },

  /**
   * Safely map an array, returns empty array if input is null/undefined
   */
  map: <T, U>(array: T[] | null | undefined, mapper: (item: T) => U): U[] => {
    if (!Array.isArray(array)) return [];
    try {
      return array.map(mapper);
    } catch (error) {
      console.warn('🛡️ Safe array map error:', error);
      return [];
    }
  },

  /**
   * Safely sort an array, returns empty array if input is null/undefined
   */
  sort: <T>(array: T[] | null | undefined, compareFn?: (a: T, b: T) => number): T[] => {
    if (!Array.isArray(array)) return [];
    try {
      return [...array].sort(compareFn);
    } catch (error) {
      console.warn('🛡️ Safe array sort error:', error);
      return [];
    }
  },

  /**
   * Safely get array length, returns 0 if input is null/undefined
   */
  length: (array: any[] | null | undefined): number => {
    return Array.isArray(array) ? array.length : 0;
  },

  /**
   * Safely slice an array, returns empty array if input is null/undefined
   */
  slice: <T>(array: T[] | null | undefined, start?: number, end?: number): T[] => {
    if (!Array.isArray(array)) return [];
    try {
      return array.slice(start, end);
    } catch (error) {
      console.warn('🛡️ Safe array slice error:', error);
      return [];
    }
  }
};

/**
 * Safe string operations to prevent TypeError on undefined/null strings
 */
export const safeString = {
  /**
   * Safely slice a string, returns fallback if input is null/undefined
   */
  slice: (str: string | null | undefined, start: number, end?: number, fallback = ''): string => {
    if (typeof str !== 'string') return fallback;
    try {
      return str.slice(start, end);
    } catch (error) {
      console.warn('🛡️ Safe string slice error:', error);
      return fallback;
    }
  },

  /**
   * Safely split a string, returns empty array if input is null/undefined
   */
  split: (str: string | null | undefined, separator: string): string[] => {
    if (typeof str !== 'string') return [];
    try {
      return str.split(separator);
    } catch (error) {
      console.warn('🛡️ Safe string split error:', error);
      return [];
    }
  },

  /**
   * Safely get string length, returns 0 if input is null/undefined
   */
  length: (str: string | null | undefined): number => {
    return typeof str === 'string' ? str.length : 0;
  },

  /**
   * Safely convert to lowercase, returns fallback if input is null/undefined
   */
  toLowerCase: (str: string | null | undefined, fallback = ''): string => {
    if (typeof str !== 'string') return fallback;
    try {
      return str.toLowerCase();
    } catch (error) {
      console.warn('🛡️ Safe string toLowerCase error:', error);
      return fallback;
    }
  }
};

/**
 * Safe object operations to prevent TypeError on undefined/null objects
 */
export const safeObject = {
  /**
   * Safely get object property with fallback
   */
  get: <T>(obj: any, path: string, fallback: T): T => {
    if (!obj || typeof obj !== 'object') return fallback;
    try {
      const keys = path.split('.');
      let result = obj;
      for (const key of keys) {
        if (result == null || typeof result !== 'object') return fallback;
        result = result[key];
      }
      return result !== undefined ? result : fallback;
    } catch (error) {
      console.warn('🛡️ Safe object get error:', error);
      return fallback;
    }
  },

  /**
   * Safely check if object has property
   */
  has: (obj: any, property: string): boolean => {
    if (!obj || typeof obj !== 'object') return false;
    try {
      return Object.prototype.hasOwnProperty.call(obj, property);
    } catch (error) {
      console.warn('🛡️ Safe object has error:', error);
      return false;
    }
  },

  /**
   * Safely get object keys, returns empty array if input is null/undefined
   */
  keys: (obj: any): string[] => {
    if (!obj || typeof obj !== 'object') return [];
    try {
      return Object.keys(obj);
    } catch (error) {
      console.warn('🛡️ Safe object keys error:', error);
      return [];
    }
  }
};

/**
 * Safe function execution to prevent TypeError on undefined functions
 */
export const safeFunction = {
  /**
   * Safely call a function, returns fallback if function is null/undefined
   */
  call: <T>(fn: ((...args: any[]) => any) | null | undefined, fallback: T, ...args: any[]): T => {
    if (typeof fn !== 'function') return fallback;
    try {
      return fn(...args);
    } catch (error) {
      console.warn('🛡️ Safe function call error:', error);
      return fallback;
    }
  },

  /**
   * Safely call an async function, returns fallback promise if function is null/undefined
   */
  callAsync: async <T>(fn: ((...args: any[]) => any) | null | undefined, fallback: T, ...args: any[]): Promise<T> => {
    if (typeof fn !== 'function') return Promise.resolve(fallback);
    try {
      const result = await fn(...args);
      return result;
    } catch (error) {
      console.warn('🛡️ Safe async function call error:', error);
      return fallback;
    }
  }
};

/**
 * Type guard utilities
 */
export const typeGuards = {
  isString: (value: any): value is string => typeof value === 'string',
  isNumber: (value: any): value is number => typeof value === 'number' && !isNaN(value),
  isArray: (value: any): value is any[] => Array.isArray(value),
  isObject: (value: any): value is object => value !== null && typeof value === 'object' && !Array.isArray(value),
  isFunction: (value: any): value is ((...args: any[]) => any) => typeof value === 'function',
  isNotNull: <T>(value: T | null | undefined): value is T => value != null,
};

/**
 * Enhanced error handling for TypeError prevention
 */
export const errorSafe = {
  /**
   * Wrap any operation in try-catch and return fallback on error
   */
  wrap: <T>(operation: () => T, fallback: T): T => {
    try {
      return operation();
    } catch (error) {
      if (error instanceof TypeError) {
        console.warn('🛡️ TypeError prevented:', error.message);
      } else {
        console.warn('🛡️ Error prevented:', error);
      }
      return fallback;
    }
  },

  /**
   * Wrap async operation in try-catch and return fallback on error
   */
  wrapAsync: async <T>(operation: () => Promise<T>, fallback: T): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof TypeError) {
        console.warn('🛡️ Async TypeError prevented:', error.message);
      } else {
        console.warn('🛡️ Async error prevented:', error);
      }
      return fallback;
    }
  }
};

// Export all utilities as a single object for convenience
export const TypeErrorPrevention = {
  safeArray,
  safeString,
  safeObject,
  safeFunction,
  typeGuards,
  errorSafe
};

export default TypeErrorPrevention;
