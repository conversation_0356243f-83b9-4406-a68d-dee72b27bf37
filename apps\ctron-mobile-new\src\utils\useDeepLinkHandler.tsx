// src/utils/useDeepLinkHandler.ts

import { useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { navigationRef } from '../navigation/navigationRef';
// Conditional import for Linking
let Linking: any = null;

// Dynamically import expo-linking for web compatibility
import('expo-linking')
  .then((module) => {
    Linking = module;
  })
  .catch((error) => {
    console.warn('expo-linking not available:', error);
    Linking = {
      addEventListener: () => ({ remove: () => {} }),
      getInitialURL: () => Promise.resolve(null),
    };
  });

export default function useDeepLinkHandler() {
  const { token, user } = useAuth();

  useEffect(() => {
    if (!token || !user) return;

    const handleUrl = ({ url }: { url: string }) => {
      if (__DEV__) {
        console.log('🔗 Deep link received:', url);
      }

      const parsed = Linking.parse(url);
      const path = parsed.path || '';

      if (!path) {
        if (__DEV__) {
          console.log('ℹ️ No specific path. Staying on current screen.');
        }
        return;
      }

      const isTechnician = user.role === 'TECHNICIAN';
      const isHomeowner = user.role === 'HOMEOWNER';

      if (path.startsWith('technician') && isTechnician) {
        navigationRef.current?.navigate('TechnicianStack');
      } else if (path.startsWith('my-jobs') && isHomeowner) {
        navigationRef.current?.navigate('HomeownerStack');
      } else {
        if (__DEV__) {
          console.warn('⚠️ Deep link path not recognized. No navigation triggered.');
        }
        // We don't deny harmless links like root URL
      }
    };

    const sub = Linking.addEventListener('url', handleUrl);

    Linking.getInitialURL().then((url: string | null) => {
      if (url) handleUrl({ url });
    });

    return () => sub.remove();
  }, [token, user]);
}
