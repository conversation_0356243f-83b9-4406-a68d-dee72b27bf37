export const isValidEmail = (email: string): boolean => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
};

export const isValidPhone = (phone: string): boolean => {
  const re = /^[0-9]{10,15}$/; // Basic global format
  return re.test(phone);
};

export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, one uppercase, one lowercase, one number
  const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/;
  return re.test(password);
};

export const isRequired = (value: string | null | undefined): boolean => {
  return value !== undefined && value !== null && value.trim().length > 0;
};

export const validateName = (name: string): boolean => {
  return /^[a-zA-Z\s'-]{2,50}$/.test(name);
};

export const isNumeric = (value: string): boolean => {
  return !isNaN(Number(value));
};
