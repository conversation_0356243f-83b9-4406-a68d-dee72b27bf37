// Web polyfills for React Native modules that don't work on web
// Detect platform without importing React Native to avoid circular dependencies
const Platform = {
  OS: typeof window !== 'undefined' && typeof document !== 'undefined' ? 'web' :
      typeof navigator !== 'undefined' && navigator.product === 'ReactNative' ? 'ios' : 'android'
};

export const setupWebPolyfills = () => {
  if (Platform.OS !== 'web') {
    return; // Only apply polyfills on web
  }

  console.log('🌐 Setting up web polyfills...');

  // Import BatchedBridge polyfill
  import('../polyfills/BatchedBridge-web.js')
    .then(() => {
      console.log('🌐 BatchedBridge polyfill loaded');
    })
    .catch((e) => {
      const error = e as Error;
      console.log('🌐 BatchedBridge polyfill not available:', error.message);
    });

  // Import ConsoleErrorReporter polyfill
  import('../polyfills/ConsoleErrorReporter-web.js')
    .then(() => {
      console.log('🌐 ConsoleErrorReporter polyfill loaded');
    })
    .catch((e) => {
      const error = e as Error;
      console.log('🌐 ConsoleErrorReporter polyfill not available:', error.message);
    });

  // Fix for __fbBatchedBridgeConfig (React Native Bridge)
  if (typeof global !== 'undefined') {
    if (!(global as any).__fbBatchedBridgeConfig) {
      (global as any).__fbBatchedBridgeConfig = {
        remoteModuleConfig: [],
        localModulesConfig: []
      };
      console.log('🌐 Mock: __fbBatchedBridgeConfig setup');
    }

    // Mock NativeModules
    if (!(global as any).nativeModules) {
      (global as any).nativeModules = {};
    }

    // Mock BatchedBridge
    if (!(global as any).BatchedBridge) {
      (global as any).BatchedBridge = {
        registerCallableModule: () => {},
        enqueueNativeCall: () => {},
        callFunctionReturnFlushedQueue: () => [],
        invokeCallbackAndReturnFlushedQueue: () => [],
        flushedQueue: () => [],
        getEventLoopRunningTime: () => 0
      };
      console.log('🌐 Mock: BatchedBridge setup');
    }
  }

  // Fix for RCTDeviceEventEmitter
  if (typeof global !== 'undefined') {
    // Mock RCTDeviceEventEmitter for web
    if (!(global as any)._RCTDeviceEventEmitter) {
      (global as any)._RCTDeviceEventEmitter = {
        default: {
          addListener: () => {
            console.log('🌐 Mock: RCTDeviceEventEmitter.addListener called');
            return { remove: () => {} };
          },
          removeListener: () => {
            console.log('🌐 Mock: RCTDeviceEventEmitter.removeListener called');
          },
          emit: () => {
            console.log('🌐 Mock: RCTDeviceEventEmitter.emit called');
          }
        }
      };
    }

    // Mock other common native modules that might cause issues
    if (!(global as any).nativeEventEmitter) {
      (global as any).nativeEventEmitter = {
        addListener: () => ({ remove: () => {} }),
        removeListener: () => {},
        emit: () => {}
      };
    }
  }

  // Mock expo-notifications for web
  import('expo-notifications')
    .then((Notifications) => {
      if (Notifications && !Notifications.addNotificationReceivedListener) {
        Notifications.addNotificationReceivedListener = () => ({ remove: () => {} });
        Notifications.addNotificationResponseReceivedListener = () => ({ remove: () => {} });
      }
    })
    .catch(() => {
      console.log('🌐 Expo notifications not available on web (expected)');
    });

  // Mock expo-secure-store for web
  import('expo-secure-store')
    .then((SecureStore) => {
      if (SecureStore && Platform.OS === 'web') {
        SecureStore.getItemAsync = async (key: string) => {
          return localStorage.getItem(key);
        };
        SecureStore.setItemAsync = async (key: string, value: string) => {
          localStorage.setItem(key, value);
        };
        SecureStore.deleteItemAsync = async (key: string) => {
          localStorage.removeItem(key);
        };
      }
    })
    .catch((e) => {
      console.log('🌐 SecureStore polyfill setup failed (might not be needed)', e);
    });

  // Mock common native modules that might be called
  if (typeof global !== 'undefined' && (global as any).nativeModules) {
    // Mock AsyncStorage
    (global as any).nativeModules.AsyncStorage = {
      getItem: (key: string) => Promise.resolve(localStorage.getItem(key)),
      setItem: (key: string, value: string) => Promise.resolve(localStorage.setItem(key, value)),
      removeItem: (key: string) => Promise.resolve(localStorage.removeItem(key)),
      clear: () => Promise.resolve(localStorage.clear()),
      getAllKeys: () => Promise.resolve(Object.keys(localStorage))
    };

    // Mock other common native modules
    (global as any).nativeModules.DeviceInfo = {
      getConstants: () => ({ deviceId: 'web-device' })
    };

    (global as any).nativeModules.StatusBarManager = {
      getHeight: () => Promise.resolve(0),
      setStyle: () => {},
      setHidden: () => {}
    };

    console.log('🌐 Mock: Native modules setup');
  }

  // Mock React Native's NativeModules
  try {
    // Skip NativeModules import to avoid circular dependency
    // NativeModules will be handled by the bridge polyfill
    console.log('🌐 Mock: NativeModules setup skipped (handled by bridge polyfill)');
  } catch (_e) {
    console.log('🌐 NativeModules polyfill not needed', _e);
  }

  console.log('✅ Web polyfills setup complete');
};
