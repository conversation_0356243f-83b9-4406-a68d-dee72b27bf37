// Simple test entry point to check if basic imports work
console.log('Starting test entry...');

try {
  console.log('Testing React import...');
  const React = require('react');
  console.log('React imported successfully');

  console.log('Testing React Native import...');
  const { Platform } = require('react-native');
  console.log('React Native imported successfully, Platform:', Platform.OS);

  console.log('Testing App.web import...');
  const AppWeb = require('./App.web');
  console.log('App.web imported successfully');

  console.log('All imports successful!');
} catch (error) {
  console.error('Import error:', error.message);
  console.error('Stack:', error.stack);
}
