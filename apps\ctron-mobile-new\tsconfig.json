{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    // Target & Module Settings
    "target": "ES2022",
    "lib": [
      "ES2022",
      "DOM"
    ],
    "module": "ESNext",
    "moduleResolution": "bundler",
    // React Native Specific
    "jsx": "react-native",
    "customConditions": [
      "react-native"
    ],
    // Strict Type Checking
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUncheckedIndexedAccess": true,
    // Module Resolution
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    // Emit Settings
    "noEmit": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    // Path Mapping
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@components/*": [
        "./src/components/*"
      ],
      "@screens/*": [
        "./src/screens/*"
      ],
      "@utils/*": [
        "./src/utils/*"
      ],
      "@api/*": [
        "./src/api/*"
      ],
      "@context/*": [
        "./src/context/*"
      ],
      "@hooks/*": [
        "./src/hooks/*"
      ],
      "@navigation/*": [
        "./src/navigation/*"
      ],
      "@services/*": [
        "./src/services/*"
      ],
      "@types/*": [
        "./src/types/*"
      ],
      "@assets/*": [
        "./assets/*"
      ]
    },
    // Type Definitions - Remove reference to problematic react-native.d.ts
    "typeRoots": [
      "./node_modules/@types",
      "./src/types"
    ]
  },
  "include": [
    "src/**/*",
    "App.tsx",
    "App.native.tsx",
    "App.web.tsx",
    "index.ts",
    "app.config.ts",
    "metro.config.js",
    "global.d.ts"
  ],
  "exclude": [
    "node_modules",
    "babel.config.js",
    "jest.config.js",
    "coverage",
    "android",
    "ios",
    "dist",
    "build",
    "web-build",
    ".expo",
    "src/polyfills/**/*.js"
  ]
}