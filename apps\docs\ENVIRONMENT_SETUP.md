# CTRON Home - Environment Configuration Guide

This guide explains how to configure environment variables for different deployment environments (development, staging, production) across all CTRON Home applications.

## 📁 Environment Files Structure

```
apps/
├── backend/
│   ├── .env                    # Current environment (copy from specific env file)
│   ├── .env.development        # Development environment
│   ├── .env.staging           # Staging environment
│   └── .env.production        # Production environment
├── web/
│   ├── .env                    # Current environment (copy from specific env file)
│   ├── .env.development        # Development environment
│   ├── .env.staging           # Staging environment
│   └── .env.production        # Production environment
└── mobile/
    ├── .env                    # Current environment (copy from specific env file)
    ├── .env.development        # Development environment
    ├── .env.staging           # Staging environment
    └── .env.production        # Production environment
```

## 🚀 Quick Setup

### 1. Development Environment
```bash
# Backend
cd apps/backend
cp .env.development .env
npm run dev

# Web App
cd apps/web
cp .env.development .env
npm run dev

# Mobile App
cd apps/mobile
cp .env.development .env
npm start
```

### 2. Staging Environment
```bash
# Backend
cd apps/backend
cp .env.staging .env
npm run dev:staging

# Web App
cd apps/web
cp .env.staging .env
npm run dev:staging

# Mobile App
cd apps/mobile
cp .env.staging .env
npm start
```

### 3. Production Environment
```bash
# Backend
cd apps/backend
cp .env.production .env
npm run start

# Web App
cd apps/web
cp .env.production .env
npm run build && npm run preview

# Mobile App
cd apps/mobile
cp .env.production .env
npm run build
```

## 🔧 Environment Variables Reference

### Backend Environment Variables

| Variable | Description | Development | Staging | Production |
|----------|-------------|-------------|---------|------------|
| `NODE_ENV` | Environment mode | development | staging | production |
| `PORT` | Server port | 3001 | 3001 | 3001 |
| `DATABASE_URL` | PostgreSQL connection | localhost | staging-db | production-db |
| `JWT_SECRET` | JWT signing secret | dev-secret | staging-secret | production-secret |
| `STRIPE_SECRET_KEY` | Stripe API key | test key | test key | live key |
| `CORS_ORIGIN` | Allowed origins | localhost:* | staging.ctron.com | app.ctron.com |

### Web App Environment Variables

| Variable | Description | Development | Staging | Production |
|----------|-------------|-------------|---------|------------|
| `VITE_API_URL` | Backend API URL | localhost:3001/api | staging-api/api | api.ctron.com/api |
| `VITE_SOCKET_URL` | Socket.IO URL | localhost:3001 | staging-api | api.ctron.com |
| `VITE_STRIPE_PUBLIC_KEY` | Stripe public key | test key | test key | live key |
| `VITE_APP_ENVIRONMENT` | App environment | development | staging | production |

### Mobile App Environment Variables

| Variable | Description | Development | Staging | Production |
|----------|-------------|-------------|---------|------------|
| `EXPO_PUBLIC_API_URL` | Backend API URL | 192.168.x.x:3001/api | staging-api/api | api.ctron.com/api |
| `EXPO_PUBLIC_SOCKET_URL` | Socket.IO URL | 192.168.x.x:3001 | staging-api | api.ctron.com |
| `EXPO_PUBLIC_STRIPE_PUBLIC_KEY` | Stripe public key | test key | test key | live key |
| `EXPO_PUBLIC_APP_ENVIRONMENT` | App environment | development | staging | production |

## 🔒 Security Best Practices

1. **Never commit sensitive .env files** - Add them to .gitignore
2. **Use different secrets per environment** - Don't reuse production secrets in development
3. **Rotate secrets regularly** - Especially JWT secrets and API keys
4. **Use environment-specific databases** - Separate dev, staging, and production data
5. **Restrict CORS origins** - Only allow necessary domains in production

## 🛠️ Customization

To add new environment variables:

1. Add to the appropriate `.env.*` files
2. Update TypeScript types in `src/types/env.d.ts`
3. Add to the configuration files (`config/env.ts`)
4. Update this documentation

## 📝 Notes

- **Development**: Uses localhost URLs and test API keys
- **Staging**: Uses staging servers with test API keys for safe testing
- **Production**: Uses live servers and live API keys
- **Mobile Development**: Uses your local IP address for API connections
- **Environment Validation**: Apps validate required environment variables on startup
