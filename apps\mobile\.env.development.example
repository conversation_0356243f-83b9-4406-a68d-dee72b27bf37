# ===========================================
# CTRON HOME MOBILE APP - DEVELOPMENT TEMPLATE
# ===========================================
# 
# This is a TEMPLATE file for development environment.
# Copy this to .env and update with your actual values.
# The .env file should contain your real configuration.
#

# API Configuration (Update these with your network IP and port)
EXPO_PUBLIC_API_URL=http://YOUR_IP_ADDRESS:YOUR_PORT/api
EXPO_PUBLIC_API_BASE_URL=http://YOUR_IP_ADDRESS:YOUR_PORT
EXPO_PUBLIC_SOCKET_URL=http://YOUR_IP_ADDRESS:YOUR_PORT

# Stripe Configuration (Test Keys)
EXPO_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here

# App Configuration
EXPO_PUBLIC_APP_NAME=CTRON Home
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_APP_ENVIRONMENT=development

# External Services
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
EXPO_PUBLIC_ANALYTICS_ID=your_analytics_id_here

# Feature Flags
EXPO_PUBLIC_ENABLE_ANALYTICS=false
EXPO_PUBLIC_ENABLE_DEBUG=true
EXPO_PUBLIC_ENABLE_MOCK_DATA=false

# Upload Configuration
EXPO_PUBLIC_MAX_FILE_SIZE=10485760
EXPO_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Timeouts and Limits
EXPO_PUBLIC_API_TIMEOUT=10000
EXPO_PUBLIC_SOCKET_TIMEOUT=5000

# Socket.IO Configuration
EXPO_PUBLIC_SOCKET_RECONNECT_ATTEMPTS=5
EXPO_PUBLIC_SOCKET_RECONNECT_DELAY=1000
EXPO_PUBLIC_SOCKET_RECONNECT_DELAY_MAX=5000

# Network Configuration (for dynamic IP/port management)
EXPO_PUBLIC_BACKEND_IP=YOUR_IP_ADDRESS
EXPO_PUBLIC_BACKEND_PORT=YOUR_PORT
EXPO_PUBLIC_MOBILE_PORT=8081

# Development Configuration
EXPO_PUBLIC_DEV_SERVER_IP=YOUR_IP_ADDRESS
EXPO_PUBLIC_DEV_SERVER_PORT=YOUR_PORT
