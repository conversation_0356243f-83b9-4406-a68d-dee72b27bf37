# CTRON Home Mobile App Environment Configuration
# Copy this file to .env and fill in your actual values

# API Configuration
EXPO_PUBLIC_API_BASE_URL=http://localhost:3001
EXPO_PUBLIC_BACKEND_IP=localhost
EXPO_PUBLIC_BACKEND_PORT=3001
EXPO_PUBLIC_SOCKET_URL=http://localhost:3001

# Stripe Configuration
EXPO_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here
EXPO_PUBLIC_MERCHANT_IDENTIFIER=merchant.com.ctronhome

# AWS Configuration (Optional)
EXPO_PUBLIC_AWS_REGION=us-east-1
EXPO_PUBLIC_AWS_BUCKET_NAME=ctron-home-uploads

# Expo Configuration
EXPO_PUBLIC_PROJECT_ID=your-expo-project-id-here

# API Configuration
EXPO_PUBLIC_API_TIMEOUT=30000
EXPO_PUBLIC_SOCKET_TIMEOUT=10000
EXPO_PUBLIC_SOCKET_RECONNECT_ATTEMPTS=5
EXPO_PUBLIC_SOCKET_RECONNECT_DELAY=1000
EXPO_PUBLIC_SOCKET_RECONNECT_DELAY_MAX=5000

# Debug Configuration
EXPO_PUBLIC_ENABLE_DEBUG=true

# Development Notes:
# - Replace localhost with your actual IP address when testing on physical devices
# - Get your Expo Project ID from https://expo.dev/accounts/[account]/projects/[project]/settings
# - Stripe keys should be test keys for development, live keys for production
# - AWS configuration is optional and only needed if using file uploads
