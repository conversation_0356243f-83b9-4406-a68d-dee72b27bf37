# ===========================================
# CTRON HOME MOBILE APP - PRODUCTION TEMPLATE
# ===========================================
# 
# This is a TEMPLATE file for production environment.
# Copy this to .env and update with your actual values.
# The .env file should contain your real configuration.
#

# API Configuration (Production URLs)
EXPO_PUBLIC_API_URL=https://your-production-api-domain.com/api
EXPO_PUBLIC_API_BASE_URL=https://your-production-api-domain.com
EXPO_PUBLIC_SOCKET_URL=https://your-production-api-domain.com

# Stripe Configuration (Live Keys)
EXPO_PUBLIC_STRIPE_PUBLIC_KEY=pk_live_your_live_stripe_public_key_here

# App Configuration
EXPO_PUBLIC_APP_NAME=CTRON Home
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_APP_ENVIRONMENT=production

# External Services
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your-production-google-maps-key
EXPO_PUBLIC_ANALYTICS_ID=your-production-analytics-id

# Feature Flags
EXPO_PUBLIC_ENABLE_ANALYTICS=true
EXPO_PUBLIC_ENABLE_DEBUG=false
EXPO_PUBLIC_ENABLE_MOCK_DATA=false

# Upload Configuration
EXPO_PUBLIC_MAX_FILE_SIZE=10485760
EXPO_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Timeouts and Limits
EXPO_PUBLIC_API_TIMEOUT=10000
EXPO_PUBLIC_SOCKET_TIMEOUT=5000

# Production Configuration (not used in production builds)
EXPO_PUBLIC_BACKEND_IP=
EXPO_PUBLIC_BACKEND_PORT=
EXPO_PUBLIC_MOBILE_PORT=
EXPO_PUBLIC_DEV_SERVER_IP=
EXPO_PUBLIC_DEV_SERVER_PORT=
