#!/bin/sh

# CTRON Home - Pre-commit hook to prevent accidental file reverts
# This hook checks if critical files have been modified unexpectedly

echo "🔍 CTRON Home: Checking for unexpected file changes..."

# Critical files that should be carefully reviewed before commit
CRITICAL_FILES="metro.config.js package.json app.json index.ts"

# Check if any critical files are being committed
for file in $CRITICAL_FILES; do
    if git diff --cached --name-only | grep -q "^apps/mobile/$file$"; then
        echo "⚠️  Critical file being committed: $file"
        
        # Show the diff for review
        echo "📝 Changes in $file:"
        git diff --cached "apps/mobile/$file" | head -20
        
        # Ask for confirmation
        echo ""
        echo "❓ Are you sure you want to commit changes to $file? (y/N)"
        read -r response
        
        case "$response" in
            [yY][eE][sS]|[yY])
                echo "✅ Proceeding with commit of $file"
                ;;
            *)
                echo "❌ Commit aborted. Review changes to $file"
                exit 1
                ;;
        esac
    fi
done

# Check for polyfill files
if git diff --cached --name-only | grep -q "^apps/mobile/src/polyfills/"; then
    echo "🔧 Polyfill files being committed - this is usually safe"
fi

echo "✅ Pre-commit checks passed"
exit 0
