# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*
.metro/

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# Cache directories that can cause file conflicts
node_modules/.cache/
.cache/
*.cache
.tmp/
temp/

# IDE and editor files that might auto-restore
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Auto-generated files that might conflict
*.log
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Build artifacts
build/
dist/
out/
.env