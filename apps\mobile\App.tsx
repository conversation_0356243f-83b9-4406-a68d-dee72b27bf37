// mobile-app/App.tsx
import React, { useEffect, useState } from 'react';
import { LogBox, Platform, StatusBar, StyleSheet, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/context/AuthContext';
import { JobProvider } from './src/context/JobContext';
import { AppNavigator } from './src/navigation/RootNavigator';
import { ErrorBoundary } from './src/components/ErrorBoundary';
import { validateEnvironment } from './src/utils/environmentValidator';
import { setupNotifications } from './src/services/notificationService';
import { StripeProvider } from '@stripe/stripe-react-native';

// Ignore specific warnings that are known issues or not critical for development
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
  'Require cycle:',
  'Constants.manifest.extra has been deprecated',
  'ViewPropTypes will be removed from React Native',
  'new NativeEventEmitter() was called with a non-null argument without the native module',
  'EventEmitter.removeListener(',
  'Warning: componentWillMount has been renamed',
  'Warning: componentWillReceiveProps has been renamed',
  'Warning: componentWillUpdate has been renamed',
]);

// Validate environment variables on app startup
validateEnvironment();

export default function App() {
  const [isNotificationsSetup, setIsNotificationsSetup] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      console.log('App.tsx: Application starting up...');
      // Setup notifications
      try {
        await setupNotifications();
        setIsNotificationsSetup(true);
        console.log('App.tsx: Notifications setup complete.');
      } catch (error) {
        console.error('App.tsx: Error setting up notifications:', error);
        // Decide how to handle notification setup failure (e.g., show a user message)
      }
    };

    initializeApp();
  }, []);

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <StripeProvider
          publishableKey={process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || ''}
          merchantIdentifier="merchant.com.ctron.home"
          urlScheme="ctronhome"
        >
          <AuthProvider>
            <JobProvider>
              <AppNavigator />
              <StatusBar barStyle="dark-content" />
            </JobProvider>
          </AuthProvider>
        </StripeProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});
