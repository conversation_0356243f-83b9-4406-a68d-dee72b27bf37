// metro.config.js - SMART PLATFORM-AWARE CONFIGURATION
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Clean alias configuration
config.resolver.alias = {
  // Path shortcuts for cleaner imports
  '@components': './src/components',
  '@screens': './src/screens',
  '@api': './src/api',
  '@utils': './src/utils',

  // Essential React Native Web compatibility (WEB ONLY)
  'react-native$': 'react-native-web',
  'stream': 'stream-browserify',
  '../Components/AccessibilityInfo/legacySendAccessibilityEvent': './src/components/AccessibilityInfo/legacySendAccessibilityEvent.js',
};

// Platform support configuration
config.resolver.platforms = ['native', 'web', 'ios', 'android'];
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// SMART PLATFORM-AWARE RESOLVER - COMPREHENSIVE WEB POLYFILLS + NATIVE COMPATIBILITY
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // CRITICAL: Preserve native functionality for iOS and Android
  if (platform === 'ios' || platform === 'android' || platform === 'native') {
    return context.resolveRequest(context, moduleName, platform);
  }

  // COMPREHENSIVE WEB POLYFILLS - Only applied to web platform
  if (platform === 'web') {
    // 1. PlatformColorValueTypes polyfill (ALL PATHS)
    if (moduleName === './PlatformColorValueTypes' ||
        moduleName.includes('PlatformColorValueTypes') ||
        moduleName.includes('StyleSheet/PlatformColorValueTypes')) {
      return {
        filePath: require.resolve('./src/polyfills/PlatformColorValueTypes.js'),
        type: 'sourceFile',
      };
    }

    // 2. Expo device polyfill for web
    if (moduleName === 'expo-device') {
      return {
        filePath: require.resolve('./src/polyfills/expo-device-web.js'),
        type: 'sourceFile',
      };
    }

    // 3. AsyncStorage polyfill for web
    if (moduleName === '@react-native-async-storage/async-storage') {
      return {
        filePath: require.resolve('./src/polyfills/async-storage-web.js'),
        type: 'sourceFile',
      };
    }

    // 4. React Native Platform utilities polyfill for web
    if (moduleName.includes('Utilities/Platform') || moduleName.includes('Platform/Platform') || moduleName === './Platform') {
      return {
        filePath: require.resolve('./src/polyfills/react-native-platform-web.js'),
        type: 'sourceFile',
      };
    }

    // 5. DateTimePicker polyfill for web
    if (moduleName === '@react-native-community/datetimepicker') {
      return {
        filePath: require.resolve('./src/polyfills/datetimepicker-web.js'),
        type: 'sourceFile',
      };
    }

    // 6. BaseViewConfig polyfill for web
    if (moduleName === './BaseViewConfig' || moduleName.includes('BaseViewConfig')) {
      return {
        filePath: require.resolve('./src/polyfills/BaseViewConfig-web.js'),
        type: 'sourceFile',
      };
    }

    // 7. RCTAlertManager polyfill for web
    if (moduleName === './RCTAlertManager' || moduleName.includes('RCTAlertManager')) {
      return {
        filePath: require.resolve('./src/polyfills/RCTAlertManager-web.js'),
        type: 'sourceFile',
      };
    }

    // 8. RCTNetworking polyfill for web
    if (moduleName === './RCTNetworking' || moduleName.includes('RCTNetworking')) {
      return {
        filePath: require.resolve('./src/polyfills/RCTNetworking-web.js'),
        type: 'sourceFile',
      };
    }

    // 9. DevToolsSettingsManager polyfill for web
    if (moduleName.includes('DevToolsSettingsManager') || moduleName.includes('DevToolsSettings')) {
      return {
        filePath: require.resolve('./src/polyfills/DevToolsSettingsManager-web.js'),
        type: 'sourceFile',
      };
    }

    // 10. BackHandler polyfill for web
    if (moduleName.includes('BackHandler') || moduleName.includes('Utilities/BackHandler')) {
      return {
        filePath: require.resolve('./src/polyfills/BackHandler-web.js'),
        type: 'sourceFile',
      };
    }

    // 11. Image component polyfill for web
    if (moduleName.includes('Image/Image') || moduleName === './Image' || (moduleName.includes('/Image') && !moduleName.includes('ImageBackground'))) {
      return {
        filePath: require.resolve('./src/polyfills/Image-web.js'),
        type: 'sourceFile',
      };
    }

    // 12. React Native utilities polyfill for web
    if (moduleName.includes('Utilities/') && !moduleName.includes('Platform') && !moduleName.includes('BackHandler')) {
      return {
        filePath: require.resolve('./src/polyfills/ReactNativeUtilities-web.js'),
        type: 'sourceFile',
      };
    }

    // 13. LogBox and development utilities polyfill for web
    if (moduleName.includes('LogBox') || moduleName.includes('YellowBox') || moduleName.includes('ExceptionsManager') || moduleName.includes('HMRClient')) {
      return {
        filePath: require.resolve('./src/polyfills/LogBox-web.js'),
        type: 'sourceFile',
      };
    }

    // 14. ConsoleErrorReporter polyfill for web
    if (moduleName.includes('ConsoleErrorReporter') || moduleName.includes('installConsoleErrorReporter') || moduleName.includes('ErrorReporter')) {
      return {
        filePath: require.resolve('./src/polyfills/ConsoleErrorReporter-web.js'),
        type: 'sourceFile',
      };
    }


  }

  // Fall back to default resolver for all other cases
  return context.resolveRequest(context, moduleName, platform);
};

// Optimized transformer configuration
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

module.exports = config;
