#!/usr/bin/env node

/**
 * CTRON Home - File Protection Script
 * Prevents critical files from auto-reverting during error fixes
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Critical files that should not auto-revert
const PROTECTED_FILES = [
  'metro.config.js',
  'package.json',
  'app.json',
  'index.ts',
  'src/context/AuthContext.tsx', // CRITICAL: Web authentication fix
  'src/polyfills/PlatformColorValueTypes.js',
  'src/polyfills/ReactNativePrivateInterface.js',
  'src/polyfills/immediate-bridge-setup.js', // CRITICAL: Bridge setup
  'src/polyfills/react-native-bridge-web.js', // CRITICAL: Web compatibility
  'App.tsx' // CRITICAL: Main app component
];

// Store file hashes to detect unauthorized changes
const fileHashes = new Map();

function calculateFileHash(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return crypto.createHash('sha256').update(content).digest('hex');
  } catch (error) {
    return null;
  }
}

function saveFileHash(filePath) {
  const hash = calculateFileHash(filePath);
  if (hash) {
    fileHashes.set(filePath, hash);
    console.log(`🔒 Protected: ${filePath}`);
  }
}

function checkFileIntegrity(filePath) {
  const currentHash = calculateFileHash(filePath);
  const savedHash = fileHashes.get(filePath);
  
  if (savedHash && currentHash && savedHash !== currentHash) {
    console.warn(`⚠️  WARNING: ${filePath} has been modified unexpectedly!`);
    console.warn(`   Expected: ${savedHash.substring(0, 8)}...`);
    console.warn(`   Current:  ${currentHash.substring(0, 8)}...`);
    return false;
  }
  
  return true;
}

function protectFiles() {
  console.log('🛡️  CTRON Home File Protection - Starting...');
  
  PROTECTED_FILES.forEach(file => {
    const filePath = path.resolve(file);
    if (fs.existsSync(filePath)) {
      saveFileHash(filePath);
    } else {
      console.warn(`⚠️  File not found: ${file}`);
    }
  });
  
  console.log(`🛡️  Protected ${fileHashes.size} files`);
}

function monitorFiles() {
  console.log('👁️  Starting file monitoring...');
  
  PROTECTED_FILES.forEach(file => {
    const filePath = path.resolve(file);
    if (fs.existsSync(filePath)) {
      fs.watchFile(filePath, { interval: 1000 }, (curr, prev) => {
        if (curr.mtime !== prev.mtime) {
          console.log(`📝 File changed: ${file}`);
          if (!checkFileIntegrity(filePath)) {
            console.log(`🔄 Consider restoring ${file} if this was unexpected`);
          } else {
            // Update hash for legitimate changes
            saveFileHash(filePath);
          }
        }
      });
    }
  });
}

function stopMonitoring() {
  console.log('🛑 Stopping file monitoring...');
  PROTECTED_FILES.forEach(file => {
    const filePath = path.resolve(file);
    if (fs.existsSync(filePath)) {
      fs.unwatchFile(filePath);
    }
  });
}

// CLI interface
const command = process.argv[2];

switch (command) {
  case 'protect':
    protectFiles();
    break;
    
  case 'monitor':
    protectFiles();
    monitorFiles();
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Received SIGINT, stopping monitoring...');
      stopMonitoring();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 Received SIGTERM, stopping monitoring...');
      stopMonitoring();
      process.exit(0);
    });
    
    console.log('Press Ctrl+C to stop monitoring');
    break;
    
  case 'check':
    protectFiles();
    let allGood = true;
    PROTECTED_FILES.forEach(file => {
      const filePath = path.resolve(file);
      if (fs.existsSync(filePath)) {
        if (!checkFileIntegrity(filePath)) {
          allGood = false;
        }
      }
    });
    
    if (allGood) {
      console.log('✅ All protected files are intact');
    } else {
      console.log('❌ Some files have unexpected changes');
      process.exit(1);
    }
    break;
    
  default:
    console.log('CTRON Home File Protection Script');
    console.log('');
    console.log('Usage:');
    console.log('  node protect-files.js protect  - Protect current file states');
    console.log('  node protect-files.js monitor  - Monitor files for changes');
    console.log('  node protect-files.js check    - Check file integrity');
    console.log('');
    console.log('Protected files:');
    PROTECTED_FILES.forEach(file => {
      console.log(`  - ${file}`);
    });
    break;
}
