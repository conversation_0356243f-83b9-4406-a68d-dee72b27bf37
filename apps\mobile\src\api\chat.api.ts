// CTRON Home - Chat API Service
// API calls for chat functionality connecting to backend endpoints

import { API_BASE_URL } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';

interface CreateChatRequest {
  jobId: string;
}

interface SendMessageRequest {
  chatId: string;
  content: string;
  attachments?: Array<{
    filename: string;
    mimeType: string;
    size: number;
    url: string;
  }>;
}

interface GetChatMessagesRequest {
  chatId: string;
  page?: number;
  limit?: number;
  before?: string;
  after?: string;
}

interface Chat {
  id: string;
  jobId: string;
  status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED';
  createdAt: string;
  updatedAt: string;
  job: {
    id: string;
    title: string;
    description: string;
    status: string;
  };
  participants: Array<{
    id: string;
    userId: string;
    user: {
      id: string;
      fullName: string;
      role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
    };
  }>;
  lastMessage?: {
    id: string;
    content: string;
    createdAt: string;
    sender: {
      id: string;
      fullName: string;
    };
  };
  unreadCount?: number;
}

interface Message {
  id: string;
  content: string;
  senderId: string;
  chatId: string;
  createdAt: string;
  sender: {
    id: string;
    fullName: string;
    role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  };
  readBy: Array<{
    userId: string;
    readAt: string;
  }>;
  attachments: Array<{
    id: string;
    filename: string;
    mimeType: string;
    size: number;
    url: string;
  }>;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ChatAPIService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = await getAuthToken();
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get all chats for the current user
   */
  async getUserChats(): Promise<{ chats: Chat[] }> {
    return this.makeRequest<{ chats: Chat[] }>('/api/chats');
  }

  /**
   * Create a new chat for a job
   */
  async createChat(data: CreateChatRequest): Promise<{ chat: Chat }> {
    return this.makeRequest<{ chat: Chat }>('/api/chats', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Get messages for a specific chat
   */
  async getChatMessages(chatId: string, params?: Omit<GetChatMessagesRequest, 'chatId'>): Promise<{ 
    messages: Message[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.before) queryParams.append('before', params.before);
    if (params?.after) queryParams.append('after', params.after);

    const queryString = queryParams.toString();
    const endpoint = `/api/chats/${chatId}/messages${queryString ? `?${queryString}` : ''}`;

    return this.makeRequest<{ 
      messages: Message[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(endpoint);
  }

  /**
   * Send a message in a chat (also available via Socket.IO)
   */
  async sendMessage(data: SendMessageRequest): Promise<{ message: Message }> {
    const { chatId, ...messageData } = data;
    return this.makeRequest<{ message: Message }>(`/api/chats/${chatId}/messages`, {
      method: 'POST',
      body: JSON.stringify(messageData),
    });
  }

  /**
   * Mark a specific message as read
   */
  async markMessageRead(messageId: string): Promise<{ success: boolean }> {
    return this.makeRequest<{ success: boolean }>(`/api/chats/messages/${messageId}/read`, {
      method: 'POST',
    });
  }

  /**
   * Mark all messages in a chat as read
   */
  async markMessagesRead(chatId: string): Promise<{ success: boolean }> {
    return this.makeRequest<{ success: boolean }>(`/api/chats/${chatId}/read`, {
      method: 'POST',
    });
  }

  /**
   * Update chat status
   */
  async updateChatStatus(chatId: string, status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED'): Promise<{ chat: Chat }> {
    return this.makeRequest<{ chat: Chat }>(`/api/chats/${chatId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  }

  /**
   * Get chat details by ID
   */
  async getChatById(chatId: string): Promise<{ chat: Chat }> {
    return this.makeRequest<{ chat: Chat }>(`/api/chats/${chatId}`);
  }

  /**
   * Get chat by job ID
   */
  async getChatByJobId(jobId: string): Promise<{ chat: Chat | null }> {
    return this.makeRequest<{ chat: Chat | null }>(`/api/chats/job/${jobId}`);
  }

  /**
   * Get or create a chat for a specific job
   */
  async getOrCreateJobChat(jobId: string): Promise<{ chat: Chat }> {
    return this.makeRequest<{ chat: Chat }>(`/api/chats/job/${jobId}`, {
      method: 'GET',
    });
  }

  /**
   * Get or create a chat for a specific job (alias for compatibility)
   */
  async getOrCreateChat(jobId: string): Promise<{ chat: Chat }> {
    return this.getOrCreateJobChat(jobId);
  }

  /**
   * Send a message with simplified interface
   */
  async sendSimpleMessage(chatId: string, messageData: { content: string; messageType?: string }): Promise<Message> {
    const response = await this.makeRequest<{ message: Message }>(`/api/chats/${chatId}/messages`, {
      method: 'POST',
      body: JSON.stringify(messageData),
    });
    return response.message;
  }

  /**
   * Get messages with simplified interface
   */
  async getMessages(chatId: string): Promise<{ messages: Message[] }> {
    return this.getChatMessages(chatId);
  }

  /**
   * Mark chat as read (alias for compatibility)
   */
  async markChatAsRead(chatId: string): Promise<void> {
    await this.markMessagesRead(chatId);
  }

  /**
   * Mark message as read (alias for compatibility)
   */
  async markMessageAsRead(messageId: string): Promise<void> {
    await this.markMessageRead(messageId);
  }
}

export const ChatAPI = new ChatAPIService();
export type { Chat, Message, CreateChatRequest, SendMessageRequest, GetChatMessagesRequest };
