// CTRON Home - Technician API
// API calls for technician discovery and management

import axios from 'axios';
import { getAuthToken } from '../utils/auth.utils';
import { API_BASE_URL } from '../config/api.config';

export interface TechnicianParams {
  lat?: number;
  lng?: number;
  minRating?: number;
  specialization?: string;
  maxDistance?: number;
}

export interface Technician {
  id: string;
  fullName: string;
  specialization: string;
  rating: number;
  distance: number;
  isAvailable: boolean;
  hourlyRate?: number;
  completedJobs?: number;
  phone?: string;
  latitude?: number;
  longitude?: number;
}

export interface TechniciansResponse {
  technicians: Technician[];
  total: number;
}

// Helper function to make authenticated API calls
const makeAuthenticatedRequest = async (method: string, endpoint: string, data?: any, params?: any) => {
  const token = await getAuthToken();
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    ...(data && { data }),
    ...(params && { params }),
  };

  return axios(config);
};

export const TechnicianAPI = {
  onboard: async (specialization: string) => {
    const res = await makeAuthenticatedRequest('POST', '/api/technicians/onboard', { specialization });
    return res.data;
  },

  getDashboard: async () => {
    const res = await makeAuthenticatedRequest('GET', '/api/technicians/dashboard');
    return res.data;
  },

  updateAvailability: async (isAvailable: boolean) => {
    const res = await makeAuthenticatedRequest('PATCH', '/api/technicians/availability', { isAvailable });
    return res.data;
  },

  getProfile: async () => {
    const res = await makeAuthenticatedRequest('GET', '/api/technicians/profile');
    return res.data;
  },

  /**
   * Get nearby technicians based on location and filters
   */
  getNearbyTechnicians: async (params: TechnicianParams): Promise<TechniciansResponse> => {
    const response = await makeAuthenticatedRequest('GET', '/api/technicians/nearby', undefined, params);
    return response.data;
  },

  /**
   * Get technician profile by ID
   */
  getTechnicianProfile: async (technicianId: string): Promise<Technician> => {
    const response = await makeAuthenticatedRequest('GET', `/api/technicians/${technicianId}`);
    return response.data.technician;
  },

  /**
   * Get technician availability
   */
  getTechnicianAvailability: async (technicianId: string): Promise<{ isAvailable: boolean; nextAvailable?: string }> => {
    const response = await makeAuthenticatedRequest('GET', `/api/technicians/${technicianId}/availability`);
    return response.data;
  },

  /**
   * Find technicians (alias for getNearbyTechnicians for backward compatibility)
   */
  findTechnicians: async (params: TechnicianParams): Promise<TechniciansResponse> => {
    return TechnicianAPI.getNearbyTechnicians(params);
  },

  createVerificationSession: async (technicianId: string) => {
    const res = await makeAuthenticatedRequest('POST', '/api/technicians/kyc/start', { technicianId });
    return res.data;
  },
};
