// src/api/uploadService.ts

import axios from 'axios';
import { getAuthToken } from '../utils/auth.utils';
import { API_BASE_URL } from '../config/api.config';

interface PresignRequestBody {
  fileType: string;
}

interface PresignResponse {
  url: string;
  key: string;
}

export const getPresignedUrl = async (body: PresignRequestBody): Promise<PresignResponse> => {
  const token = await getAuthToken();
  const response = await axios.post<PresignResponse>(`${API_BASE_URL}/api/uploads/presign`, body, {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });
  return response.data;
};
