/**
 * LEGACY SEND ACCESSIBILITY EVENT
 * Direct polyfill for the missing React Native accessibility module
 * This file provides the exact module that ReactNativePrivateInterface is looking for
 */

console.log('♿ LEGACY ACCESSIBILITY: Loading legacySendAccessibilityEvent...');

// Legacy send accessibility event function
function legacySendAccessibilityEvent(reactTag, eventType, eventData) {
  if (typeof window !== 'undefined' && window.document) {
    // Web environment - use DOM accessibility APIs
    try {
      // Find the element by React tag
      const element = window.document.querySelector(`[data-reactroot] [data-tag="${reactTag}"]`) ||
                     window.document.querySelector(`[data-reactid="${reactTag}"]`) ||
                     window.document.getElementById(`react-${reactTag}`);
      
      if (element) {
        // Dispatch appropriate accessibility event based on eventType
        switch (eventType) {
          case 'focus':
            element.focus();
            break;
          case 'click':
            element.click();
            break;
          case 'announcement':
            // Create ARIA live region for announcements
            let liveRegion = window.document.getElementById('rn-accessibility-live-region');
            if (!liveRegion) {
              liveRegion = window.document.createElement('div');
              liveRegion.id = 'rn-accessibility-live-region';
              liveRegion.setAttribute('aria-live', 'polite');
              liveRegion.setAttribute('aria-atomic', 'true');
              liveRegion.style.position = 'absolute';
              liveRegion.style.left = '-10000px';
              liveRegion.style.width = '1px';
              liveRegion.style.height = '1px';
              liveRegion.style.overflow = 'hidden';
              window.document.body.appendChild(liveRegion);
            }
            liveRegion.textContent = eventData?.announcement || 'Accessibility event';
            break;
          default:
            // Generic accessibility event
            const event = new CustomEvent('accessibility', {
              detail: { eventType, eventData, reactTag },
              bubbles: true
            });
            element.dispatchEvent(event);
        }
        
        console.log('♿ LEGACY ACCESSIBILITY: Dispatched web accessibility event:', eventType, 'for tag:', reactTag);
      } else {
        console.warn('♿ LEGACY ACCESSIBILITY: Element not found for tag:', reactTag);
      }
    } catch (error) {
      console.warn('♿ LEGACY ACCESSIBILITY: Failed to dispatch web accessibility event:', error);
    }
  } else {
    // Native environment - log for debugging (actual implementation handled by native code)
    console.log('♿ LEGACY ACCESSIBILITY: Native accessibility event:', eventType, 'for tag:', reactTag);
  }
}

// Install globally for React Native compatibility
if (typeof global !== 'undefined') {
  if (!global.legacySendAccessibilityEvent) {
    global.legacySendAccessibilityEvent = legacySendAccessibilityEvent;
    console.log('♿ LEGACY ACCESSIBILITY: legacySendAccessibilityEvent installed globally');
  }
}

// Export as both default and named export for maximum compatibility
module.exports = legacySendAccessibilityEvent;
module.exports.default = legacySendAccessibilityEvent;
module.exports.legacySendAccessibilityEvent = legacySendAccessibilityEvent;

// ES6 export for modern bundlers
if (typeof exports !== 'undefined') {
  exports.default = legacySendAccessibilityEvent;
  exports.legacySendAccessibilityEvent = legacySendAccessibilityEvent;
}

console.log('♿ LEGACY ACCESSIBILITY: legacySendAccessibilityEvent module loaded');
