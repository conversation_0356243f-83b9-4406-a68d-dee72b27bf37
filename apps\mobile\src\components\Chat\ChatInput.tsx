// apps/mobile/src/components/Chat/ChatInput.tsx

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { designSystem } from '../../styles/designSystem';

const { colors, spacing, typography, borderRadius, shadows } = designSystem;

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  onTyping,
  disabled = false,
  placeholder = 'Type a message...',
  maxLength = 2000,
}) => {
  const [message, setMessage] = useState('');
  const [inputHeight, setInputHeight] = useState(44);
  const textInputRef = useRef<TextInput>(null);
  const sendButtonScale = useRef(new Animated.Value(1)).current;

  const handleSendMessage = () => {
    const trimmedMessage = message.trim();
    
    if (!trimmedMessage || disabled) {
      return;
    }

    if (trimmedMessage.length > maxLength) {
      Alert.alert(
        'Message Too Long',
        `Message cannot exceed ${maxLength} characters.`,
        [{ text: 'OK' }]
      );
      return;
    }

    // Animate send button
    Animated.sequence([
      Animated.timing(sendButtonScale, {
        toValue: 0.8,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(sendButtonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    onSendMessage(trimmedMessage);
    setMessage('');
    setInputHeight(44);
  };

  const handleTextChange = (text: string) => {
    setMessage(text);
    onTyping?.();
  };

  const handleContentSizeChange = (event: any) => {
    const { height } = event.nativeEvent.contentSize;
    const newHeight = Math.min(Math.max(44, height + 20), 120);
    setInputHeight(newHeight);
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        {/* Text Input */}
        <View style={[styles.textInputContainer, { height: inputHeight }]}>
          <TextInput
            ref={textInputRef}
            style={[styles.textInput, { height: inputHeight - 12 }]}
            placeholder={placeholder}
            placeholderTextColor={colors.secondaryLabel}
            value={message}
            onChangeText={handleTextChange}
            onContentSizeChange={handleContentSizeChange}
            multiline
            maxLength={maxLength}
            editable={!disabled}
            textAlignVertical="top"
            returnKeyType="default"
            blurOnSubmit={false}
          />
        </View>

        {/* Send Button */}
        <Animated.View style={[styles.sendButtonContainer, { transform: [{ scale: sendButtonScale }] }]}>
          <TouchableOpacity
            style={[
              styles.sendButton,
              canSend ? styles.sendButtonActive : styles.sendButtonInactive,
            ]}
            onPress={handleSendMessage}
            disabled={!canSend}
            activeOpacity={0.8}
          >
            <Ionicons
              name="send"
              size={20}
              color={canSend ? colors.white : colors.secondaryLabel}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Character Counter */}
      {message.length > maxLength * 0.8 && (
        <View style={styles.characterCountContainer}>
          <Text
            style={[
              styles.characterCount,
              message.length > maxLength ? styles.characterCountError : null,
            ]}
          >
            {message.length}/{maxLength}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 40,
    maxHeight: 120,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  textInput: {
    ...typography.body,
    color: colors.label,
    textAlignVertical: 'top',
  },
  sendButtonContainer: {
    marginBottom: 2,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sendButtonActive: {
    backgroundColor: colors.primary.main,
  },
  sendButtonInactive: {
    backgroundColor: colors.secondarySystemBackground,
  },
  characterCountContainer: {
    alignItems: 'flex-end',
    marginTop: spacing.xs,
  },
  characterCount: {
    ...typography.caption1,
    color: colors.secondaryLabel,
  },
  characterCountError: {
    color: colors.destructive,
  },
});
