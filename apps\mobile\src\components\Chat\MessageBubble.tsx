// apps/mobile/src/components/Chat/MessageBubble.tsx

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors, spacing, typography } from '../../styles/theme';
import { getPrimaryColor, getBackgroundColor } from '../../utils/colorUtils';

interface Message {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  timestamp: string;
  isRead: boolean;
  messageType: 'TEXT' | 'IMAGE' | 'SYSTEM';
}

interface MessageBubbleProps {
  message: Message;
  isOwnMessage: boolean;
  showReadStatus?: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwnMessage,
  showReadStatus = false,
  onPress,
  onLongPress,
}) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderSystemMessage = () => (
    <View style={styles.systemMessageContainer}>
      <Text style={styles.systemMessageText}>{message.content}</Text>
    </View>
  );

  const renderTextMessage = () => (
    <TouchableOpacity
      style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer,
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.8}
    >
      <View
        style={[
          styles.messageBubble,
          isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble,
        ]}
      >
        {/* Sender name for other messages */}
        {!isOwnMessage && (
          <Text style={styles.senderName}>{message.senderName}</Text>
        )}

        {/* Message content */}
        <Text
          style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
          ]}
        >
          {message.content}
        </Text>

        {/* Message footer with time and read status */}
        <View style={styles.messageFooter}>
          <Text
            style={[
              styles.messageTime,
              isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime,
            ]}
          >
            {formatTime(message.timestamp)}
          </Text>

          {/* Read status for own messages */}
          {isOwnMessage && showReadStatus && (
            <View style={styles.readStatusContainer}>
              <Text style={styles.readStatus}>
                {message.isRead ? '✓✓' : '✓'}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Message tail */}
      <View
        style={[
          styles.messageTail,
          isOwnMessage ? styles.ownMessageTail : styles.otherMessageTail,
        ]}
      />
    </TouchableOpacity>
  );

  if (message.messageType === 'SYSTEM') {
    return renderSystemMessage();
  }

  return renderTextMessage();
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: spacing.xs,
    marginHorizontal: spacing.md,
    maxWidth: '80%',
  },
  ownMessageContainer: {
    alignSelf: 'flex-end',
  },
  otherMessageContainer: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 18,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  ownMessageBubble: {
    backgroundColor: '#007AFF', // iOS blue for modern look
    marginRight: spacing.sm,
    borderBottomRightRadius: 4, // Tail effect
  },
  otherMessageBubble: {
    backgroundColor: '#E5E5EA', // Light gray for contrast
    marginLeft: spacing.sm,
    borderBottomLeftRadius: 4, // Tail effect
  },
  senderName: {
    ...typography.caption1,
    color: colors.secondaryLabel,
    marginBottom: spacing.xs,
    fontWeight: '600',
  },
  messageText: {
    ...typography.body,
    lineHeight: 20,
  },
  ownMessageText: {
    color: colors.white,
  },
  otherMessageText: {
    color: colors.label,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  messageTime: {
    ...typography.caption2,
    fontSize: 11,
  },
  ownMessageTime: {
    color: colors.white,
    opacity: 0.8,
  },
  otherMessageTime: {
    color: colors.secondaryLabel,
  },
  readStatusContainer: {
    marginLeft: spacing.xs,
  },
  readStatus: {
    ...typography.caption2,
    fontSize: 11,
    color: colors.white,
    opacity: 0.8,
  },
  messageTail: {
    position: 'absolute',
    bottom: spacing.md,
    width: 0,
    height: 0,
    borderStyle: 'solid',
  },
  ownMessageTail: {
    right: -spacing.xs,
    borderLeftWidth: spacing.sm,
    borderRightWidth: 0,
    borderTopWidth: spacing.sm,
    borderBottomWidth: 0,
    borderLeftColor: getPrimaryColor(),
    borderRightColor: 'transparent',
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  otherMessageTail: {
    left: -spacing.xs,
    borderLeftWidth: 0,
    borderRightWidth: spacing.sm,
    borderTopWidth: spacing.sm,
    borderBottomWidth: 0,
    borderLeftColor: 'transparent',
    borderRightColor: getBackgroundColor('secondary'),
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  systemMessageContainer: {
    alignSelf: 'center',
    backgroundColor: colors.secondarySystemBackground,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    marginVertical: spacing.sm,
  },
  systemMessageText: {
    ...typography.caption1,
    color: colors.secondaryLabel,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
