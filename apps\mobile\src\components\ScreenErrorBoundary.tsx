// CTRON Home - Screen Error Boundary
// Specialized error boundary for individual screens

import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../styles/theme';
import { getPrimaryColor, getErrorColor, getTextColor, getBackgroundColor } from '../utils/colorUtils';
import { debugLogger } from '../utils/debugLogger';

interface Props {
  children: ReactNode;
  screenName?: string;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class ScreenErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error for debugging
    debugLogger.error(`Screen Error in ${this.props.screenName || 'Unknown Screen'}:`, {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // In production, send to error tracking service
    if (!__DEV__) {
      // Example: Sentry.captureException(error, { contexts: { react: errorInfo } });
      console.error('Production error captured:', error);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });

    // Call custom retry handler if provided
    this.props.onRetry?.();
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error screen
      return (
        <View style={styles.container}>
          <View style={styles.errorContainer}>
            <Ionicons 
              name="warning-outline" 
              size={64} 
              color={getErrorColor()} 
              style={styles.errorIcon}
            />
            
            <Text style={styles.errorTitle}>
              Oops! Something went wrong
            </Text>
            
            <Text style={styles.errorMessage}>
              {this.props.screenName 
                ? `There was an error loading the ${this.props.screenName} screen.`
                : 'There was an unexpected error.'
              }
            </Text>

            {__DEV__ && this.state.error && (
              <View style={styles.debugContainer}>
                <Text style={styles.debugTitle}>Debug Information:</Text>
                <Text style={styles.debugText}>
                  {this.state.error.message}
                </Text>
                {this.state.error.stack && (
                  <Text style={styles.debugStack} numberOfLines={10}>
                    {this.state.error.stack}
                  </Text>
                )}
              </View>
            )}

            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={styles.retryButton} 
                onPress={this.handleRetry}
                activeOpacity={0.8}
              >
                <Ionicons name="refresh" size={20} color="white" />
                <Text style={styles.retryButtonText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getBackgroundColor('primary'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorContainer: {
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
  },
  errorIcon: {
    marginBottom: spacing.lg,
  },
  errorTitle: {
    ...typography.heading,
    fontSize: 24,
    fontWeight: 'bold',
    color: getTextColor('primary'),
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  errorMessage: {
    ...typography.body,
    color: getTextColor('secondary'),
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  debugContainer: {
    backgroundColor: '#f5f5f5',
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.lg,
    width: '100%',
    maxHeight: 200,
  },
  debugTitle: {
    ...typography.subheading,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: spacing.sm,
  },
  debugText: {
    ...typography.caption,
    color: getErrorColor(),
    marginBottom: spacing.sm,
    fontFamily: 'monospace',
  },
  debugStack: {
    ...typography.caption,
    fontSize: 10,
    color: '#666',
    fontFamily: 'monospace',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  retryButton: {
    backgroundColor: getPrimaryColor(),
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  retryButtonText: {
    ...typography.button,
    color: 'white',
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
});

// Convenience wrapper for screens
export const withScreenErrorBoundary = (
  WrappedComponent: React.ComponentType<any>,
  screenName?: string
) => {
  return (props: any) => (
    <ScreenErrorBoundary screenName={screenName}>
      <WrappedComponent {...props} />
    </ScreenErrorBoundary>
  );
};

export default ScreenErrorBoundary;
