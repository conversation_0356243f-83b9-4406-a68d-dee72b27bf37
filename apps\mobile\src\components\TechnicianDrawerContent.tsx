// src/components/TechnicianDrawerContent.tsx
import React from 'react';
import { DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useJobs } from '../context/JobContext';

export default function TechnicianDrawerContent(props: any) {
  const { user, logout } = useAuth();
  const { assignedJobs } = useJobs();

  const now = new Date();
  const thisMonthCompleted = assignedJobs.filter(
    (job) => 
      job.status === 'COMPLETED' &&
      new Date(job.scheduledAt).getMonth() === now.getMonth() &&
      new Date(job.scheduledAt).getFullYear() === now.getFullYear()
  );

  const monthlyEarnings = thisMonthCompleted.length * 50; // Assume £50/job

  return (
    <DrawerContentScrollView {...props}>
      <View style={styles.header}>
        <Image
          source={{ uri: 'https://i.pravatar.cc/100?u=' + user?.userId }}
          style={styles.avatar}
        />
        <Text style={styles.name}>{user?.fullName}</Text>
        <Text style={styles.verification}>✅ Verified Technician</Text>

        <View style={styles.balanceBox}>
          <Text style={styles.balanceLabel}>Earnings (This Month)</Text>
          <Text style={styles.balanceValue}>£{monthlyEarnings}</Text>
        </View>
      </View>

      {/* Drawer Links */}
      <DrawerItemList {...props} />

      {/* Footer */}
      <TouchableOpacity style={styles.logoutButton} onPress={() => logout()}>
        <Text style={styles.logoutText}>Log Out</Text>
      </TouchableOpacity>
    </DrawerContentScrollView>
  );
}

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderColor: '#eee',
    marginBottom: 12,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ddd',
    marginBottom: 12,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  verification: {
    color: '#4CAF50',
    marginTop: 4,
  },
  balanceBox: {
    marginTop: 12,
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 12,
    color: '#888',
  },
  balanceValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  logoutButton: {
    marginTop: 20,
    marginHorizontal: 16,
    padding: 12,
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutText: {
    color: 'white',
    fontWeight: 'bold',
  },
});
