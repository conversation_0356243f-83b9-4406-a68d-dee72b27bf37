// src/components/TechnicianListItem.tsx

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Technician } from '../types/technician';

interface Props {
  technician: Technician;
  onPress: () => void;
}

const TechnicianListItem = ({ technician, onPress }: Props) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Text style={styles.name}>🔧 {technician.specialization}</Text>
      <Text style={styles.meta}>Rating: {technician.rating.toFixed(1)}</Text>
      <Text style={styles.meta}>Available: {technician.isAvailable ? 'Yes' : 'No'}</Text>
    </TouchableOpacity>
  );
};

export default TechnicianListItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f7f7f7',
    borderRadius: 8,
    padding: 14,
    marginVertical: 6,
  },
  name: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  meta: {
    color: '#555',
  },
});
