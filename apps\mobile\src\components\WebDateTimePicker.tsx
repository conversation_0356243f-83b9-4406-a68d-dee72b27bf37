import React from 'react';
import { Platform, TextInputProps } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';

const WebDateTimePicker: React.FC<TextInputProps & { value: Date; onChange: (d: Date) => void }> = ({ value, onChange, ...rest }) => {
  if (Platform.OS === 'web') {
    return (
      <input
        type="datetime-local"
        value={value.toISOString().slice(0, 16)}
        onChange={(e) => onChange(new Date(e.target.value))}
        {...(rest as any)}
      />
    );
  }
  return (
    <DateTimePicker value={value} onChange={(_: any, d: Date | undefined) => d && onChange(d)} {...(rest as any)} />
  );
};
export default WebDateTimePicker;