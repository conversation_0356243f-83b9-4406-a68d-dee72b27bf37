// CTRON Home Design System - Bottom Navigation Component
// Mobile navigation with design system styling

import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { colors, spacing, typography, borderRadius, shadows } from '../../styles/theme';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
}

export interface BottomNavigationProps {
  items: NavigationItem[];
  activeTab: string;
  onTabPress: (tabId: string) => void;
  style?: any;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  items,
  activeTab,
  onTabPress,
  style,
}) => {
  return (
    <SafeAreaView style={[styles.safeArea, style]}>
      <View style={styles.container}>
        {items.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.navItem,
              activeTab === item.id && styles.navItemActive,
            ]}
            onPress={() => onTabPress(item.id)}
            activeOpacity={0.7}
            accessibilityRole="tab"
            accessibilityLabel={item.label}
            accessibilityState={{ selected: activeTab === item.id }}
          >
            <View style={styles.iconContainer}>
              {item.icon}
              {item.badge && item.badge > 0 && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {item.badge > 99 ? '99+' : item.badge.toString()}
                  </Text>
                </View>
              )}
            </View>
            <Text
              style={[
                styles.navLabel,
                activeTab === item.id && styles.navLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: colors.systemBackground,
  },

  container: {
    flexDirection: 'row',
    backgroundColor: colors.systemBackground,
    borderTopWidth: 1,
    borderTopColor: colors.separator,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
  },

  navItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.xs,
    borderRadius: borderRadius.md,
    minHeight: 44, // Touch-friendly minimum
  },

  navItemActive: {
    backgroundColor: `${colors.primary.main}15`, // 15% opacity
  },

  iconContainer: {
    position: 'relative',
    marginBottom: spacing.xs,
  },

  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: colors.secondary.main,
    borderRadius: borderRadius.full,
    minWidth: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xs,
  },

  badgeText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: '700',
  },

  navLabel: {
    ...typography.caption2,
    fontWeight: '500',
    color: colors.tertiaryLabel,
    textAlign: 'center',
  },

  navLabelActive: {
    color: colors.primary.main,
    fontWeight: typography.fontWeight.semibold,
  },
});

export default BottomNavigation;
