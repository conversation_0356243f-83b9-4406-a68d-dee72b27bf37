// CTRON Home Design System - Card Component
// Flexible card component for jobs, technicians, and content display

import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';
import { lightTheme } from '../../styles/theme';

const { colors, spacing, borderRadius, shadows } = lightTheme;

export interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'job' | 'technician';
  padding?: number;
  margin?: number;
  onPress?: () => void;
  style?: any;
  testID?: string;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = spacing.md,
  margin = 0,
  onPress,
  style,
  testID,
  ...touchableProps
}) => {
  const cardStyles = [
    styles.base,
    styles[variant],
    { padding },
    margin && { margin },
    style,
  ];

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyles as any}
        onPress={onPress}
        activeOpacity={0.95}
        testID={testID}
        accessibilityRole="button"
        {...touchableProps}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyles as any} testID={testID}>
      {children}
    </View>
  );
};

// Job Card Specific Component
export interface JobCardProps extends CardProps {
  status?: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled';
  priority?: 'low' | 'medium' | 'high' | 'emergency';
}

export const JobCard: React.FC<JobCardProps> = ({
  children,
  status,
  priority,
  style,
  ...props
}) => {
  const jobCardStyles = [
    styles.jobCard,
    status && styles[`status_${status}`],
    priority === 'emergency' && styles.emergencyBorder,
    style,
  ];

  return (
    <Card variant="job" style={jobCardStyles} {...props}>
      {children}
    </Card>
  );
};

// Technician Card Specific Component
export interface TechnicianCardProps extends CardProps {
  verified?: boolean;
  available?: boolean;
}

export const TechnicianCard: React.FC<TechnicianCardProps> = ({
  children,
  verified,
  available,
  style,
  ...props
}) => {
  const technicianCardStyles = [
    styles.technicianCard,
    verified && styles.verifiedCard,
    available && styles.availableCard,
    style,
  ];

  return (
    <Card variant="technician" style={technicianCardStyles} {...props}>
      {children}
    </Card>
  );
};

const styles = StyleSheet.create({
  base: {
    backgroundColor: colors.systemBackground,
    borderRadius: borderRadius.lg,
    overflow: 'hidden',
  },

  // Variants
  default: {
    borderWidth: 1,
    borderColor: colors.separator,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
  },

  elevated: {
    backgroundColor: colors.secondarySystemBackground,
    ...shadows.md,
    borderWidth: 1,
    borderColor: colors.separator,
  },

  outlined: {
    borderWidth: 1,
    borderColor: colors.separator,
    backgroundColor: colors.systemBackground,
  },

  job: {
    backgroundColor: colors.systemBackground,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
    borderWidth: 1,
    borderColor: colors.separator,
    marginBottom: spacing.md,
  },

  technician: {
    backgroundColor: colors.systemBackground,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
    borderWidth: 1,
    borderColor: colors.separator,
    position: 'relative',
  },

  // Job Status Styles
  status_pending: {
    borderLeftWidth: 4,
    borderLeftColor: colors.warning.main,
  },

  status_assigned: {
    borderLeftWidth: 4,
    borderLeftColor: colors.info.main,
  },

  status_active: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary.main,
  },

  status_completed: {
    borderLeftWidth: 4,
    borderLeftColor: colors.success.main,
  },

  status_cancelled: {
    borderLeftWidth: 4,
    borderLeftColor: colors.error.main,
  },

  emergencyBorder: {
    borderWidth: 2,
    borderColor: colors.error.main,
    ...shadows.lg,
  },

  // Technician Card Styles
  verifiedCard: {
    borderTopWidth: 4,
    borderTopColor: colors.success.main,
  },

  availableCard: {
    backgroundColor: colors.secondarySystemBackground,
  },

  jobCard: {
    // Additional job-specific styles can be added here
  },

  technicianCard: {
    // Additional technician-specific styles can be added here
  },
});

export default Card;
