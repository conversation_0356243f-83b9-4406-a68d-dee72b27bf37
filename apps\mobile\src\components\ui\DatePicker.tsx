// CTRON Home Design System - Date Picker Component
// Cross-platform date picker with fallback support

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Platform,
  Alert,
} from 'react-native';
import { colors, spacing, typography, borderRadius } from '../../styles/theme';

// DateTimePicker configuration with fallback support
let DateTimePicker: any = null;
let hasDateTimePicker = false;

try {
  const DateTimePickerModule = require('@react-native-community/datetimepicker');
  DateTimePicker = DateTimePickerModule.default || DateTimePickerModule;
  hasDateTimePicker = true;
} catch (err) {
  if (__DEV__) {
    console.log('DateTimePicker not available, using text input fallback');
  }
  hasDateTimePicker = false;
}

export interface DatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'date' | 'time' | 'datetime';
  placeholder?: string;
  label?: string;
  style?: any;
  disabled?: boolean;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  minimumDate,
  maximumDate,
  mode = 'datetime',
  placeholder = 'Select date and time',
  label,
  style,
  disabled = false,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [textValue, setTextValue] = useState(value.toLocaleString());

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // Handle platform-specific behavior
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (event?.type === 'dismissed') {
      setShowPicker(false);
      return;
    }

    if (selectedDate) {
      // Validate date range
      if (minimumDate && selectedDate < minimumDate) {
        Alert.alert('Invalid Date', 'Please select a date in the future');
        return;
      }

      if (maximumDate && selectedDate > maximumDate) {
        Alert.alert('Invalid Date', 'Please select an earlier date');
        return;
      }

      onChange(selectedDate);
      setTextValue(selectedDate.toLocaleString());

      if (Platform.OS === 'ios') {
        setShowPicker(false);
      }
    }
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);

    // Try to parse the text as a date with multiple formats
    let parsedDate: Date | null = null;

    // Try different date formats
    const formats = [
      text, // Direct parsing
      text.replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/, '$3-$1-$2'), // MM/DD/YYYY to YYYY-MM-DD
      text.replace(/(\d{1,2})-(\d{1,2})-(\d{4})/, '$3-$1-$2'), // MM-DD-YYYY to YYYY-MM-DD
      text.replace(/(\d{4})\/(\d{1,2})\/(\d{1,2})/, '$1-$2-$3'), // YYYY/MM/DD to YYYY-MM-DD
    ];

    for (const format of formats) {
      const testDate = new Date(format);
      if (!isNaN(testDate.getTime())) {
        parsedDate = testDate;
        break;
      }
    }

    if (parsedDate) {
      // Validate parsed date
      if (minimumDate && parsedDate < minimumDate) {
        return;
      }
      if (maximumDate && parsedDate > maximumDate) {
        return;
      }
      onChange(parsedDate);
    }
  };

  const openPicker = () => {
    if (disabled) return;
    setShowPicker(true);
  };

  const formatDisplayValue = () => {
    if (mode === 'date') {
      return value.toLocaleDateString();
    } else if (mode === 'time') {
      return value.toLocaleTimeString();
    } else {
      return value.toLocaleString();
    }
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}

      {hasDateTimePicker ? (
        <>
          <TouchableOpacity
            style={[styles.dateButton, disabled && styles.disabled]}
            onPress={openPicker}
            disabled={disabled}
            accessibilityRole="button"
            accessibilityLabel={`Select ${mode}, current value: ${formatDisplayValue()}`}
          >
            <Text style={[styles.dateText, disabled && styles.disabledText]}>
              {formatDisplayValue()}
            </Text>
            <Text style={styles.chevron}>▼</Text>
          </TouchableOpacity>

          {showPicker && Platform.OS !== 'web' && (
            <DateTimePicker
              testID="dateTimePicker"
              value={value}
              mode={mode}
              is24Hour={true}
              display={Platform.OS === 'ios' ? 'default' : 'default'}
              onChange={handleDateChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
            />
          )}
        </>
      ) : (
        // Fallback to text input with better formatting
        <View>
          <TextInput
            style={[styles.textInput, disabled && styles.disabled]}
            value={textValue}
            onChangeText={handleTextChange}
            placeholder={mode === 'date' ? 'MM/DD/YYYY' : mode === 'time' ? 'HH:MM AM/PM' : 'MM/DD/YYYY HH:MM AM/PM'}
            placeholderTextColor={colors.text.tertiary}
            editable={!disabled}
            accessibilityLabel={label || 'Date input'}
          />
          <Text style={styles.helpText}>
            {mode === 'date'
              ? 'Enter date as MM/DD/YYYY (e.g., 12/25/2024)'
              : mode === 'time'
              ? 'Enter time as HH:MM AM/PM (e.g., 2:30 PM)'
              : 'Enter date and time (e.g., 12/25/2024 2:30 PM)'
            }
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },

  label: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },

  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    backgroundColor: colors.background.primary,
    minHeight: 48,
  },

  dateText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    flex: 1,
  },

  chevron: {
    fontSize: typography.fontSize.sm,
    color: colors.text.tertiary,
    marginLeft: spacing.sm,
  },

  textInput: {
    borderWidth: 2,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
    minHeight: 48,
  },

  disabled: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[300],
  },

  disabledText: {
    color: colors.gray[500],
  },

  helpText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
});

export default DatePicker;
