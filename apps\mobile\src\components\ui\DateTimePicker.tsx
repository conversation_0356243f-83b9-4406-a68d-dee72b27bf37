import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';
import { lightTheme as theme } from '../../styles/theme';

// Platform-aware DateTimePicker import to prevent Android bundling issues
let RNDateTimePicker: any;
try {
  RNDateTimePicker = require('@react-native-community/datetimepicker').default;
} catch (error) {
  // Fallback for platforms where DateTimePicker might not be available
  RNDateTimePicker = ({ value, onChange }: any) => {
    // Simple fallback - just call onChange with current value
    React.useEffect(() => {
      if (Platform.OS === 'web') {
        // For web, we could use HTML5 date input
        console.log('DateTimePicker: Using web fallback');
      }
    }, []);
    return null;
  };
}

const { colors, spacing, typography, borderRadius } = theme;

interface DateTimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  minimumDate?: Date;
  maximumDate?: Date;
  style?: any;
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  value,
  onChange,
  mode = 'datetime',
  minimumDate,
  maximumDate,
  style,
}) => {
  const [show, setShow] = useState(false);
  const [currentMode, setCurrentMode] = useState<'date' | 'time'>('date');

  const handleChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || value;
    
    if (Platform.OS === 'android') {
      setShow(false);
    }
    
    if (mode === 'datetime' && currentMode === 'date' && Platform.OS === 'android') {
      // On Android, show time picker after date is selected
      setCurrentMode('time');
      setShow(true);
    } else {
      setCurrentMode('date');
    }
    
    onChange(currentDate);
  };

  const showDatePicker = () => {
    setCurrentMode('date');
    setShow(true);
  };

  const showTimePicker = () => {
    setCurrentMode('time');
    setShow(true);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDateTime = (date: Date) => {
    return `${formatDate(date)} at ${formatTime(date)}`;
  };

  const getDisplayText = () => {
    switch (mode) {
      case 'date':
        return formatDate(value);
      case 'time':
        return formatTime(value);
      case 'datetime':
        return formatDateTime(value);
      default:
        return formatDateTime(value);
    }
  };

  return (
    <View style={[styles.container, style]}>
      {mode === 'datetime' ? (
        <View style={styles.dateTimeContainer}>
          <TouchableOpacity
            style={[styles.picker, styles.datePicker]}
            onPress={showDatePicker}
          >
            <Text style={styles.label}>Date</Text>
            <Text style={styles.value}>{formatDate(value)}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.picker, styles.timePicker]}
            onPress={showTimePicker}
          >
            <Text style={styles.label}>Time</Text>
            <Text style={styles.value}>{formatTime(value)}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.picker}
          onPress={() => setShow(true)}
        >
          <Text style={styles.value}>{getDisplayText()}</Text>
        </TouchableOpacity>
      )}

      {show && (
        <RNDateTimePicker
          value={value}
          mode={mode === 'datetime' ? currentMode : mode}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleChange}
          minimumDate={minimumDate}
          maximumDate={maximumDate}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.sm,
  },

  dateTimeContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },

  picker: {
    backgroundColor: colors.background.primary,
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    minHeight: 56,
    justifyContent: 'center',
  },

  datePicker: {
    flex: 2,
  },

  timePicker: {
    flex: 1,
  },

  label: {
    fontSize: typography.fontSize.sm,
    fontWeight: '600' as const,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },

  value: {
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    fontWeight: '500' as const,
  },
});
