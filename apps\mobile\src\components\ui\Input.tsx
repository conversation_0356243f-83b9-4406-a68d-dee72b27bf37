// CTRON Home - Enhanced Input Component
// Modern input component with design system integration

import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TextInputProps } from 'react-native';
import { lightTheme } from '../../styles/theme';

const { colors, spacing, typography, borderRadius, shadows, sizes } = lightTheme;

interface InputProps extends TextInputProps {
  label?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
}

export const Input: React.FC<InputProps> = ({
  label,
  required = false,
  error,
  hint,
  variant = 'default',
  size = 'medium',
  style,
  onFocus,
  onBlur,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const inputStyles = [
    styles.input,
    styles[variant],
    styles[`size_${size}`],
    isFocused && styles.focused,
    error && styles.inputError,
    style,
  ].filter(Boolean) as any;

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      <TextInput
        style={inputStyles}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholderTextColor={colors.tertiaryLabel}
        selectionColor={colors.primary.main}
        {...props}
      />
      {error && <Text style={styles.errorText}>{error}</Text>}
      {hint && !error && <Text style={styles.hintText}>{hint}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },

  label: {
    ...typography.subheadline,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },

  required: {
    color: colors.error.main,
  },

  input: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.body,
    color: colors.text.primary,
    minHeight: sizes.touchTarget,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
  },

  // Variants
  default: {
    backgroundColor: colors.secondarySystemBackground,
    borderColor: colors.separator,
  },

  outlined: {
    backgroundColor: colors.systemBackground,
    borderColor: colors.separator,
  },

  filled: {
    backgroundColor: colors.secondarySystemBackground,
    borderColor: 'transparent',
  },

  // Sizes
  size_small: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    minHeight: sizes.inputHeight.sm,
    ...typography.footnote,
  },

  size_medium: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 44,
    ...typography.body,
  },

  size_large: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: sizes.inputHeight.lg,
    ...typography.callout,
  },

  // States
  focused: {
    borderColor: colors.primary.main,
    borderWidth: 2,
    ...shadows.md,
  },

  inputError: {
    borderColor: colors.error.main,
    borderWidth: 2,
  },

  errorText: {
    ...typography.caption1,
    color: colors.error.main,
    marginTop: spacing.xs,
  },

  hintText: {
    ...typography.caption1,
    color: colors.tertiaryLabel,
    marginTop: spacing.xs,
  },
});
