// CTRON Home Design System - Job Card Component
// Specialized card for displaying job information

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { colors, spacing, typography, borderRadius, shadows } from '../../styles/theme';
import { StatusBadge } from './StatusBadge';

export interface JobData {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' | 'overdue';
  priority?: 'low' | 'medium' | 'high' | 'emergency';
  scheduledAt?: string;
  location?: string;
  technician?: {
    name: string;
    avatar?: string;
    rating?: number;
  };
  estimatedDuration?: string;
  price?: number;
}

export interface JobCardProps {
  job: JobData;
  onPress?: () => void;
  style?: ViewStyle;
  showTechnician?: boolean;
  showPrice?: boolean;
}

export const JobCard: React.FC<JobCardProps> = ({
  job,
  onPress,
  style,
  showTechnician = true,
  showPrice = true,
}) => {
  const getStatusBorderColor = () => {
    const statusColors = {
      pending: colors.status.pending,
      assigned: colors.status.assigned,
      active: colors.status.active,
      completed: colors.status.completed,
      cancelled: colors.status.cancelled,
      overdue: colors.status.overdue,
    };
    return statusColors[job.status];
  };

  const getPriorityStyle = () => {
    if (job.priority === 'emergency') {
      return {
        borderWidth: 2,
        borderColor: colors.secondary[500],
        ...shadows.lg,
      };
    }
    return {};
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const cardStyles = [
    styles.container,
    {
      borderLeftColor: getStatusBorderColor(),
      borderLeftWidth: 4,
    },
    getPriorityStyle(),
    style,
  ];

  const CardContent = (
    <View style={cardStyles}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {job.title}
          </Text>
          {job.priority && (
            <View style={styles.priorityContainer}>
              <StatusBadge status={job.priority as any} variant="priority" size="sm" />
            </View>
          )}
        </View>
        <StatusBadge status={job.status} size="sm" />
      </View>

      {/* Description */}
      <Text style={styles.description} numberOfLines={2}>
        {job.description}
      </Text>

      {/* Details */}
      <View style={styles.details}>
        {job.scheduledAt && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Scheduled:</Text>
            <Text style={styles.detailValue}>{formatDate(job.scheduledAt)}</Text>
          </View>
        )}
        
        {job.location && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailValue} numberOfLines={1}>
              {job.location}
            </Text>
          </View>
        )}

        {job.estimatedDuration && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Duration:</Text>
            <Text style={styles.detailValue}>{job.estimatedDuration}</Text>
          </View>
        )}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        {showTechnician && job.technician && (
          <View style={styles.technicianInfo}>
            <Text style={styles.technicianName}>{job.technician.name}</Text>
            {job.technician.rating && (
              <Text style={styles.rating}>⭐ {job.technician.rating.toFixed(1)}</Text>
            )}
          </View>
        )}
        
        {showPrice && job.price && (
          <Text style={styles.price}>£{job.price.toFixed(2)}</Text>
        )}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.95}
        accessibilityRole="button"
        accessibilityLabel={`Job: ${job.title}, Status: ${job.status}`}
      >
        {CardContent}
      </TouchableOpacity>
    );
  }

  return CardContent;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.systemBackground,
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.separator,
    shadowColor: "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },

  titleContainer: {
    flex: 1,
    marginRight: spacing.sm,
  },

  title: {
    ...typography.title3,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  priorityContainer: {
    marginTop: spacing.xs,
  },

  description: {
    ...typography.body,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },

  details: {
    marginBottom: spacing.md,
  },

  detailItem: {
    flexDirection: 'row',
    marginBottom: spacing.xs,
  },

  detailLabel: {
    ...typography.footnote,
    fontWeight: '500',
    color: colors.tertiaryLabel,
    width: 80,
  },

  detailValue: {
    ...typography.footnote,
    color: colors.text.secondary,
    flex: 1,
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.separator,
  },

  technicianInfo: {
    flex: 1,
  },

  technicianName: {
    ...typography.footnote,
    fontWeight: '500',
    color: colors.text.primary,
  },

  rating: {
    ...typography.caption2,
    color: colors.tertiaryLabel,
    marginTop: spacing.xs,
  },

  price: {
    ...typography.title3,
    fontWeight: '700',
    color: colors.primary.main,
  },
});

export default JobCard;
