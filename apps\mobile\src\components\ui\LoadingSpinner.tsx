// src/components/ui/LoadingSpinner.tsx
import React, { memo } from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { colors, spacing, typography } from '../../styles/theme';
import { getPrimaryColor, getTextColor } from '../../utils/colorUtils';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  style?: any;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = memo(({
  size = 'large',
  color = getPrimaryColor(),
  text,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
      {text && <Text style={styles.text}>{text}</Text>}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  text: {
    marginTop: spacing.sm,
    fontSize: typography.fontSize.sm,
    color: getTextColor('secondary'),
    textAlign: 'center',
  },
});
