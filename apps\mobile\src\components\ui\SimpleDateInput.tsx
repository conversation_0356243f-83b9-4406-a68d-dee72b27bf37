// CTRON Home Design System - Simple Date Input Component
// Text-based date input without external dependencies

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Alert,
} from 'react-native';
import { colors, spacing, typography, borderRadius } from '../../styles/theme';

export interface SimpleDateInputProps {
  value: Date;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'date' | 'time' | 'datetime';
  placeholder?: string;
  label?: string;
  style?: any;
  disabled?: boolean;
}

export const SimpleDateInput: React.FC<SimpleDateInputProps> = ({
  value,
  onChange,
  minimumDate,
  maximumDate,
  mode = 'datetime',
  placeholder,
  label,
  style,
  disabled = false,
}) => {
  const [textValue, setTextValue] = useState('');

  useEffect(() => {
    // Initialize text value from the date prop
    setTextValue(formatDateForDisplay(value, mode));
  }, [value, mode]);

  const formatDateForDisplay = (date: Date, displayMode: string): string => {
    if (displayMode === 'date') {
      return date.toLocaleDateString();
    } else if (displayMode === 'time') {
      return date.toLocaleTimeString();
    } else {
      return date.toLocaleString();
    }
  };

  const parseDate = (text: string): Date | null => {
    // Try multiple date formats
    const formats = [
      text, // Direct parsing
      // MM/DD/YYYY formats
      text.replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/, '$3-$1-$2'),
      text.replace(/(\d{1,2})\/(\d{1,2})\/(\d{2})/, '20$3-$1-$2'),
      // MM-DD-YYYY formats
      text.replace(/(\d{1,2})-(\d{1,2})-(\d{4})/, '$3-$1-$2'),
      text.replace(/(\d{1,2})-(\d{1,2})-(\d{2})/, '20$3-$1-$2'),
      // DD/MM/YYYY formats
      text.replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/, '$3-$2-$1'),
      // YYYY/MM/DD formats
      text.replace(/(\d{4})\/(\d{1,2})\/(\d{1,2})/, '$1-$2-$3'),
    ];

    for (const format of formats) {
      const testDate = new Date(format);
      if (!isNaN(testDate.getTime())) {
        return testDate;
      }
    }

    return null;
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);
    
    const parsedDate = parseDate(text);
    
    if (parsedDate) {
      // Validate date range
      if (minimumDate && parsedDate < minimumDate) {
        Alert.alert('Invalid Date', 'Please select a date in the future');
        return;
      }
      
      if (maximumDate && parsedDate > maximumDate) {
        Alert.alert('Invalid Date', 'Please select an earlier date');
        return;
      }

      onChange(parsedDate);
    }
  };

  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;
    
    switch (mode) {
      case 'date':
        return 'MM/DD/YYYY';
      case 'time':
        return 'HH:MM AM/PM';
      default:
        return 'MM/DD/YYYY HH:MM AM/PM';
    }
  };

  const getHelpText = (): string => {
    switch (mode) {
      case 'date':
        return 'Enter date as MM/DD/YYYY (e.g., 12/25/2024)';
      case 'time':
        return 'Enter time as HH:MM AM/PM (e.g., 2:30 PM)';
      default:
        return 'Enter date and time (e.g., 12/25/2024 2:30 PM)';
    }
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TextInput
        style={[styles.textInput, disabled && styles.disabled]}
        value={textValue}
        onChangeText={handleTextChange}
        placeholder={getPlaceholder()}
        placeholderTextColor={colors.text.tertiary}
        editable={!disabled}
        accessibilityLabel={label || 'Date input'}
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      <Text style={styles.helpText}>
        {getHelpText()}
      </Text>
      
      {minimumDate && (
        <Text style={styles.constraintText}>
          Minimum date: {formatDateForDisplay(minimumDate, mode)}
        </Text>
      )}
      
      {maximumDate && (
        <Text style={styles.constraintText}>
          Maximum date: {formatDateForDisplay(maximumDate, mode)}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },

  label: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },

  textInput: {
    borderWidth: 2,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing.lg,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    backgroundColor: colors.background.primary,
    color: colors.text.primary,
    minHeight: 48,
  },

  disabled: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[300],
  },

  helpText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },

  constraintText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
});

export default SimpleDateInput;
