// CTRON Home Design System - Status Badge Component
// Visual status indicators for jobs, technicians, and system states

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { lightTheme } from '../../styles/theme';

const { colors, spacing, typography, borderRadius } = lightTheme;

export interface StatusBadgeProps {
  status: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' | 'overdue' | 'verified' | 'available' | 'offline' | 'emergency';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'filled' | 'outlined' | 'subtle' | 'priority';
  customText?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'md',
  variant = 'filled',
  customText,
  style,
  textStyle,
  testID,
}) => {
  const getStatusConfig = () => {
    const configs = {
      pending: {
        text: 'Pending',
        color: colors.warning.main,
        backgroundColor: colors.warning.main,
        lightBackground: '#FEF3C7',
      },
      assigned: {
        text: 'Assigned',
        color: colors.info.main,
        backgroundColor: colors.info.main,
        lightBackground: '#DBEAFE',
      },
      active: {
        text: 'In Progress',
        color: colors.primary.main,
        backgroundColor: colors.primary.main,
        lightBackground: '#E5F3FF',
      },
      completed: {
        text: 'Completed',
        color: colors.success.main,
        backgroundColor: colors.success.main,
        lightBackground: '#D1FAE5',
      },
      cancelled: {
        text: 'Cancelled',
        color: colors.gray500,
        backgroundColor: colors.gray500,
        lightBackground: '#F3F4F6',
      },
      overdue: {
        text: 'Overdue',
        color: colors.error.main,
        backgroundColor: colors.error.main,
        lightBackground: '#FECACA',
      },
      verified: {
        text: 'Verified',
        color: colors.success.main,
        backgroundColor: colors.success.main,
        lightBackground: '#D1FAE5',
      },
      available: {
        text: 'Available',
        color: colors.success.main,
        backgroundColor: colors.success.main,
        lightBackground: '#D1FAE5',
      },
      offline: {
        text: 'Offline',
        color: colors.gray500,
        backgroundColor: colors.gray500,
        lightBackground: colors.gray100,
      },
      emergency: {
        text: 'EMERGENCY',
        color: colors.white,
        backgroundColor: colors.error.main,
        lightBackground: '#FECACA',
      },
    };

    return configs[status];
  };

  const statusConfig = getStatusConfig();
  const displayText = customText || statusConfig.text;

  const badgeStyles = [
    styles.base,
    styles[size],
    styles[variant],
    variant === 'filled' && { backgroundColor: statusConfig.backgroundColor },
    variant === 'outlined' && {
      borderColor: statusConfig.color,
      backgroundColor: 'transparent',
    },
    variant === 'subtle' && { backgroundColor: statusConfig.lightBackground },
    variant === 'priority' && { backgroundColor: statusConfig.backgroundColor },
    style,
  ].filter(Boolean);

  const textStyles = [
    styles.text,
    styles[`${size}Text`],
    variant === 'filled' && { color: colors.white },
    variant === 'outlined' && { color: statusConfig.color },
    variant === 'subtle' && { color: statusConfig.color },
    variant === 'priority' && { color: statusConfig.color },
    textStyle,
  ].filter(Boolean);

  return (
    <View style={badgeStyles} testID={testID}>
      <Text style={textStyles}>{displayText}</Text>
    </View>
  );
};

// Verification Badge Component
export interface VerificationBadgeProps {
  verified: boolean;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  style?: ViewStyle;
}

export const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  verified,
  size = 'md',
  showIcon = true,
  style,
}) => {
  if (!verified) return null;

  return (
    <View style={[styles.verificationBadge, styles[size], style]}>
      {showIcon && <Text style={styles.verificationIcon}>✓</Text>}
      <Text style={[styles.verificationText, styles[`${size}Text`]]}>
        Verified
      </Text>
    </View>
  );
};

// Priority Badge Component
export interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high' | 'emergency';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  priority,
  size = 'md',
  style,
}) => {
  const getPriorityConfig = () => {
    const configs = {
      low: {
        text: 'Low',
        color: colors.gray500,
        backgroundColor: colors.gray100,
      },
      medium: {
        text: 'Medium',
        color: colors.warning.main,
        backgroundColor: '#FEF3C7',
      },
      high: {
        text: 'High',
        color: colors.secondary.main,
        backgroundColor: '#E8F5E8',
      },
      emergency: {
        text: 'EMERGENCY',
        color: colors.white,
        backgroundColor: colors.error.main,
      },
    };

    return configs[priority];
  };

  const priorityConfig = getPriorityConfig();

  const badgeStyles = [
    styles.base,
    styles[size],
    { backgroundColor: priorityConfig.backgroundColor },
    priority === 'emergency' && styles.emergencyPulse,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${size}Text`],
    { color: priorityConfig.color },
    priority === 'emergency' && styles.emergencyText,
  ];

  return (
    <View style={badgeStyles}>
      <Text style={textStyles}>{priorityConfig.text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    alignSelf: 'flex-start',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Sizes
  sm: {
    paddingHorizontal: spacing.xs,
    paddingVertical: spacing.xs,
  },

  md: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },

  lg: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
  },

  // Variants
  filled: {
    // Background color set dynamically
  },

  outlined: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },

  subtle: {
    // Background color set dynamically
  },

  priority: {
    // Background color set dynamically for priority badges
  },

  // Text Styles
  text: {
    ...typography.caption2,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  smText: {
    ...typography.caption2,
  },

  mdText: {
    ...typography.caption2,
  },

  lgText: {
    ...typography.footnote,
  },

  // Verification Badge
  verificationBadge: {
    backgroundColor: colors.success.main,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },

  verificationIcon: {
    color: colors.white,
    ...typography.footnote,
    fontWeight: '700',
    marginRight: spacing.xs,
  },

  verificationText: {
    color: colors.white,
    ...typography.caption2,
    fontWeight: '600',
  },

  // Emergency Styles
  emergencyPulse: {
    // Animation would be implemented with Animated API
  },

  emergencyText: {
    fontWeight: '700',
  },
});

export default StatusBadge;
