// src/context/AuthContext.tsx
import React, { createContext, useState, useEffect, useContext } from 'react';
import * as SecureStore from 'expo-secure-store';
import { jwtDecode } from 'jwt-decode';
import { Platform } from 'react-native';
import { CommonActions } from '@react-navigation/native';
import Toast from 'react-native-toast-message';

import { setAuthToken } from '../utils/auth.utils';
import { navigationRef } from '../navigation/navigationRef';
import { SocketService } from '../services/socket.service';

export interface UserPayload {
  userId: string;
  fullName: string;
  email: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  exp: number;
  iat: number;
}

interface AuthContextType {
  user: UserPayload | null;
  token: string | null;
  loading: boolean;
  login: (token: string) => Promise<void>;
  logout: (showMessage?: boolean) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);
export { AuthContext };

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserPayload | null>(null);
  const [token, setTokenState] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('🔐 AuthProvider: Starting bootstrap...');
    bootstrap();
  }, []);

  const bootstrap = async () => {
    console.log('🔐 AuthProvider: Bootstrap function called, Platform:', Platform.OS);
    try {
      // Handle web platform authentication
      if (Platform.OS === 'web') {
        console.log('🌐 AuthProvider: Handling web platform authentication');
        try {
          const webToken = localStorage.getItem('token');
          console.log('🌐 AuthProvider: Web token from localStorage:', webToken ? 'Found' : 'Not found');
          if (webToken) {
            const decoded = jwtDecode<UserPayload>(webToken);

            if (__DEV__) {
              console.log('🌐 Web token decoded successfully for user:', decoded.userId);
            }

            if (!decoded.role) {
              console.warn('❌ Web token is missing role. Clearing...');
              localStorage.removeItem('token');
              setLoading(false);
              return;
            }

            if (decoded.exp * 1000 < Date.now()) {
              console.warn('⚠️ Web token expired. Clearing...');
              localStorage.removeItem('token');
              setLoading(false);
              return;
            }

            // Valid web token found
            setUser(decoded);
            setTokenState(webToken);
            setAuthToken(webToken);

            const success = navigateToRoleDashboard(decoded.role);
            if (!success) {
              console.warn('🚫 Web role mismatch detected. Logging out...');
              await logout(true);
            }
          }
        } catch (error) {
          console.error('🔴 Web auth bootstrap failed:', error);
          localStorage.removeItem('token');
        }
        setLoading(false);
        return;
      }

      const storedToken = await SecureStore.getItemAsync('token');
      if (!storedToken) {
        setLoading(false);
        return;
      }

      const decoded = jwtDecode<UserPayload>(storedToken);
      console.log('🧾 Decoded token payload (bootstrap):', decoded);

      // Ensure id property is set from userId for compatibility
      if (!(decoded as any).id && decoded.userId) {
        (decoded as any).id = decoded.userId;
      }

      if (!decoded.role) {
        console.warn('❌ Token is missing role. Forcing logout.');
        await logout(true);
        return;
      }

      if (decoded.exp * 1000 < Date.now()) {
        console.warn('⚠️ Token expired. Clearing...');
        await logout(true);
      } else {
        setUser(decoded);
        setTokenState(storedToken);
        setAuthToken(storedToken);

        const success = navigateToRoleDashboard(decoded.role);
        if (!success) {
          console.warn('🚫 Role mismatch detected. Logging out...');
          await logout(true);
        }
      }
    } catch (err) {
      console.error('🔴 Bootstrap failed:', err);
      await logout(true);
    } finally {
      setLoading(false);
    }
  };

  const login = async (token: string) => {
    try {
      if (!token) throw new Error('Invalid token');

      // Store token based on platform
      if (Platform.OS === 'web') {
        localStorage.setItem('token', token);
      } else {
        await SecureStore.setItemAsync('token', token);
      }

      const decoded = jwtDecode<UserPayload>(token);

      // Ensure id property is set from userId for compatibility
      if (!(decoded as any).id && decoded.userId) {
        (decoded as any).id = decoded.userId;
      }

      // Only log in development mode and avoid logging sensitive data
      if (__DEV__) {
        console.log('🧾 Token decoded successfully for user:', decoded.userId);
      }

      if (!decoded.role) {
        if (__DEV__) {
          console.warn('❌ Decoded token missing role. Logging out...');
        }
        await logout(true);
        return;
      }

      setUser(decoded);
      setTokenState(token);
      setAuthToken(token);

      const success = navigateToRoleDashboard(decoded.role);
      if (!success) {
        if (__DEV__) {
          console.warn('🚫 Role mismatch at login. Logging out...');
        }
        await logout(true);
      }
    } catch (error) {
      if (__DEV__) {
        console.error('🔴 Login failed:', error instanceof Error ? error.message : 'Unknown error');
      }
    }
  };

  const logout = async (showMessage = false) => {
    try {
      // Clean up secure storage
      if (Platform.OS !== 'web') {
        await SecureStore.deleteItemAsync('token');
      } else {
        // For web, clear localStorage
        localStorage.removeItem('token');
      }

      setUser(null);
      setTokenState(null);
      setAuthToken(null);

      if (__DEV__) {
        console.log('👋 Logged out');
      }

      if (showMessage) {
        Toast.show({
          type: 'info',
          text1: 'Session expired',
          text2: 'Please login again.',
          position: 'bottom',
        });
      }

      // Ensure navigation is available before attempting to navigate
      if (navigationRef.current) {
        navigationRef.current.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: 'Auth' }],
          })
        );
      }
    } catch (error) {
      if (__DEV__) {
        console.error('🔴 Logout failed:', error instanceof Error ? error.message : 'Unknown error');
      }

      // Even if logout fails, clear local state
      setUser(null);
      setTokenState(null);
      setAuthToken(null);
    }
  };

  const navigateToRoleDashboard = (role: UserPayload['role']): boolean => {
    let expectedStack: string;
    let firstScreen: string;

    switch (role) {
      case 'TECHNICIAN':
        expectedStack = 'TechnicianStack';
        firstScreen = 'TechnicianDrawer';
        break;
      case 'ADMIN':
        expectedStack = 'AdminStack';
        firstScreen = 'AdminDashboard';
        break;
      case 'HOMEOWNER':
      default:
        expectedStack = 'HomeownerStack';
        firstScreen = 'Home';
        break;
    }

    const currentState = navigationRef.current?.getRootState();
    const currentStack = currentState?.routes?.[0]?.name;

    if (currentStack && currentStack !== 'Auth' && currentStack !== expectedStack) {
      console.error(`🚨 Expected ${expectedStack} but found ${currentStack}`);
      return false;
    }

    navigationRef.current?.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: expectedStack,
            state: {
              routes: [{ name: firstScreen }],
            },
          },
        ],
      })
    );

    return true;
  };

  return (
    <AuthContext.Provider value={{ user, token, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('❌ useAuth must be used inside an <AuthProvider>');
  }
  return context;
};
