//src/context/TechnicianContext.tsx

import React, { createContext, useState, useEffect, useContext } from 'react';
import { TechnicianAPI } from '../api/technician.api';
import { useAuth } from './AuthContext';

export interface Technician {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  skills?: string[];
  available?: boolean;
}

interface TechnicianContextType {
  technician: Technician | null;
  refreshTechnician: () => Promise<void>;
}

const TechnicianContext = createContext<TechnicianContextType>(null!);

export const TechnicianProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { token } = useAuth();
  const [technician, setTechnician] = useState<Technician | null>(null);

  const refreshTechnician = async () => {
    if (!token) return;
    try {
      const res = await TechnicianAPI.getProfile();
      if (res?.technician) {
        setTechnician(res.technician);
      }
    } catch (err) {
      console.error('[TechnicianContext] Failed to load technician profile:', err);
    }
  };

  useEffect(() => {
    refreshTechnician();
  }, [token]);

  return (
    <TechnicianContext.Provider value={{ technician, refreshTechnician }}>
      {children}
    </TechnicianContext.Provider>
  );
};

export const useTechnician = () => useContext(TechnicianContext);