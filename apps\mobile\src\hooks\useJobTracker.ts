// src/hooks/useJobTracker.ts

import { useEffect, useState } from 'react';
import { JobAPI } from '../api/job.api';

export const useJobTracker = () => {
  const [jobs, setJobs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const res = await JobAPI.getUserJobs();
      setJobs(res);
    } catch (err) {
      console.error('Failed to fetch jobs', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return { jobs, loading, refresh: fetchJobs };
};
