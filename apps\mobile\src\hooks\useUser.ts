import { useEffect, useState } from 'react';
import * as SecureStore from 'expo-secure-store';
import { jwtDecode } from 'jwt-decode';

interface UserPayload {
  userId: string;
  id: string; // Add id property for compatibility
  fullName: string;
  email: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
}

// Real auth state management
export function useUser() {
  const [user, setUser] = useState<UserPayload | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const token = await SecureStore.getItemAsync('token');
        if (token) {
          const decoded = jwtDecode<UserPayload>(token);
          setUser(decoded);
        }
      } catch (error) {
        console.error('Error loading user:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  return { user, role: user?.role || null, loading };
}
