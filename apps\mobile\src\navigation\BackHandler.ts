//src/navigation/Backhandler.ts
import { BackHandler as RNBackHandler } from 'react-native';
import { navigationRef } from './navigationRef';

export const BackHandler = {
  attach: () => {
    const subscription = RNBackHandler.addEventListener('hardwareBackPress', () => {
      if (navigationRef.current?.canGoBack()) {
        navigationRef.current.goBack();
        return true;
      }
      return false; // Exit app
    });
    return subscription;
  },
};
