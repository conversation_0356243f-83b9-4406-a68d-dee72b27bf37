// src/navigation/RootNavigator.tsx
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainer } from '@react-navigation/native';
import { View, ActivityIndicator } from 'react-native';
import { navigationRef } from './navigationRef';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { useAuth } from '../context/AuthContext';
import { colors } from '../styles/theme';

// Navigation Stacks
import AuthStack from './AuthStack';
import HomeownerStack from './HomeownerStack';
import TechnicianStack from './TechnicianStack';
import AdminStack from './AdminStack';

// Types for the routes
export type RootStackParamList = {
  Auth: undefined;
  HomeownerStack: undefined;
  TechnicianStack: undefined;
  AdminStack: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export const RootNavigator = () => {
  const { user, loading } = useAuth();

  // Debug logging
  if (__DEV__) {
    console.log('🧭 RootNavigator render - Loading:', loading, 'User:', user ? `${user.role} (${(user as any).id || user.userId})` : 'null');
  }

  // Safety timeout for loading state (prevent infinite loading)
  React.useEffect(() => {
    if (loading) {
      const timeout = setTimeout(() => {
        if (__DEV__) {
          console.warn('⚠️ Loading timeout reached - this might indicate an issue with AuthContext');
        }
      }, 10000); // 10 second timeout

      return () => clearTimeout(timeout);
    }
  }, [loading]);

  // Show loading screen while checking authentication
  if (loading) {
    if (__DEV__) {
      console.log('⏳ RootNavigator showing loading screen');
    }
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.background.primary,
      }}>
        <ActivityIndicator size="large" color={colors.primary.main} />
      </View>
    );
  }

  // Determine initial route
  const initialRoute = user ?
    (user.role === 'TECHNICIAN' ? 'TechnicianStack' :
     user.role === 'ADMIN' ? 'AdminStack' : 'HomeownerStack')
    : 'Auth';

  if (__DEV__) {
    console.log('🧭 RootNavigator initial route:', initialRoute);
  }

  return (
    <ErrorBoundary>
      <NavigationContainer ref={navigationRef}>
        <Stack.Navigator
          screenOptions={{ headerShown: false }}
          initialRouteName={initialRoute}
        >
          <Stack.Screen name="Auth" component={AuthStack} />
          <Stack.Screen name="HomeownerStack" component={HomeownerStack} />
          <Stack.Screen name="TechnicianStack" component={TechnicianStack} />
          <Stack.Screen name="AdminStack" component={AdminStack} />
        </Stack.Navigator>
      </NavigationContainer>
    </ErrorBoundary>
  );
};
