// src/polyfills/BaseViewConfig-web.js
// Web polyfill for React Native BaseViewConfig

import { Platform } from 'react-native';

/**
 * Base view configuration for React Native Web compatibility
 * This provides the basic view configuration that React Native expects
 */

const BaseViewConfig = {
  uiViewClassName: 'RCTView',
  
  // Base view properties that are valid for all views
  validAttributes: {
    // Layout properties
    width: true,
    height: true,
    top: true,
    left: true,
    right: true,
    bottom: true,
    minWidth: true,
    maxWidth: true,
    minHeight: true,
    maxHeight: true,
    margin: true,
    marginVertical: true,
    marginHorizontal: true,
    marginTop: true,
    marginBottom: true,
    marginLeft: true,
    marginRight: true,
    marginStart: true,
    marginEnd: true,
    padding: true,
    paddingVertical: true,
    paddingHorizontal: true,
    paddingTop: true,
    paddingBottom: true,
    paddingLeft: true,
    paddingRight: true,
    paddingStart: true,
    paddingEnd: true,
    
    // Position properties
    position: true,
    zIndex: true,
    
    // Flexbox properties
    flex: true,
    flexGrow: true,
    flexShrink: true,
    flexBasis: true,
    flexDirection: true,
    flexWrap: true,
    justifyContent: true,
    alignItems: true,
    alignSelf: true,
    alignContent: true,
    
    // Display properties
    display: true,
    overflow: true,
    opacity: true,
    
    // Background properties
    backgroundColor: true,
    
    // Border properties
    borderWidth: true,
    borderTopWidth: true,
    borderBottomWidth: true,
    borderLeftWidth: true,
    borderRightWidth: true,
    borderStartWidth: true,
    borderEndWidth: true,
    borderColor: true,
    borderTopColor: true,
    borderBottomColor: true,
    borderLeftColor: true,
    borderRightColor: true,
    borderStartColor: true,
    borderEndColor: true,
    borderRadius: true,
    borderTopLeftRadius: true,
    borderTopRightRadius: true,
    borderBottomLeftRadius: true,
    borderBottomRightRadius: true,
    borderTopStartRadius: true,
    borderTopEndRadius: true,
    borderBottomStartRadius: true,
    borderBottomEndRadius: true,
    borderStyle: true,
    
    // Transform properties
    transform: true,
    transformMatrix: true,
    rotation: true,
    scaleX: true,
    scaleY: true,
    translateX: true,
    translateY: true,
    
    // Shadow properties (iOS)
    shadowColor: true,
    shadowOffset: true,
    shadowOpacity: true,
    shadowRadius: true,
    
    // Elevation (Android)
    elevation: true,
    
    // Accessibility properties
    accessible: true,
    accessibilityLabel: true,
    accessibilityHint: true,
    accessibilityRole: true,
    accessibilityState: true,
    accessibilityValue: true,
    accessibilityActions: true,
    accessibilityElementsHidden: true,
    accessibilityViewIsModal: true,
    accessibilityIgnoresInvertColors: true,
    accessibilityLiveRegion: true,
    importantForAccessibility: true,
    
    // Interaction properties
    pointerEvents: true,
    hitSlop: true,
    
    // Test properties
    testID: true,
    nativeID: true,
    
    // Web-specific properties
    className: true,
    id: true,
    tabIndex: true,
    role: true,
    'aria-label': true,
    'aria-labelledby': true,
    'aria-describedby': true,
    'aria-hidden': true,
    'aria-expanded': true,
    'aria-selected': true,
    'aria-checked': true,
    'aria-disabled': true,
    'aria-pressed': true,
    'aria-live': true,
    'aria-atomic': true,
    'aria-busy': true,
    'aria-relevant': true,
    'aria-owns': true,
    'aria-controls': true,
    'aria-flowto': true,
    'aria-activedescendant': true,
    'aria-autocomplete': true,
    'aria-haspopup': true,
    'aria-invalid': true,
    'aria-level': true,
    'aria-multiline': true,
    'aria-multiselectable': true,
    'aria-orientation': true,
    'aria-readonly': true,
    'aria-required': true,
    'aria-sort': true,
    'aria-valuemax': true,
    'aria-valuemin': true,
    'aria-valuenow': true,
    'aria-valuetext': true,
    
    // Data attributes for web
    'data-testid': true,
    'data-test': true,
    'data-automation': true,
  },
  
  // Direct event types (events that don't bubble)
  directEventTypes: {
    topLayout: {
      registrationName: 'onLayout',
    },
    topLoadingError: {
      registrationName: 'onError',
    },
    topLoadingFinish: {
      registrationName: 'onLoad',
    },
    topLoadingStart: {
      registrationName: 'onLoadStart',
    },
    topMagicTap: {
      registrationName: 'onMagicTap',
    },
    topAccessibilityTap: {
      registrationName: 'onAccessibilityTap',
    },
    topAccessibilityAction: {
      registrationName: 'onAccessibilityAction',
    },
  },
  
  // Bubbling event types (events that bubble up the component tree)
  bubblingEventTypes: {
    topPress: {
      phasedRegistrationNames: {
        bubbled: 'onPress',
        captured: 'onPressCapture',
      },
    },
    topPressIn: {
      phasedRegistrationNames: {
        bubbled: 'onPressIn',
        captured: 'onPressInCapture',
      },
    },
    topPressOut: {
      phasedRegistrationNames: {
        bubbled: 'onPressOut',
        captured: 'onPressOutCapture',
      },
    },
    topLongPress: {
      phasedRegistrationNames: {
        bubbled: 'onLongPress',
        captured: 'onLongPressCapture',
      },
    },
    topTouchStart: {
      phasedRegistrationNames: {
        bubbled: 'onTouchStart',
        captured: 'onTouchStartCapture',
      },
    },
    topTouchMove: {
      phasedRegistrationNames: {
        bubbled: 'onTouchMove',
        captured: 'onTouchMoveCapture',
      },
    },
    topTouchEnd: {
      phasedRegistrationNames: {
        bubbled: 'onTouchEnd',
        captured: 'onTouchEndCapture',
      },
    },
    topTouchCancel: {
      phasedRegistrationNames: {
        bubbled: 'onTouchCancel',
        captured: 'onTouchCancelCapture',
      },
    },
    topMouseEnter: {
      phasedRegistrationNames: {
        bubbled: 'onMouseEnter',
        captured: 'onMouseEnterCapture',
      },
    },
    topMouseLeave: {
      phasedRegistrationNames: {
        bubbled: 'onMouseLeave',
        captured: 'onMouseLeaveCapture',
      },
    },
  },
};

// Export as default for compatibility
export default BaseViewConfig;

// Also export named exports for compatibility
export const uiViewClassName = BaseViewConfig.uiViewClassName;
export const validAttributes = BaseViewConfig.validAttributes;
export const directEventTypes = BaseViewConfig.directEventTypes;
export const bubblingEventTypes = BaseViewConfig.bubblingEventTypes;

// Utility functions for view configuration
export const createViewConfig = (overrides = {}) => ({
  ...BaseViewConfig,
  ...overrides,
});

export const mergeViewConfig = (baseConfig, additionalConfig) => ({
  ...baseConfig,
  validAttributes: {
    ...baseConfig.validAttributes,
    ...additionalConfig.validAttributes,
  },
  directEventTypes: {
    ...baseConfig.directEventTypes,
    ...additionalConfig.directEventTypes,
  },
  bubblingEventTypes: {
    ...baseConfig.bubblingEventTypes,
    ...additionalConfig.bubblingEventTypes,
  },
});

// Platform-specific configuration
export const getPlatformViewConfig = () => {
  if (Platform.OS === 'web') {
    return {
      ...BaseViewConfig,
      validAttributes: {
        ...BaseViewConfig.validAttributes,
        // Add web-specific attributes
        contentEditable: true,
        draggable: true,
        spellCheck: true,
        autoComplete: true,
        autoFocus: true,
        autoCapitalize: true,
        autoCorrect: true,
      },
    };
  }
  
  return BaseViewConfig;
};
