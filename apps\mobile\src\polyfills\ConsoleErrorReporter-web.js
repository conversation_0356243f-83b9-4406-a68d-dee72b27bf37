// ConsoleErrorReporter polyfill for React Native Web
// Fixes: require(...).installConsoleErrorReporter is not a function

import { Platform } from 'react-native';

// Mock console error reporter for web compatibility
const ConsoleErrorReporter = {
  installConsoleErrorReporter: () => {
    if (Platform.OS === 'web') {
      console.log('🌐 Mock: installConsoleErrorReporter called (web polyfill)');
      
      // Setup basic error reporting for web
      const originalConsoleError = console.error;
      const originalConsoleWarn = console.warn;
      
      console.error = (...args) => {
        originalConsoleError(...args);
        
        // In development, we can add additional error handling here
        if (__DEV__) {
          const errorMessage = args.join(' ');
          if (errorMessage.includes('Warning:') || errorMessage.includes('Error:')) {
            // Could send to error reporting service in production
            console.log('🔍 Error captured by web polyfill:', errorMessage);
          }
        }
      };
      
      console.warn = (...args) => {
        originalConsoleWarn(...args);
        
        if (__DEV__) {
          const warningMessage = args.join(' ');
          console.log('⚠️ Warning captured by web polyfill:', warningMessage);
        }
      };
      
      return {
        uninstall: () => {
          console.error = originalConsoleError;
          console.warn = originalConsoleWarn;
          console.log('🌐 Mock: Console error reporter uninstalled');
        }
      };
    }
    
    // For non-web platforms, return a no-op
    return {
      uninstall: () => {}
    };
  },
  
  // Additional methods that might be called
  setGlobalHandler: (handler) => {
    if (Platform.OS === 'web') {
      console.log('🌐 Mock: setGlobalHandler called (web polyfill)');
      // Store the handler for potential use
      if (typeof window !== 'undefined') {
        window.__webErrorHandler = handler;
      }
    }
  },
  
  getGlobalHandler: () => {
    if (Platform.OS === 'web') {
      console.log('🌐 Mock: getGlobalHandler called (web polyfill)');
      return typeof window !== 'undefined' ? window.__webErrorHandler : null;
    }
    return null;
  }
};

// Export the mock for direct imports
export default ConsoleErrorReporter;

// Also export individual functions
export const installConsoleErrorReporter = ConsoleErrorReporter.installConsoleErrorReporter;
export const setGlobalHandler = ConsoleErrorReporter.setGlobalHandler;
export const getGlobalHandler = ConsoleErrorReporter.getGlobalHandler;

// Setup global polyfill if on web
if (Platform.OS === 'web' && typeof global !== 'undefined') {
  // Mock the module that's being required
  if (!global.__consoleErrorReporterPolyfill) {
    global.__consoleErrorReporterPolyfill = ConsoleErrorReporter;
    console.log('🌐 ConsoleErrorReporter polyfill installed globally');
  }

  // Also setup direct function access for require() calls
  if (!global.installConsoleErrorReporter) {
    global.installConsoleErrorReporter = ConsoleErrorReporter.installConsoleErrorReporter;
    console.log('🌐 installConsoleErrorReporter function available globally');
  }

  // Mock the entire module for require() calls
  if (typeof require !== 'undefined' && require.cache) {
    // Try to intercept require calls for ConsoleErrorReporter
    const originalRequire = require;
    if (typeof originalRequire === 'function') {
      // This will help catch direct require() calls
      console.log('🌐 ConsoleErrorReporter: Enhanced require interception ready');
    }
  }
}
