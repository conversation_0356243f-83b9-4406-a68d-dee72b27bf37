// src/polyfills/DevToolsSettingsManager-web.js
// Web polyfill for React Native DevToolsSettingsManager

import { Platform } from 'react-native';

/**
 * Web implementation of DevToolsSettingsManager
 * Provides development tools settings management for web platform
 */

const DevToolsSettingsManager = {
  /**
   * Get development tools settings
   */
  getSettings: () => {
    if (Platform.OS === 'web') {
      // Return default settings for web
      return {
        isDebuggingEnabled: __DEV__,
        isNetworkInspectEnabled: __DEV__,
        isElementInspectorEnabled: __DEV__,
        isPerformanceMonitorEnabled: __DEV__,
        isHotReloadEnabled: __DEV__,
        isLiveReloadEnabled: false,
        isRemoteDebuggingEnabled: false,
        debuggerHost: 'localhost',
        debuggerPort: 8081,
      };
    }
    
    return {};
  },

  /**
   * Set development tools settings
   */
  setSettings: (settings) => {
    if (Platform.OS === 'web' && __DEV__) {
      // Store settings in localStorage for web
      try {
        localStorage.setItem('ReactNativeDevToolsSettings', JSON.stringify(settings));
      } catch (error) {
        console.warn('DevToolsSettingsManager: Failed to save settings', error);
      }
    }
  },

  /**
   * Get specific setting value
   */
  getSetting: (key, defaultValue = null) => {
    const settings = DevToolsSettingsManager.getSettings();
    return settings[key] !== undefined ? settings[key] : defaultValue;
  },

  /**
   * Set specific setting value
   */
  setSetting: (key, value) => {
    const settings = DevToolsSettingsManager.getSettings();
    settings[key] = value;
    DevToolsSettingsManager.setSettings(settings);
  },

  /**
   * Reset settings to defaults
   */
  resetSettings: () => {
    if (Platform.OS === 'web') {
      try {
        localStorage.removeItem('ReactNativeDevToolsSettings');
      } catch (error) {
        console.warn('DevToolsSettingsManager: Failed to reset settings', error);
      }
    }
  },

  /**
   * Check if debugging is enabled
   */
  isDebuggingEnabled: () => {
    return DevToolsSettingsManager.getSetting('isDebuggingEnabled', __DEV__);
  },

  /**
   * Enable/disable debugging
   */
  setDebuggingEnabled: (enabled) => {
    DevToolsSettingsManager.setSetting('isDebuggingEnabled', enabled);
  },

  /**
   * Check if network inspection is enabled
   */
  isNetworkInspectEnabled: () => {
    return DevToolsSettingsManager.getSetting('isNetworkInspectEnabled', __DEV__);
  },

  /**
   * Enable/disable network inspection
   */
  setNetworkInspectEnabled: (enabled) => {
    DevToolsSettingsManager.setSetting('isNetworkInspectEnabled', enabled);
  },

  /**
   * Check if element inspector is enabled
   */
  isElementInspectorEnabled: () => {
    return DevToolsSettingsManager.getSetting('isElementInspectorEnabled', __DEV__);
  },

  /**
   * Enable/disable element inspector
   */
  setElementInspectorEnabled: (enabled) => {
    DevToolsSettingsManager.setSetting('isElementInspectorEnabled', enabled);
  },

  /**
   * Check if performance monitor is enabled
   */
  isPerformanceMonitorEnabled: () => {
    return DevToolsSettingsManager.getSetting('isPerformanceMonitorEnabled', __DEV__);
  },

  /**
   * Enable/disable performance monitor
   */
  setPerformanceMonitorEnabled: (enabled) => {
    DevToolsSettingsManager.setSetting('isPerformanceMonitorEnabled', enabled);
  },

  /**
   * Get debugger connection info
   */
  getDebuggerInfo: () => {
    return {
      host: DevToolsSettingsManager.getSetting('debuggerHost', 'localhost'),
      port: DevToolsSettingsManager.getSetting('debuggerPort', 8081),
      isConnected: false, // Web doesn't use remote debugger
    };
  },

  /**
   * Set debugger connection info
   */
  setDebuggerInfo: (host, port) => {
    DevToolsSettingsManager.setSetting('debuggerHost', host);
    DevToolsSettingsManager.setSetting('debuggerPort', port);
  },

  /**
   * Initialize development tools
   */
  initialize: () => {
    if (Platform.OS === 'web' && __DEV__) {
      // Load settings from localStorage
      try {
        const stored = localStorage.getItem('ReactNativeDevToolsSettings');
        if (stored) {
          const settings = JSON.parse(stored);
          // Apply any initialization logic here
          console.log('DevToolsSettingsManager: Initialized with settings', settings);
        }
      } catch (error) {
        console.warn('DevToolsSettingsManager: Failed to load settings', error);
      }
    }
  },

  /**
   * Cleanup development tools
   */
  cleanup: () => {
    if (Platform.OS === 'web') {
      // Perform any cleanup logic here
      console.log('DevToolsSettingsManager: Cleanup completed');
    }
  },
};

// Initialize on load
if (Platform.OS === 'web' && __DEV__) {
  DevToolsSettingsManager.initialize();
}

// Export as default for compatibility
export default DevToolsSettingsManager;

// Also export named exports for compatibility
export const getSettings = DevToolsSettingsManager.getSettings;
export const setSettings = DevToolsSettingsManager.setSettings;
export const getSetting = DevToolsSettingsManager.getSetting;
export const setSetting = DevToolsSettingsManager.setSetting;
export const resetSettings = DevToolsSettingsManager.resetSettings;
export const isDebuggingEnabled = DevToolsSettingsManager.isDebuggingEnabled;
export const setDebuggingEnabled = DevToolsSettingsManager.setDebuggingEnabled;
export const isNetworkInspectEnabled = DevToolsSettingsManager.isNetworkInspectEnabled;
export const setNetworkInspectEnabled = DevToolsSettingsManager.setNetworkInspectEnabled;
export const isElementInspectorEnabled = DevToolsSettingsManager.isElementInspectorEnabled;
export const setElementInspectorEnabled = DevToolsSettingsManager.setElementInspectorEnabled;
export const isPerformanceMonitorEnabled = DevToolsSettingsManager.isPerformanceMonitorEnabled;
export const setPerformanceMonitorEnabled = DevToolsSettingsManager.setPerformanceMonitorEnabled;
export const getDebuggerInfo = DevToolsSettingsManager.getDebuggerInfo;
export const setDebuggerInfo = DevToolsSettingsManager.setDebuggerInfo;
export const initialize = DevToolsSettingsManager.initialize;
export const cleanup = DevToolsSettingsManager.cleanup;

// Development utilities for web
export const webDevUtils = {
  /**
   * Log performance metrics
   */
  logPerformance: (name, startTime) => {
    if (__DEV__ && DevToolsSettingsManager.isPerformanceMonitorEnabled()) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    }
  },

  /**
   * Log network request
   */
  logNetworkRequest: (method, url, status, duration) => {
    if (__DEV__ && DevToolsSettingsManager.isNetworkInspectEnabled()) {
      console.log(`Network: ${method} ${url} - ${status} (${duration}ms)`);
    }
  },

  /**
   * Log element inspection
   */
  logElementInspection: (element, action) => {
    if (__DEV__ && DevToolsSettingsManager.isElementInspectorEnabled()) {
      console.log(`Element Inspector: ${action}`, element);
    }
  },

  /**
   * Enable React DevTools
   */
  enableReactDevTools: () => {
    if (Platform.OS === 'web' && __DEV__) {
      // Check if React DevTools extension is available
      if (typeof window !== 'undefined' && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('React DevTools detected and enabled');
        return true;
      } else {
        console.log('React DevTools not detected. Install the browser extension for enhanced debugging.');
        return false;
      }
    }
    return false;
  },

  /**
   * Get development environment info
   */
  getDevEnvironmentInfo: () => {
    if (Platform.OS === 'web') {
      return {
        platform: 'web',
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        devToolsEnabled: __DEV__,
        reactDevToolsAvailable: typeof window !== 'undefined' && !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__,
      };
    }
    return {};
  },
};
