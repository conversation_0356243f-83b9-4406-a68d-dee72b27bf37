// src/polyfills/Image-web.js
// Web polyfill for React Native Image component

import React from 'react';
import { Platform } from 'react-native';

/**
 * Web implementation of React Native Image component
 * Provides the same API as React Native's Image component
 */

const ImageWeb = React.forwardRef(({
  source,
  style,
  resizeMode = 'cover',
  onLoad,
  onError,
  onLoadStart,
  onLoadEnd,
  onProgress,
  defaultSource,
  loadingIndicatorSource,
  fadeDuration = 300,
  accessible = true,
  accessibilityLabel,
  testID,
  blurRadius,
  capInsets,
  progressiveRenderingEnabled,
  ...otherProps
}, ref) => {
  // Only render on web platform
  if (Platform.OS !== 'web') {
    return null;
  }

  // Handle different source formats
  const getImageSource = () => {
    if (!source) return defaultSource?.uri || '';
    
    if (typeof source === 'string') {
      return source;
    }
    
    if (typeof source === 'number') {
      // Handle require() sources - these are typically bundled assets
      return source;
    }
    
    if (source.uri) {
      return source.uri;
    }
    
    return '';
  };

  // Convert React Native resize modes to CSS object-fit
  const getObjectFit = () => {
    switch (resizeMode) {
      case 'cover':
        return 'cover';
      case 'contain':
        return 'contain';
      case 'stretch':
        return 'fill';
      case 'repeat':
        return 'none';
      case 'center':
        return 'none';
      default:
        return 'cover';
    }
  };

  // Convert React Native styles to web-compatible styles
  const getWebStyle = () => {
    const webStyle = {
      objectFit: getObjectFit(),
      objectPosition: resizeMode === 'center' ? 'center' : 'initial',
      ...style,
    };

    // Handle blur radius
    if (blurRadius) {
      webStyle.filter = `blur(${blurRadius}px)`;
    }

    // Ensure the image behaves like a block element
    if (!webStyle.display) {
      webStyle.display = 'block';
    }

    return webStyle;
  };

  // Handle image loading events
  const handleLoad = (event) => {
    if (onLoadEnd) onLoadEnd();
    if (onLoad) {
      onLoad({
        nativeEvent: {
          source: {
            width: event.target.naturalWidth,
            height: event.target.naturalHeight,
            uri: event.target.src,
          },
        },
      });
    }
  };

  const handleError = (event) => {
    if (onLoadEnd) onLoadEnd();
    if (onError) {
      onError({
        nativeEvent: {
          error: event.error || 'Image load error',
        },
      });
    }
  };

  const handleLoadStart = () => {
    if (onLoadStart) onLoadStart();
  };

  const imageSource = getImageSource();

  return React.createElement('img', {
    ref,
    src: imageSource,
    style: getWebStyle(),
    onLoad: handleLoad,
    onError: handleError,
    onLoadStart: handleLoadStart,
    alt: accessibilityLabel || '',
    'data-testid': testID,
    'aria-label': accessibilityLabel,
    role: accessible ? 'img' : 'presentation',
    ...otherProps,
  });
});

// Static methods for compatibility
ImageWeb.getSize = (uri, success, failure) => {
  if (Platform.OS !== 'web') return;
  
  const img = new Image();
  img.onload = () => {
    if (success) {
      success(img.naturalWidth, img.naturalHeight);
    }
  };
  img.onerror = () => {
    if (failure) {
      failure(new Error('Failed to get image size'));
    }
  };
  img.src = uri;
};

ImageWeb.getSizeWithHeaders = (uri, headers, success, failure) => {
  // For web, headers aren't easily supported with Image constructor
  // Fall back to getSize
  ImageWeb.getSize(uri, success, failure);
};

ImageWeb.prefetch = (url) => {
  if (Platform.OS !== 'web') return Promise.resolve(false);
  
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => reject(new Error('Image prefetch failed'));
    img.src = url;
  });
};

ImageWeb.abortPrefetch = (requestId) => {
  // Not easily implementable on web
  console.warn('Image.abortPrefetch is not supported on web');
};

ImageWeb.queryCache = (urls) => {
  if (Platform.OS !== 'web') return Promise.resolve({});
  
  // Return a simple cache status - web browsers handle caching automatically
  const result = {};
  urls.forEach(url => {
    result[url] = 'memory'; // Assume cached
  });
  return Promise.resolve(result);
};

// Resize mode constants
ImageWeb.resizeMode = {
  cover: 'cover',
  contain: 'contain',
  stretch: 'stretch',
  repeat: 'repeat',
  center: 'center',
};

// Cache policy constants (for compatibility)
ImageWeb.cachePolicy = {
  immutable: 'immutable',
  web: 'web',
  cacheOnly: 'cacheOnly',
};

// Export as default for compatibility
export default ImageWeb;

// Also export named exports for compatibility
export const Image = ImageWeb;
export const getSize = ImageWeb.getSize;
export const getSizeWithHeaders = ImageWeb.getSizeWithHeaders;
export const prefetch = ImageWeb.prefetch;
export const abortPrefetch = ImageWeb.abortPrefetch;
export const queryCache = ImageWeb.queryCache;
export const resizeMode = ImageWeb.resizeMode;
export const cachePolicy = ImageWeb.cachePolicy;

// Additional web-specific utilities
export const webImageUtils = {
  /**
   * Convert base64 to blob URL
   */
  base64ToBlob: (base64, mimeType = 'image/png') => {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });
    return URL.createObjectURL(blob);
  },

  /**
   * Load image with progress tracking
   */
  loadWithProgress: (url, onProgress) => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';
      
      xhr.onprogress = (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = (event.loaded / event.total) * 100;
          onProgress(progress);
        }
      };
      
      xhr.onload = () => {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const objectURL = URL.createObjectURL(blob);
          resolve(objectURL);
        } else {
          reject(new Error(`HTTP ${xhr.status}`));
        }
      };
      
      xhr.onerror = () => reject(new Error('Network error'));
      xhr.send();
    });
  },

  /**
   * Resize image on canvas
   */
  resizeImage: (imageUrl, maxWidth, maxHeight) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Calculate new dimensions
        let { width, height } = img;
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(URL.createObjectURL(blob));
          } else {
            reject(new Error('Canvas to blob conversion failed'));
          }
        });
      };
      img.onerror = () => reject(new Error('Image load failed'));
      img.src = imageUrl;
    });
  },

  /**
   * Get image metadata
   */
  getImageMetadata: (imageUrl) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
          aspectRatio: img.naturalWidth / img.naturalHeight,
          src: img.src,
        });
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = imageUrl;
    });
  },
};
