// src/polyfills/LogBox-web.js
// Web polyfill for React Native LogBox and development utilities

// Detect platform without importing React Native to avoid circular dependencies
const Platform = {
  OS: typeof window !== 'undefined' && typeof document !== 'undefined' ? 'web' :
      typeof navigator !== 'undefined' && navigator.product === 'ReactNative' ? 'ios' : 'android'
};

/**
 * Web implementation of LogBox for development error display
 */

const LogBox = {
  /**
   * Install LogBox (no-op on web)
   */
  install: () => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('LogBox: Installed for web platform');
    }
  },

  /**
   * Uninstall LogBox (no-op on web)
   */
  uninstall: () => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('LogBox: Uninstalled for web platform');
    }
  },

  /**
   * Ignore specific warnings
   */
  ignoreLogs: (patterns) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('LogBox: Ignoring log patterns:', patterns);
      // Store ignored patterns for filtering
      LogBox._ignoredPatterns = patterns;
    }
  },

  /**
   * Ignore all logs
   */
  ignoreAllLogs: (ignore = true) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('LogBox: Ignore all logs:', ignore);
      LogBox._ignoreAll = ignore;
    }
  },

  // Internal state
  _ignoredPatterns: [],
  _ignoreAll: false,
};

// Console override for web LogBox functionality
if (__DEV__ && Platform.OS === 'web') {
  const originalConsole = { ...console };

  // Override console methods to implement LogBox filtering
  const createConsoleOverride = (method) => {
    return (...args) => {
      if (LogBox._ignoreAll) return;

      const message = args.join(' ');
      const shouldIgnore = LogBox._ignoredPatterns.some(pattern => {
        if (typeof pattern === 'string') {
          return message.includes(pattern);
        }
        if (pattern instanceof RegExp) {
          return pattern.test(message);
        }
        return false;
      });

      if (!shouldIgnore) {
        originalConsole[method](...args);
      }
    };
  };

  console.warn = createConsoleOverride('warn');
  console.error = createConsoleOverride('error');
  console.log = createConsoleOverride('log');
}

// YellowBox compatibility (deprecated but might be referenced)
const YellowBox = {
  ignoreWarnings: (warnings) => {
    LogBox.ignoreLogs(warnings);
  },
};

// ExceptionsManager for web
const ExceptionsManager = {
  handleException: (error, isFatal) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.error('ExceptionsManager:', isFatal ? 'Fatal error:' : 'Error:', error);
      
      // Show error in a more prominent way for fatal errors
      if (isFatal) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 0, 0, 0.9);
          color: white;
          padding: 20px;
          font-family: monospace;
          font-size: 14px;
          z-index: 999999;
          overflow: auto;
        `;
        errorDiv.innerHTML = `
          <h2>Fatal Error</h2>
          <pre>${error.stack || error.message || error}</pre>
          <button onclick="this.parentElement.remove()" style="margin-top: 20px; padding: 10px;">Close</button>
        `;
        document.body.appendChild(errorDiv);
      }
    }
  },

  updateExceptionMessage: (message, stack, exceptionId) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.error('ExceptionsManager: Updated exception:', { message, stack, exceptionId });
    }
  },

  dismissRedbox: () => {
    if (__DEV__ && Platform.OS === 'web') {
      // Remove any error overlays
      const errorDivs = document.querySelectorAll('[style*="rgba(255, 0, 0, 0.9)"]');
      errorDivs.forEach(div => div.remove());
    }
  },
};

// HMRClient for web (Hot Module Reload)
const HMRClient = {
  setup: (platform, bundleEntry, host, port, path) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('HMRClient: Setup for web platform');
      // Web has its own HMR through Webpack/Metro
    }
  },

  enable: () => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('HMRClient: Enabled for web platform');
    }
  },

  disable: () => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('HMRClient: Disabled for web platform');
    }
  },

  registerBundle: (requestUrl) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('HMRClient: Registered bundle:', requestUrl);
    }
  },

  log: (level, data) => {
    if (__DEV__ && Platform.OS === 'web') {
      console[level]('HMRClient:', data);
    }
  },
};

// Performance monitoring for web
const Performance = {
  mark: (name) => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.mark) {
      performance.mark(name);
    }
  },

  measure: (name, startMark, endMark) => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.measure) {
      try {
        performance.measure(name, startMark, endMark);
      } catch (error) {
        console.warn('Performance.measure failed:', error);
      }
    }
  },

  clearMarks: (name) => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.clearMarks) {
      performance.clearMarks(name);
    }
  },

  clearMeasures: (name) => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.clearMeasures) {
      performance.clearMeasures(name);
    }
  },

  getEntriesByName: (name, type) => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.getEntriesByName) {
      return performance.getEntriesByName(name, type);
    }
    return [];
  },

  getEntriesByType: (type) => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.getEntriesByType) {
      return performance.getEntriesByType(type);
    }
    return [];
  },

  now: () => {
    if (Platform.OS === 'web' && typeof performance !== 'undefined' && performance.now) {
      return performance.now();
    }
    return Date.now();
  },
};

// DevSettings for web
const DevSettings = {
  addMenuItem: (title, handler) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('DevSettings: Added menu item:', title);
      // Could implement a dev menu overlay here
    }
  },

  addListener: (event, handler) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('DevSettings: Added listener for:', event);
      return { remove: () => {} };
    }
    return { remove: () => {} };
  },

  removeListener: (event, handler) => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('DevSettings: Removed listener for:', event);
    }
  },

  reload: () => {
    if (__DEV__ && Platform.OS === 'web') {
      window.location.reload();
    }
  },

  onFastRefresh: () => {
    if (__DEV__ && Platform.OS === 'web') {
      console.log('DevSettings: Fast refresh triggered');
    }
  },
};

// Global error handler for web
if (__DEV__ && Platform.OS === 'web') {
  window.addEventListener('error', (event) => {
    ExceptionsManager.handleException(event.error, true);
  });

  window.addEventListener('unhandledrejection', (event) => {
    ExceptionsManager.handleException(event.reason, false);
  });
}

// Export all development utilities
export default LogBox;

export {
  LogBox,
  YellowBox,
  ExceptionsManager,
  HMRClient,
  Performance,
  DevSettings,
};

// Web-specific development utilities
export const webDevUtilities = {
  /**
   * Create a development overlay
   */
  createDevOverlay: (content) => {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      z-index: 999999;
      max-width: 300px;
    `;
    overlay.innerHTML = content;
    document.body.appendChild(overlay);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (overlay.parentElement) {
        overlay.parentElement.removeChild(overlay);
      }
    }, 5000);
    
    return overlay;
  },

  /**
   * Log performance metrics
   */
  logPerformanceMetrics: () => {
    if (typeof performance !== 'undefined') {
      const navigation = performance.getEntriesByType('navigation')[0];
      if (navigation) {
        console.log('Performance Metrics:', {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalTime: navigation.loadEventEnd - navigation.fetchStart,
        });
      }
    }
  },

  /**
   * Monitor memory usage
   */
  monitorMemory: () => {
    if (typeof performance !== 'undefined' && performance.memory) {
      const memory = performance.memory;
      console.log('Memory Usage:', {
        used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB',
      });
    }
  },
};
