// src/polyfills/RCTAlertManager-web.js
// Web polyfill for React Native RCTAlertManager

import { Platform } from 'react-native';

/**
 * Web implementation of RCTAlertManager using browser alert/confirm APIs
 * Provides the same API as React Native's Alert component
 */

const RCTAlertManager = {
  /**
   * Show an alert dialog
   */
  alertWithArgs: (args) => {
    if (Platform.OS !== 'web') {
      return;
    }

    const { title, message, buttons, type } = args;
    
    // For web, we'll use browser's built-in alert/confirm
    if (!buttons || buttons.length === 0) {
      // Simple alert with just OK button
      window.alert(`${title}\n\n${message}`);
      return;
    }

    if (buttons.length === 1) {
      // Single button alert
      window.alert(`${title}\n\n${message}`);
      if (buttons[0].onPress) {
        buttons[0].onPress();
      }
      return;
    }

    if (buttons.length === 2) {
      // Two button alert - use confirm
      const result = window.confirm(`${title}\n\n${message}`);
      
      // Find cancel and OK buttons
      const cancelButton = buttons.find(btn => btn.style === 'cancel') || buttons[0];
      const okButton = buttons.find(btn => btn.style !== 'cancel') || buttons[1];
      
      if (result && okButton.onPress) {
        okButton.onPress();
      } else if (!result && cancelButton.onPress) {
        cancelButton.onPress();
      }
      return;
    }

    // For more than 2 buttons, create a custom modal
    createCustomAlert(title, message, buttons);
  },

  /**
   * Show a prompt dialog (web-specific implementation)
   */
  prompt: (title, message, callbackOrButtons, type, defaultValue, keyboardType) => {
    if (Platform.OS !== 'web') {
      return;
    }

    const result = window.prompt(`${title}\n\n${message}`, defaultValue || '');
    
    if (typeof callbackOrButtons === 'function') {
      // Single callback function
      callbackOrButtons(result);
    } else if (Array.isArray(callbackOrButtons)) {
      // Array of buttons
      if (result !== null && callbackOrButtons[1] && callbackOrButtons[1].onPress) {
        callbackOrButtons[1].onPress(result);
      } else if (result === null && callbackOrButtons[0] && callbackOrButtons[0].onPress) {
        callbackOrButtons[0].onPress();
      }
    }
  },
};

/**
 * Create a custom alert modal for multiple buttons
 */
function createCustomAlert(title, message, buttons) {
  // Create modal overlay
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  // Create modal content
  const modal = document.createElement('div');
  modal.style.cssText = `
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `;

  // Create title
  if (title) {
    const titleElement = document.createElement('h3');
    titleElement.textContent = title;
    titleElement.style.cssText = `
      margin: 0 0 10px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    `;
    modal.appendChild(titleElement);
  }

  // Create message
  if (message) {
    const messageElement = document.createElement('p');
    messageElement.textContent = message;
    messageElement.style.cssText = `
      margin: 0 0 20px 0;
      font-size: 14px;
      line-height: 1.4;
      color: #666;
    `;
    modal.appendChild(messageElement);
  }

  // Create buttons container
  const buttonsContainer = document.createElement('div');
  buttonsContainer.style.cssText = `
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  `;

  // Create buttons
  buttons.forEach((button, index) => {
    const buttonElement = document.createElement('button');
    buttonElement.textContent = button.text || 'OK';
    
    const isCancel = button.style === 'cancel';
    const isDestructive = button.style === 'destructive';
    
    buttonElement.style.cssText = `
      padding: 8px 16px;
      border: 1px solid ${isCancel ? '#ccc' : isDestructive ? '#ff3b30' : '#007aff'};
      background-color: ${isCancel ? 'white' : isDestructive ? '#ff3b30' : '#007aff'};
      color: ${isCancel ? '#333' : 'white'};
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: ${isCancel ? 'normal' : '600'};
      min-width: 60px;
    `;

    buttonElement.addEventListener('click', () => {
      document.body.removeChild(overlay);
      if (button.onPress) {
        button.onPress();
      }
    });

    buttonsContainer.appendChild(buttonElement);
  });

  modal.appendChild(buttonsContainer);
  overlay.appendChild(modal);

  // Close on overlay click
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      document.body.removeChild(overlay);
      // Call cancel button if exists
      const cancelButton = buttons.find(btn => btn.style === 'cancel');
      if (cancelButton && cancelButton.onPress) {
        cancelButton.onPress();
      }
    }
  });

  // Close on escape key
  const handleEscape = (e) => {
    if (e.key === 'Escape') {
      document.body.removeChild(overlay);
      document.removeEventListener('keydown', handleEscape);
      // Call cancel button if exists
      const cancelButton = buttons.find(btn => btn.style === 'cancel');
      if (cancelButton && cancelButton.onPress) {
        cancelButton.onPress();
      }
    }
  };
  document.addEventListener('keydown', handleEscape);

  document.body.appendChild(overlay);
}

// Export as default for compatibility
export default RCTAlertManager;

// Also export named exports for compatibility
export const alertWithArgs = RCTAlertManager.alertWithArgs;
export const prompt = RCTAlertManager.prompt;

// Additional utility functions
export const showAlert = (title, message, buttons) => {
  RCTAlertManager.alertWithArgs({ title, message, buttons });
};

export const showConfirm = (title, message, onConfirm, onCancel) => {
  const buttons = [
    { text: 'Cancel', style: 'cancel', onPress: onCancel },
    { text: 'OK', onPress: onConfirm },
  ];
  RCTAlertManager.alertWithArgs({ title, message, buttons });
};

export const showPrompt = (title, message, onSubmit, onCancel, defaultValue) => {
  RCTAlertManager.prompt(title, message, [
    { text: 'Cancel', style: 'cancel', onPress: onCancel },
    { text: 'OK', onPress: onSubmit },
  ], 'plain-text', defaultValue);
};

// Web-specific alert utilities
export const webAlertUtils = {
  /**
   * Show a toast-like notification
   */
  showToast: (message, duration = 3000) => {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10001;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, duration);
  },

  /**
   * Show a loading alert
   */
  showLoading: (message = 'Loading...') => {
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
      background-color: white;
      border-radius: 8px;
      padding: 30px;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;

    const spinner = document.createElement('div');
    spinner.style.cssText = `
      width: 30px;
      height: 30px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007aff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px auto;
    `;

    const messageElement = document.createElement('p');
    messageElement.textContent = message;
    messageElement.style.cssText = `
      margin: 0;
      font-size: 14px;
      color: #666;
    `;

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    modal.appendChild(spinner);
    modal.appendChild(messageElement);
    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    return overlay;
  },

  /**
   * Hide loading alert
   */
  hideLoading: () => {
    const overlay = document.getElementById('loading-overlay');
    if (overlay && overlay.parentNode) {
      overlay.parentNode.removeChild(overlay);
    }
  },
};
