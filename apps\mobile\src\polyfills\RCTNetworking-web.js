// src/polyfills/RCTNetworking-web.js
// Web polyfill for React Native RCTNetworking module

// Detect platform without importing React Native to avoid circular dependencies
const Platform = {
  OS: typeof window !== 'undefined' && typeof document !== 'undefined' ? 'web' :
      typeof navigator !== 'undefined' && navigator.product === 'ReactNative' ? 'ios' : 'android'
};

/**
 * Web implementation of RCTNetworking using fetch API and XMLHttpRequest
 * Provides the same API as React Native's networking module
 */

const RCTNetworking = {
  /**
   * Send an HTTP request
   */
  sendRequest: (method, trackingName, url, headers, data, responseType, incrementalUpdates, timeout, callback, withCredentials) => {
    if (Platform.OS !== 'web') {
      return;
    }

    // Create a unique request ID
    const requestId = Math.random().toString(36).substr(2, 9);

    // Prepare request options
    const options = {
      method: method.toUpperCase(),
      headers: headers || {},
      credentials: withCredentials ? 'include' : 'same-origin',
    };

    // Add body for POST, PUT, PATCH requests
    if (data && ['POST', 'PUT', 'PATCH'].includes(options.method)) {
      if (typeof data === 'string') {
        options.body = data;
      } else if (data instanceof FormData) {
        options.body = data;
      } else {
        options.body = JSON.stringify(data);
        if (!options.headers['Content-Type']) {
          options.headers['Content-Type'] = 'application/json';
        }
      }
    }

    // Set timeout if specified
    const controller = new AbortController();
    if (timeout > 0) {
      setTimeout(() => controller.abort(), timeout);
      options.signal = controller.signal;
    }

    // Make the request
    fetch(url, options)
      .then(response => {
        const responseHeaders = {};
        response.headers.forEach((value, key) => {
          responseHeaders[key] = value;
        });

        // Handle different response types
        if (responseType === 'text' || !responseType) {
          return response.text().then(text => ({
            status: response.status,
            statusText: response.statusText,
            headers: responseHeaders,
            url: response.url,
            text: text,
          }));
        } else if (responseType === 'blob') {
          return response.blob().then(blob => ({
            status: response.status,
            statusText: response.statusText,
            headers: responseHeaders,
            url: response.url,
            blob: blob,
          }));
        } else {
          return response.text().then(text => ({
            status: response.status,
            statusText: response.statusText,
            headers: responseHeaders,
            url: response.url,
            text: text,
          }));
        }
      })
      .then(result => {
        if (callback) {
          callback(null, result);
        }
      })
      .catch(error => {
        if (callback) {
          callback({
            message: error.message,
            code: error.name === 'AbortError' ? 'TIMEOUT' : 'NETWORK_ERROR',
          }, null);
        }
      });

    return requestId;
  },

  /**
   * Abort a request
   */
  abortRequest: (requestId) => {
    // In a real implementation, we would track active requests
    // For now, we'll just log the abort attempt
    if (__DEV__) {
      console.log('RCTNetworking: Abort request', requestId);
    }
  },

  /**
   * Clear cookies (web implementation)
   */
  clearCookies: (callback) => {
    if (Platform.OS === 'web') {
      // Clear all cookies for the current domain
      document.cookie.split(";").forEach(cookie => {
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
      });
    }
    
    if (callback) {
      callback(true);
    }
  },

  /**
   * Add request interceptor
   */
  addRequestInterceptor: (interceptor) => {
    // Store interceptor for future use
    if (!RCTNetworking._requestInterceptors) {
      RCTNetworking._requestInterceptors = [];
    }
    RCTNetworking._requestInterceptors.push(interceptor);
  },

  /**
   * Add response interceptor
   */
  addResponseInterceptor: (interceptor) => {
    // Store interceptor for future use
    if (!RCTNetworking._responseInterceptors) {
      RCTNetworking._responseInterceptors = [];
    }
    RCTNetworking._responseInterceptors.push(interceptor);
  },

  /**
   * Remove interceptor
   */
  removeInterceptor: (interceptor) => {
    if (RCTNetworking._requestInterceptors) {
      const index = RCTNetworking._requestInterceptors.indexOf(interceptor);
      if (index > -1) {
        RCTNetworking._requestInterceptors.splice(index, 1);
      }
    }
    if (RCTNetworking._responseInterceptors) {
      const index = RCTNetworking._responseInterceptors.indexOf(interceptor);
      if (index > -1) {
        RCTNetworking._responseInterceptors.splice(index, 1);
      }
    }
  },

  /**
   * Get network state
   */
  getCurrentConnectivity: (callback) => {
    if (Platform.OS === 'web') {
      const isOnline = navigator.onLine;
      const connectionType = navigator.connection ? navigator.connection.effectiveType : 'unknown';
      
      if (callback) {
        callback({
          network_info: isOnline ? 'wifi' : 'none',
          connection_type: connectionType,
          is_connected: isOnline,
        });
      }
    }
  },

  /**
   * Add connectivity listener
   */
  addConnectivityListener: (callback) => {
    if (Platform.OS === 'web') {
      const handleOnline = () => callback({ isConnected: true });
      const handleOffline = () => callback({ isConnected: false });
      
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      // Return cleanup function
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
    return () => {};
  },

  /**
   * Remove connectivity listener
   */
  removeConnectivityListener: (listener) => {
    if (typeof listener === 'function') {
      listener();
    }
  },
};

// XMLHttpRequest polyfill extensions for React Native compatibility
if (Platform.OS === 'web' && typeof XMLHttpRequest !== 'undefined') {
  // Enhance XMLHttpRequest with React Native specific methods
  const originalXHR = XMLHttpRequest;
  
  // Override XMLHttpRequest to add React Native compatibility
  window.XMLHttpRequest = function() {
    const xhr = new originalXHR();
    
    // Add React Native specific properties
    xhr._method = null;
    xhr._url = null;
    xhr._requestHeaders = {};
    xhr._responseHeaders = {};
    
    // Override open method
    const originalOpen = xhr.open;
    xhr.open = function(method, url, async, user, password) {
      xhr._method = method;
      xhr._url = url;
      return originalOpen.call(this, method, url, async, user, password);
    };
    
    // Override setRequestHeader method
    const originalSetRequestHeader = xhr.setRequestHeader;
    xhr.setRequestHeader = function(header, value) {
      xhr._requestHeaders[header] = value;
      return originalSetRequestHeader.call(this, header, value);
    };
    
    // Override getAllResponseHeaders method
    const originalGetAllResponseHeaders = xhr.getAllResponseHeaders;
    xhr.getAllResponseHeaders = function() {
      const headers = originalGetAllResponseHeaders.call(this);
      if (headers) {
        // Parse headers into object format
        headers.split('\r\n').forEach(line => {
          const parts = line.split(': ');
          if (parts.length === 2) {
            xhr._responseHeaders[parts[0]] = parts[1];
          }
        });
      }
      return headers;
    };
    
    // Add React Native specific methods
    xhr.getResponseHeader = function(header) {
      return xhr._responseHeaders[header] || originalXHR.prototype.getResponseHeader.call(this, header);
    };
    
    return xhr;
  };
  
  // Copy static properties
  Object.setPrototypeOf(window.XMLHttpRequest, originalXHR);
  Object.getOwnPropertyNames(originalXHR).forEach(prop => {
    if (typeof originalXHR[prop] !== 'function') {
      window.XMLHttpRequest[prop] = originalXHR[prop];
    }
  });
}

// Export as default for compatibility
export default RCTNetworking;

// Also export named exports for compatibility
export const sendRequest = RCTNetworking.sendRequest;
export const abortRequest = RCTNetworking.abortRequest;
export const clearCookies = RCTNetworking.clearCookies;
export const addRequestInterceptor = RCTNetworking.addRequestInterceptor;
export const addResponseInterceptor = RCTNetworking.addResponseInterceptor;
export const removeInterceptor = RCTNetworking.removeInterceptor;
export const getCurrentConnectivity = RCTNetworking.getCurrentConnectivity;
export const addConnectivityListener = RCTNetworking.addConnectivityListener;
export const removeConnectivityListener = RCTNetworking.removeConnectivityListener;

// Utility functions for network operations
export const networkUtils = {
  /**
   * Check if URL is valid
   */
  isValidUrl: (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Parse response headers
   */
  parseHeaders: (headersString) => {
    const headers = {};
    if (headersString) {
      headersString.split('\r\n').forEach(line => {
        const parts = line.split(': ');
        if (parts.length === 2) {
          headers[parts[0]] = parts[1];
        }
      });
    }
    return headers;
  },

  /**
   * Create form data from object
   */
  createFormData: (data) => {
    const formData = new FormData();
    Object.keys(data).forEach(key => {
      formData.append(key, data[key]);
    });
    return formData;
  },

  /**
   * Get content type from headers
   */
  getContentType: (headers) => {
    const contentType = headers['Content-Type'] || headers['content-type'];
    return contentType ? contentType.split(';')[0] : 'text/plain';
  },

  /**
   * Check if response is JSON
   */
  isJsonResponse: (headers) => {
    const contentType = networkUtils.getContentType(headers);
    return contentType.includes('application/json');
  },

  /**
   * Parse JSON safely
   */
  parseJson: (text) => {
    try {
      return JSON.parse(text);
    } catch {
      return null;
    }
  },
};
