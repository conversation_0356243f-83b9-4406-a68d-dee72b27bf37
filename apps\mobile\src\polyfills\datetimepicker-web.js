// src/polyfills/datetimepicker-web.js
// Web polyfill for @react-native-community/datetimepicker

import React from 'react';
import { Platform } from 'react-native';

/**
 * Web implementation of DateTimePicker using HTML5 input elements
 * Provides the same API as @react-native-community/datetimepicker
 */
const DateTimePickerWeb = ({
  value,
  mode = 'date',
  display = 'default',
  onChange,
  minimumDate,
  maximumDate,
  locale = 'en-US',
  timeZoneOffsetInMinutes,
  textColor,
  accentColor,
  themeVariant,
  style,
  disabled = false,
  ...otherProps
}) => {
  // Only render on web platform
  if (Platform.OS !== 'web') {
    return null;
  }

  // Convert React Native date to HTML input format
  const formatDateForInput = (date) => {
    if (!date) return '';
    
    if (mode === 'date') {
      return date.toISOString().split('T')[0];
    } else if (mode === 'time') {
      return date.toTimeString().split(' ')[0].substring(0, 5);
    } else if (mode === 'datetime') {
      return date.toISOString().slice(0, 16);
    }
    return '';
  };

  // Convert HTML input value back to Date object
  const parseInputValue = (inputValue) => {
    if (!inputValue) return new Date();
    
    if (mode === 'date') {
      return new Date(inputValue + 'T00:00:00');
    } else if (mode === 'time') {
      const today = new Date();
      const [hours, minutes] = inputValue.split(':');
      today.setHours(parseInt(hours, 10));
      today.setMinutes(parseInt(minutes, 10));
      today.setSeconds(0);
      today.setMilliseconds(0);
      return today;
    } else if (mode === 'datetime') {
      return new Date(inputValue);
    }
    return new Date();
  };

  // Handle input change
  const handleChange = (event) => {
    const inputValue = event.target.value;
    const newDate = parseInputValue(inputValue);
    
    if (onChange) {
      // Mimic the React Native DateTimePicker onChange signature
      onChange(event, newDate);
    }
  };

  // Determine HTML input type based on mode
  const getInputType = () => {
    switch (mode) {
      case 'date':
        return 'date';
      case 'time':
        return 'time';
      case 'datetime':
        return 'datetime-local';
      default:
        return 'date';
    }
  };

  // Format min/max dates for HTML input
  const formatMinMaxDate = (date) => {
    if (!date) return undefined;
    
    if (mode === 'date') {
      return date.toISOString().split('T')[0];
    } else if (mode === 'datetime') {
      return date.toISOString().slice(0, 16);
    }
    return undefined;
  };

  // Base styles for the input
  const inputStyles = {
    padding: '8px 12px',
    borderRadius: '6px',
    border: '1px solid #ccc',
    fontSize: '16px',
    fontFamily: 'inherit',
    backgroundColor: disabled ? '#f5f5f5' : '#fff',
    color: textColor || (disabled ? '#999' : '#333'),
    cursor: disabled ? 'not-allowed' : 'pointer',
    outline: 'none',
    transition: 'border-color 0.2s ease',
    ...style,
  };

  // Focus styles
  const focusStyles = {
    borderColor: accentColor || '#007AFF',
    boxShadow: `0 0 0 2px ${accentColor || '#007AFF'}20`,
  };

  return React.createElement('input', {
    type: getInputType(),
    value: formatDateForInput(value),
    onChange: handleChange,
    min: formatMinMaxDate(minimumDate),
    max: formatMinMaxDate(maximumDate),
    disabled,
    style: inputStyles,
    onFocus: (e) => {
      Object.assign(e.target.style, focusStyles);
    },
    onBlur: (e) => {
      e.target.style.borderColor = '#ccc';
      e.target.style.boxShadow = 'none';
    },
    ...otherProps,
  });
};

// Default export for compatibility
export default DateTimePickerWeb;

// Named exports for compatibility
export { DateTimePickerWeb };

// Export constants that might be used
export const DateTimePickerModes = {
  DATE: 'date',
  TIME: 'time',
  DATETIME: 'datetime',
};

export const DateTimePickerDisplays = {
  DEFAULT: 'default',
  SPINNER: 'spinner',
  COMPACT: 'compact',
  INLINE: 'inline',
};

// Export event types for compatibility
export const DateTimePickerEvent = {
  SET: 'set',
  DISMISSED: 'dismissed',
  NEUTRAL_BUTTON: 'neutralButtonPressed',
};

// Additional utility functions
export const formatDate = (date, mode = 'date', locale = 'en-US') => {
  if (!date) return '';
  
  const options = {};
  
  if (mode === 'date') {
    options.year = 'numeric';
    options.month = '2-digit';
    options.day = '2-digit';
  } else if (mode === 'time') {
    options.hour = '2-digit';
    options.minute = '2-digit';
  } else if (mode === 'datetime') {
    options.year = 'numeric';
    options.month = '2-digit';
    options.day = '2-digit';
    options.hour = '2-digit';
    options.minute = '2-digit';
  }
  
  return date.toLocaleDateString(locale, options);
};

// Validation utilities
export const isValidDate = (date) => {
  return date instanceof Date && !isNaN(date.getTime());
};

export const isDateInRange = (date, minDate, maxDate) => {
  if (!isValidDate(date)) return false;
  
  if (minDate && date < minDate) return false;
  if (maxDate && date > maxDate) return false;
  
  return true;
};

// Theme utilities
export const getThemeColors = (themeVariant = 'light') => {
  if (themeVariant === 'dark') {
    return {
      backgroundColor: '#1c1c1e',
      textColor: '#ffffff',
      borderColor: '#38383a',
      accentColor: '#0a84ff',
    };
  }
  
  return {
    backgroundColor: '#ffffff',
    textColor: '#000000',
    borderColor: '#c6c6c8',
    accentColor: '#007aff',
  };
};
