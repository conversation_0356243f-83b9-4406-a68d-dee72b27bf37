// src/polyfills/expo-device-web.js
// Web polyfill for expo-device module

import { Platform } from 'react-native';

// Mock expo-device for web platform
const Device = {
  isDevice: Platform.OS !== 'web',
  brand: Platform.OS === 'web' ? 'Web' : null,
  manufacturer: Platform.OS === 'web' ? 'Browser' : null,
  modelName: Platform.OS === 'web' ? 'Web Browser' : null,
  modelId: Platform.OS === 'web' ? 'web-browser' : null,
  designName: Platform.OS === 'web' ? 'Web' : null,
  productName: Platform.OS === 'web' ? 'Web Browser' : null,
  deviceYearClass: Platform.OS === 'web' ? 2023 : null,
  totalMemory: Platform.OS === 'web' ? 8000000000 : null, // 8GB default
  supportedCpuArchitectures: Platform.OS === 'web' ? ['x86_64'] : null,
  osName: Platform.OS === 'web' ? 'Web' : null,
  osVersion: Platform.OS === 'web' ? '1.0.0' : null,
  osBuildId: Platform.OS === 'web' ? 'web-build' : null,
  osInternalBuildId: Platform.OS === 'web' ? 'web-internal' : null,
  deviceName: Platform.OS === 'web' ? 'Web Browser' : null,
  deviceType: Platform.OS === 'web' ? 'DESKTOP' : null,
  
  // Device type constants
  DeviceType: {
    UNKNOWN: 0,
    PHONE: 1,
    TABLET: 2,
    DESKTOP: 3,
    TV: 4,
  },
  
  // Platform OS constants
  PlatformOS: {
    IOS: 'ios',
    ANDROID: 'android',
    WEB: 'web',
    WINDOWS: 'windows',
    MACOS: 'macos',
  },
};

// Export as both default and named export for compatibility
export default Device;
export const isDevice = Device.isDevice;
export const brand = Device.brand;
export const manufacturer = Device.manufacturer;
export const modelName = Device.modelName;
export const modelId = Device.modelId;
export const designName = Device.designName;
export const productName = Device.productName;
export const deviceYearClass = Device.deviceYearClass;
export const totalMemory = Device.totalMemory;
export const supportedCpuArchitectures = Device.supportedCpuArchitectures;
export const osName = Device.osName;
export const osVersion = Device.osVersion;
export const osBuildId = Device.osBuildId;
export const osInternalBuildId = Device.osInternalBuildId;
export const deviceName = Device.deviceName;
export const deviceType = Device.deviceType;
export const DeviceType = Device.DeviceType;
export const PlatformOS = Device.PlatformOS;
