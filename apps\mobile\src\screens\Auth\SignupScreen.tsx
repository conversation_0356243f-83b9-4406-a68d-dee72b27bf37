//src/screens/Auth/SignupScreen.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAuth } from '../../context/AuthContext';
import AuthAPI from '../../api/auth.api';

type Role = 'HOMEOWNER' | 'TECHNICIAN';

export default function SignupScreen() {
  const { login } = useAuth();

  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<Role>('HOMEOWNER');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSignup = async () => {
    setError('');
    if (!fullName || !email || !password || !phone) {
      setError('All fields are required');
      return;
    }

    if (!email.includes('@')) {
      setError('Invalid email address');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    try {
      setLoading(true);

      if (__DEV__) {
        console.log('🔐 [SignupScreen] Attempting signup with:', { fullName, email, phone, role });
      }

      const res = await AuthAPI.signup({ fullName, email, phone, password, role });

      if (__DEV__) {
        console.log('✅ [SignupScreen] Signup response received');
      }

      const { token } = res;
      if (!token) throw new Error('No token returned');

      await login(token);
    } catch (err: any) {
      if (__DEV__) {
        console.error('❌ [SignupScreen] Signup failed:', err);
        console.error('📄 [SignupScreen] Error details:', err.response?.data || err);
      }

      setError(err.response?.data?.message || err.message || 'Signup failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={(Platform.OS === 'ios' ? 'padding' : undefined) as 'padding' | undefined}
      >
        <Text style={styles.title}>Create Account</Text>

        {error && (
          <View style={styles.errorBox}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        <TextInput
          style={styles.input}
          placeholder="Full Name"
          value={fullName}
          onChangeText={setFullName}
        />
        <TextInput
          style={styles.input}
          placeholder="Phone"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
        />
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          autoCorrect={false}
          autoComplete="email"
          textContentType="emailAddress"
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />

        <View style={styles.roleSection}>
          <Text style={styles.roleLabel}>I am a:</Text>
          <View style={styles.roleSwitcher}>
            <TouchableOpacity
              style={[styles.roleButton, role === 'HOMEOWNER' && styles.roleButtonActive]}
              onPress={() => setRole('HOMEOWNER')}
              accessibilityLabel="Select Homeowner role"
              accessibilityRole="button"
            >
              <Text style={styles.roleIcon}>🏠</Text>
              <Text style={[styles.roleText, role === 'HOMEOWNER' && styles.roleTextActive]}>
                Homeowner
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.roleButton, role === 'TECHNICIAN' && styles.roleButtonActive]}
              onPress={() => setRole('TECHNICIAN')}
              accessibilityLabel="Select Technician role"
              accessibilityRole="button"
            >
              <Text style={styles.roleIcon}>🔧</Text>
              <Text style={[styles.roleText, role === 'TECHNICIAN' && styles.roleTextActive]}>
                Technician
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.button, loading && styles.disabledButton]}
          onPress={handleSignup}
          disabled={loading}
        >
          {loading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Sign Up</Text>}
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.md,
    backgroundColor: colors.background.primary,
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.lg,
    textAlign: 'center',
    color: colors.text.primary,
  },
  input: {
    height: 50,
    borderColor: colors.border.medium,
    borderWidth: 1,
    borderRadius: borderRadius.sm,
    marginBottom: spacing.sm,
    paddingHorizontal: spacing.sm,
    fontSize: typography.fontSize.base,
    color: colors.text.primary,
    backgroundColor: colors.background.primary,
  },
  button: {
    height: 50,
    backgroundColor: colors.primary.main,
    borderRadius: borderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.md,
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonText: {
    color: colors.text.inverse,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.bold,
  },
  errorBox: {
    backgroundColor: colors.error.light,
    padding: spacing.sm,
    marginBottom: spacing.sm,
    borderRadius: borderRadius.sm,
  },
  errorText: {
    color: colors.error.dark,
    textAlign: 'center',
    fontSize: typography.fontSize.sm,
  },
  roleSection: {
    marginBottom: spacing.md,
  },
  roleLabel: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  roleSwitcher: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  roleButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.full,
    borderWidth: 1,
    borderColor: colors.border.medium,
    marginHorizontal: spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
  },
  roleButtonActive: {
    backgroundColor: colors.primary.light,
    borderColor: colors.primary.main,
  },
  roleIcon: {
    fontSize: typography.fontSize.lg,
    marginRight: spacing.xs,
  },
  roleText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
  },
  roleTextActive: {
    fontWeight: typography.fontWeight.bold,
    color: colors.primary.main,
  },
});
