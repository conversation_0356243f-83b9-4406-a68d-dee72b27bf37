// CTRON Home - Chat List Screen
// Display all active chats for the current user

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { colors, spacing, typography, borderRadius } from '../../styles/theme';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { Button } from '../../components/ui/Button';
import { ChatAPI } from '../../api/chat.api';

interface Chat {
  id: string;
  jobId: string;
  status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED';
  createdAt: string;
  updatedAt: string;
  job: {
    id: string;
    title: string;
    description: string;
    status: string;
  };
  participants: Array<{
    id: string;
    userId: string;
    user: {
      id: string;
      fullName: string;
      role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
    };
  }>;
  lastMessage?: {
    id: string;
    content: string;
    createdAt: string;
    sender: {
      id: string;
      fullName: string;
    };
  };
  unreadCount?: number;
}

export default function ChatListScreen() {
  const navigation = useNavigation<any>();
  const { user } = useAuth();

  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      setLoading(true);
      const response = await ChatAPI.getUserChats();
      setChats(response.chats || []);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to load chats',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadChats();
    setRefreshing(false);
  };

  const handleChatPress = (chat: Chat) => {
    navigation.navigate('ChatScreen', {
      chatId: chat.id,
      jobTitle: chat.job.title
    });
  };

  const formatLastMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getOtherParticipant = (chat: Chat) => {
    return chat.participants.find(p => p.userId !== user?.userId)?.user;
  };

  const renderChatItem = ({ item: chat }: { item: Chat }) => {
    const otherParticipant = getOtherParticipant(chat);
    const hasUnread = (chat.unreadCount || 0) > 0;

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => handleChatPress(chat)}
        accessibilityLabel={`Chat for ${chat.job.title}`}
        accessibilityRole="button"
      >
        <Card style={[styles.chatCard, hasUnread && styles.unreadChatCard] as any}>
          <View style={styles.chatHeader}>
            <View style={styles.chatInfo}>
              <Text style={[styles.jobTitle, hasUnread && styles.unreadText]}>
                {chat.job.title}
              </Text>
              <Text style={styles.participantName}>
                with {otherParticipant?.fullName || 'Unknown User'}
              </Text>
            </View>
            <View style={styles.chatMeta}>
              {chat.lastMessage && (
                <Text style={styles.timestamp}>
                  {formatLastMessageTime(chat.lastMessage.createdAt)}
                </Text>
              )}
              {hasUnread && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>
                    {chat.unreadCount! > 99 ? '99+' : chat.unreadCount}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {chat.lastMessage && (
            <Text style={styles.lastMessage} numberOfLines={2}>
              {chat.lastMessage.sender.fullName}: {chat.lastMessage.content}
            </Text>
          )}

          <View style={styles.chatStatus}>
            <View style={[
              styles.statusIndicator,
              chat.status === 'ACTIVE' && styles.activeStatus,
              chat.status === 'CLOSED' && styles.closedStatus,
              chat.status === 'ARCHIVED' && styles.archivedStatus,
            ]} />
            <Text style={styles.statusText}>
              {chat.status.toLowerCase()}
            </Text>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyTitle}>No Chats Yet</Text>
      <Text style={styles.emptyMessage}>
        Chats will appear here when you start communicating about your jobs.
      </Text>
      <Button
        title="Refresh"
        onPress={handleRefresh}
        variant="ghost"
        size="md"
        style={styles.refreshButton}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <Header title="Messages" />
      <FlatList
        data={chats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },

  listContainer: {
    flexGrow: 1,
    padding: spacing.lg,
  },

  chatItem: {
    marginBottom: spacing.md,
  },

  chatCard: {
    padding: spacing.lg,
  },

  unreadChatCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },

  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },

  chatInfo: {
    flex: 1,
    marginRight: spacing.md,
  },

  jobTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  unreadText: {
    fontWeight: '700' as const,
  },

  participantName: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
  },

  chatMeta: {
    alignItems: 'flex-end',
  },

  timestamp: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.tertiary,
    marginBottom: spacing.xs,
  },

  unreadBadge: {
    backgroundColor: colors.primary[500],
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
  },

  unreadCount: {
    fontSize: typography.fontSize.xs,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.background.primary,
  },

  lastMessage: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
    lineHeight: 20,
  },

  chatStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.sm,
  },

  activeStatus: {
    backgroundColor: (colors.success as any)[500],
  },

  closedStatus: {
    backgroundColor: colors.text.tertiary,
  },

  archivedStatus: {
    backgroundColor: (colors.warning as any)[500],
  },

  statusText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.tertiary,
    textTransform: 'capitalize',
  },

  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },

  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },

  emptyMessage: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.primary,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
    paddingHorizontal: spacing[6],
    lineHeight: 24,
  },

  refreshButton: {
    minWidth: 120,
  },

  refreshIcon: {
    fontSize: 20,
    color: colors.text.secondary,
  },
});
