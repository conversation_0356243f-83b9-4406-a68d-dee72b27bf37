// CTRON Home - Enhanced Homeowner Home Screen
// Main dashboard for homeowners with design system integration

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { useJobs } from '../../context/JobContext';
import type { Job } from '../../types/job';
import { designSystem } from '../../styles/designSystem';

const { colors, spacing, typography, borderRadius, shadows } = designSystem;
import { Button } from '../../components/ui/Button';
import { JobCard } from '../../components/ui/JobCard';
import { Header } from '../../components/ui/Header';
import { Card } from '../../components/ui/Card';

const HomeScreen = () => {
  const navigation = useNavigation<any>();
  const { user, logout } = useAuth();
  const { myJobs = [], refreshJobs, loading } = useJobs();
  const [refreshing, setRefreshing] = useState(false);
  const isMountedRef = useRef(true);

  const jobs: Job[] = myJobs;
  const firstName = user?.fullName?.split(' ')[0] || 'Homeowner';

  const ongoingJobs = jobs.filter(
    job => job.status === 'PENDING' || job.status === 'IN_PROGRESS'
  );
  const completedJobs = jobs.filter(job => job.status === 'COMPLETED');

  const lastCompleted = completedJobs.sort((a, b) =>
    new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  )[0];

  useEffect(() => {
    refreshJobs();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) return;

    setRefreshing(true);
    await refreshJobs();

    if (isMountedRef.current) {
      setRefreshing(false);
    }
  }, [refreshJobs]);

  const mapJobStatus = (status: string) => {
    const statusMap: { [key: string]: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' } = {
      'PENDING': 'pending',
      'ASSIGNED': 'assigned',
      'IN_PROGRESS': 'active',
      'COMPLETED': 'completed',
      'CANCELLED': 'cancelled',
    };
    return statusMap[status] || 'pending';
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>Good morning,</Text>
        <Text style={styles.nameText}>{firstName} 👋</Text>
      </View>

      {/* Quick Action Button */}
      <Button
        title="+ Book a Technician"
        onPress={() => navigation.navigate('BookJob')}
        variant="primary"
        size="lg"
        fullWidth
        style={styles.bookButton}
      />

      {/* Summary Cards */}
      <View style={styles.summaryContainer}>
        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Total Jobs</Text>
          <Text style={styles.summaryValue}>{jobs.length}</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Ongoing</Text>
          <Text style={styles.summaryValue}>{ongoingJobs.length}</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Last Done</Text>
          <Text style={styles.summaryValue}>
            {lastCompleted
              ? new Date(lastCompleted.updatedAt).toLocaleDateString()
              : 'N/A'}
          </Text>
        </Card>
      </View>

      {/* Section Title */}
      <Text style={styles.sectionTitle}>Recent Jobs</Text>
    </View>
  );

  const renderJobItem = useCallback(({ item }: { item: Job }) => {
    const jobData = {
      id: item.id,
      title: `Job #${item.id.slice(0, 8)}`,
      description: item.issue || 'Service request',
      status: mapJobStatus(item.status),
      scheduledAt: item.scheduledAt,
      location: item.address || 'Location not specified',
      price: undefined,
    };

    return (
      <JobCard
        job={jobData}
        onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
        showTechnician={false}
        showPrice={false}
      />
    );
  }, [navigation]);

  const renderEmptyComponent = useCallback(() => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>No jobs yet</Text>
      <Text style={styles.emptyText}>
        Book your first service to get started with CTRON Home
      </Text>
      <Button
        title="Book a Service"
        onPress={() => navigation.navigate('BookJob')}
        variant="primary"
        style={styles.emptyButton}
      />
    </View>
  ), [navigation]);

  return (
    <View style={styles.container}>
      <Header
        title="CTRON Home"
        rightAction={{
          icon: <Text style={styles.logoutIcon}>👤</Text>,
          onPress: () => logout(true),
          accessibilityLabel: 'User menu',
        }}
      />
      <FlatList
        data={jobs.slice(0, 5)} // Show only recent jobs
        keyExtractor={(item) => item.id}
        renderItem={renderJobItem}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        getItemLayout={(data, index) => ({
          length: 120, // Approximate item height
          offset: 120 * index,
          index,
        })}
      />
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  contentContainer: {
    padding: spacing.md,
    paddingBottom: spacing.xxl,
  },
  headerContainer: {
    marginBottom: spacing.lg,
  },
  welcomeSection: {
    marginBottom: spacing.md,
  },
  welcomeText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
  },
  nameText: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  bookButton: {
    marginBottom: spacing.lg,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  summaryCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
    padding: spacing.md,
    backgroundColor: colors.background.card,
    borderRadius: borderRadius.md,
    ...shadows.sm,
  },
  summaryLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  summaryValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  sectionTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
    marginTop: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  emptyButton: {
    width: '100%',
    maxWidth: 200,
  },
  logoutIcon: {
    fontSize: typography.fontSize.lg,
    color: colors.text.primary,
  },
});
