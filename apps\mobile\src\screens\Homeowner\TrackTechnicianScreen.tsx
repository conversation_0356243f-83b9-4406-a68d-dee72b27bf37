// src/screens/Homeowner/TrackTechnicianScreen.tsx

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, RefreshControl, ScrollView } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { lightTheme as theme } from '../../styles/theme';
import { SocketService } from '../../services/socket.service';
import { JobAPI } from '../../api/job.api';
import { formatDistanceToNow } from 'date-fns';

type TrackTechnicianScreenRouteProp = RouteProp<{
  TrackTechnician: { jobId: string };
}, 'TrackTechnician'>;

interface TechnicianLocation {
  lat: number;
  lng: number;
  timestamp: string;
}

interface JobDetails {
  id: string;
  title: string;
  status: string;
  technician: {
    id: string;
    user: {
      fullName: string;
      phone: string;
    };
    specializations: string[];
    rating: number;
  };
  scheduledDate?: string;
  estimatedDuration?: number;
}

const TrackTechnicianScreen = () => {
  const route = useRoute<TrackTechnicianScreenRouteProp>();
  const { jobId } = route.params;

  const [job, setJob] = useState<JobDetails | null>(null);
  const [technicianLocation, setTechnicianLocation] = useState<TechnicianLocation | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [estimatedArrival, setEstimatedArrival] = useState<string | null>(null);

  useEffect(() => {
    loadJobDetails();
    setupSocketListeners();

    return () => {
      // Clean up socket listeners to prevent memory leaks
      const socketService = SocketService;
      socketService.off('technicianLocationUpdate', handleLocationUpdate);
      socketService.off('jobStatusChanged', handleStatusChange);
      socketService.off('jobUpdate', handleJobUpdate);
    };
  }, [jobId]);

  const loadJobDetails = async () => {
    try {
      setLoading(true);
      // const jobData = await jobApi.getJobById(jobId);
      // setJob(jobData);
      console.log('Loading job details for jobId:', jobId);

      // Join job room for real-time updates
      // socketService.emit('joinJob', jobId);
    } catch (error) {
      console.error('Error loading job details:', error);
      Alert.alert('Error', 'Failed to load job details');
    } finally {
      setLoading(false);
    }
  };

  const handleLocationUpdate = (data: any) => {
    setTechnicianLocation(data);
    calculateEstimatedArrival(data);
  };

  const handleStatusChange = (data: any) => {
    if (data.jobId === jobId && job) {
      setJob({ ...job, status: data.status });
    }
  };

  const handleJobUpdate = (data: any) => {
    if (data.id === jobId) {
      setJob(data);
    }
  };

  const setupSocketListeners = () => {
    const socketService = SocketService;

    // Listen for technician location updates
    socketService.on('technicianLocationUpdate', handleLocationUpdate);

    // Listen for job status changes
    socketService.on('jobStatusChanged', handleStatusChange);

    // Listen for job updates
    socketService.on('jobUpdate', handleJobUpdate);

    // Join job room for real-time updates
    socketService.emit('joinJob', jobId);
  };

  const calculateEstimatedArrival = (location: TechnicianLocation) => {
    // This is a simplified calculation - in production you'd use a mapping service
    // For now, we'll estimate based on distance and average speed
    if (job?.scheduledDate) {
      const scheduledTime = new Date(job.scheduledDate);
      const now = new Date();

      if (scheduledTime > now) {
        setEstimatedArrival(formatDistanceToNow(scheduledTime, { addSuffix: true }));
      } else {
        setEstimatedArrival('Should arrive soon');
      }
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJobDetails();
    setRefreshing(false);
  };

  const handleCallTechnician = () => {
    if (job?.technician?.user?.phone) {
      Alert.alert(
        'Call Technician',
        `Call ${job.technician.user.fullName}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Call',
            onPress: () => {
              // In a real app, you'd use Linking.openURL(`tel:${job.technician.user.phone}`)
              Alert.alert('Calling', `Calling ${job.technician.user.phone}`);
            }
          }
        ]
      );
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner size="large" />
        <Text style={styles.loadingText}>Loading tracking information...</Text>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Job not found</Text>
        <Button title="Go Back" onPress={() => {}} variant="outline" />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <Card style={styles.jobCard}>
        <Text style={styles.jobTitle}>{job.title}</Text>
        <Text style={styles.jobStatus}>Status: {job.status.replace('_', ' ')}</Text>
      </Card>

      <Card style={styles.technicianCard}>
        <Text style={styles.sectionTitle}>Your Technician</Text>
        <Text style={styles.technicianName}>{job.technician.user.fullName}</Text>
        <Text style={styles.technicianInfo}>
          Rating: {job.technician.rating}/5 ⭐
        </Text>
        <Text style={styles.technicianInfo}>
          Specializations: {job.technician.specializations.join(', ')}
        </Text>

        <Button
          title="Call Technician"
          onPress={handleCallTechnician}
          variant="outline"
          style={styles.callButton}
        />
      </Card>

      {job.status === 'ASSIGNED' && (
        <Card style={styles.trackingCard}>
          <Text style={styles.sectionTitle}>Tracking Information</Text>

          {estimatedArrival ? (
            <View style={styles.arrivalInfo}>
              <Text style={styles.arrivalText}>Estimated Arrival</Text>
              <Text style={styles.arrivalTime}>{estimatedArrival}</Text>
            </View>
          ) : (
            <Text style={styles.noTrackingText}>
              Technician location will be available when they're on their way
            </Text>
          )}

          {technicianLocation && (
            <View style={styles.locationInfo}>
              <Text style={styles.locationText}>
                Last location update: {formatDistanceToNow(new Date(technicianLocation.timestamp), { addSuffix: true })}
              </Text>
            </View>
          )}
        </Card>
      )}

      {job.status === 'IN_PROGRESS' && (
        <Card style={styles.progressCard}>
          <Text style={styles.sectionTitle}>Work in Progress</Text>
          <Text style={styles.progressText}>
            Your technician is currently working on your job. You'll be notified when it's completed.
          </Text>
        </Card>
      )}

      {job.status === 'COMPLETED' && (
        <Card style={styles.completedCard}>
          <Text style={styles.sectionTitle}>Job Completed</Text>
          <Text style={styles.completedText}>
            Your job has been completed! Please check the job details for completion photos and notes.
          </Text>
        </Card>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral.light,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.neutral.light,
  },
  loadingText: {
    ...typography.body,
    marginTop: spacing.md,
    color: colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.neutral.light,
  },
  errorText: {
    ...typography.heading,
    color: colors.error.main,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  jobCard: {
    margin: spacing.md,
    padding: spacing.lg,
  },
  jobTitle: {
    ...typography.heading,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  jobStatus: {
    ...typography.body,
    color: colors.primary.main,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  technicianCard: {
    margin: spacing.md,
    marginTop: 0,
    padding: spacing.lg,
  },
  sectionTitle: {
    ...typography.subheading,
    color: colors.text.primary,
    marginBottom: spacing.md,
    fontWeight: '600',
  },
  technicianName: {
    ...typography.heading,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  technicianInfo: {
    ...typography.body,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  callButton: {
    marginTop: spacing.md,
  },
  trackingCard: {
    margin: spacing.md,
    marginTop: 0,
    padding: spacing.lg,
    backgroundColor: colors.primary.light,
  },
  arrivalInfo: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  arrivalText: {
    ...typography.body,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  arrivalTime: {
    ...typography.heading,
    color: colors.primary.main,
    fontWeight: 'bold',
  },
  noTrackingText: {
    ...typography.body,
    color: colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  locationInfo: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.neutral.main,
  },
  locationText: {
    ...typography.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  progressCard: {
    margin: spacing.md,
    marginTop: 0,
    padding: spacing.lg,
    backgroundColor: colors.warning.light,
  },
  progressText: {
    ...typography.body,
    color: colors.text.primary,
    textAlign: 'center',
  },
  completedCard: {
    margin: spacing.md,
    marginTop: 0,
    padding: spacing.lg,
    backgroundColor: colors.success.light,
  },
  completedText: {
    ...typography.body,
    color: colors.text.primary,
    textAlign: 'center',
  },
});

export default TrackTechnicianScreen;
