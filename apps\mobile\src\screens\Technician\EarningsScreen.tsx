// CTRON Home - Enhanced Technician Earnings Screen
// Advanced analytics with summary cards and insights

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Platform-aware AsyncStorage import to prevent Android bundling issues
let AsyncStorage: any;
try {
  AsyncStorage = require('@react-native-async-storage/async-storage').default;
} catch (error) {
  // Fallback for platforms where AsyncStorage might not be available
  AsyncStorage = {
    getItem: async (key: string) => null,
    setItem: async (key: string, value: string) => {},
    removeItem: async (key: string) => {},
  };
}

import PaymentAPI from '../../api/payment.api';
import { colors, typography, spacing, borderRadius } from '../../styles/theme';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Time period filter options
 */
type TimePeriod = 'week' | 'month' | 'quarter' | 'year';

/**
 * Enhanced earning interface
 */
interface Earning {
  id: string;
  amount: number;
  date: string;
  jobId: string;
  description: string;
  category: 'job_completion' | 'bonus' | 'tip' | 'overtime';
  status: 'paid' | 'pending' | 'disputed';
  bonus?: number;
}

/**
 * Summary metrics interface
 */
interface SummaryMetrics {
  totalEarnings: number;
  thisMonth: number;
  lastMonth: number;
  averagePerJob: number;
  totalJobs: number;
  pendingAmount: number;
}

/**
 * Enhanced earnings screen component
 */
const EarningsScreen = () => {
  const [earnings, setEarnings] = useState<Earning[]>([]);
  const [filteredEarnings, setFilteredEarnings] = useState<Earning[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('month');
  const [summaryMetrics, setSummaryMetrics] = useState<SummaryMetrics>({
    totalEarnings: 0,
    thisMonth: 0,
    lastMonth: 0,
    averagePerJob: 0,
    totalJobs: 0,
    pendingAmount: 0,
  });

  /**
   * Load earnings data and calculate metrics
   */
  const fetchEarnings = async () => {
    try {
      setLoading(true);
      const res = await PaymentAPI.getEarnings();

      // Transform basic earnings to enhanced format
      const enhancedEarnings: Earning[] = (res.earnings || []).map((earning: any, index: number) => ({
        id: earning.id || `earning_${index}`,
        amount: earning.amount || 0,
        date: earning.date || new Date().toISOString(),
        jobId: earning.jobId || `job_${index}`,
        description: earning.description || 'Job completion payment',
        category: earning.category || 'job_completion',
        status: earning.status || 'paid',
        bonus: earning.bonus || 0,
      }));

      setEarnings(enhancedEarnings);
      calculateSummaryMetrics(enhancedEarnings);
      filterEarningsByPeriod(enhancedEarnings, selectedPeriod);

      // Save to local storage for offline access
      await AsyncStorage.setItem('technician_earnings', JSON.stringify(enhancedEarnings));
    } catch (err: any) {
      console.error('Failed to fetch earnings:', err);
      // Try to load from local storage
      await loadEarningsFromStorage();
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load earnings from local storage
   */
  const loadEarningsFromStorage = async () => {
    try {
      const savedEarnings = await AsyncStorage.getItem('technician_earnings');
      if (savedEarnings) {
        const parsedEarnings = JSON.parse(savedEarnings);
        setEarnings(parsedEarnings);
        calculateSummaryMetrics(parsedEarnings);
        filterEarningsByPeriod(parsedEarnings, selectedPeriod);
      }
    } catch (error) {
      console.error('Failed to load earnings from storage:', error);
    }
  };

  /**
   * Calculate summary metrics from earnings data
   */
  const calculateSummaryMetrics = (earningsData: Earning[]) => {
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();
    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

    const totalEarnings = earningsData.reduce((sum, earning) => sum + earning.amount, 0);
    const thisMonthEarnings = earningsData
      .filter(earning => {
        const earningDate = new Date(earning.date);
        return earningDate.getMonth() === thisMonth && earningDate.getFullYear() === thisYear;
      })
      .reduce((sum, earning) => sum + earning.amount, 0);

    const lastMonthEarnings = earningsData
      .filter(earning => {
        const earningDate = new Date(earning.date);
        return earningDate.getMonth() === lastMonth && earningDate.getFullYear() === lastMonthYear;
      })
      .reduce((sum, earning) => sum + earning.amount, 0);

    const totalJobs = earningsData.length;
    const averagePerJob = totalJobs > 0 ? totalEarnings / totalJobs : 0;
    const pendingAmount = earningsData
      .filter(earning => earning.status === 'pending')
      .reduce((sum, earning) => sum + earning.amount, 0);

    setSummaryMetrics({
      totalEarnings,
      thisMonth: thisMonthEarnings,
      lastMonth: lastMonthEarnings,
      averagePerJob,
      totalJobs,
      pendingAmount,
    });
  };

  /**
   * Filter earnings by selected time period
   */
  const filterEarningsByPeriod = (earningsData: Earning[], period: TimePeriod) => {
    const now = new Date();
    let filteredData: Earning[] = [];

    switch (period) {
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        filteredData = earningsData.filter(earning => new Date(earning.date) >= weekAgo);
        break;
      case 'month':
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        filteredData = earningsData.filter(earning => new Date(earning.date) >= monthAgo);
        break;
      case 'quarter':
        const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
        filteredData = earningsData.filter(earning => new Date(earning.date) >= quarterAgo);
        break;
      case 'year':
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        filteredData = earningsData.filter(earning => new Date(earning.date) >= yearAgo);
        break;
      default:
        filteredData = earningsData;
    }

    setFilteredEarnings(filteredData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
  };

  /**
   * Handle period filter change
   */
  const handlePeriodChange = (period: TimePeriod) => {
    setSelectedPeriod(period);
    filterEarningsByPeriod(earnings, period);
  };

  useEffect(() => {
    fetchEarnings();
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main || '#007AFF'} />
        <Text style={styles.loadingText}>Loading earnings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, styles.summaryCardPrimary]}>
            <Text style={styles.summaryLabel}>Total Earnings</Text>
            <Text style={styles.summaryValue}>£{summaryMetrics.totalEarnings.toFixed(2)}</Text>
            <Text style={styles.summarySubtext}>All time</Text>
          </View>

          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>This Month</Text>
            <Text style={styles.summaryValue}>£{summaryMetrics.thisMonth.toFixed(2)}</Text>
            <View style={styles.summaryChange}>
              <Ionicons
                name={summaryMetrics.thisMonth >= summaryMetrics.lastMonth ? "trending-up" : "trending-down"}
                size={16}
                color={summaryMetrics.thisMonth >= summaryMetrics.lastMonth ? colors.success.main : colors.error.main}
              />
              <Text style={[
                styles.summaryChangeText,
                { color: summaryMetrics.thisMonth >= summaryMetrics.lastMonth ? colors.success.main : colors.error.main }
              ]}>
                {summaryMetrics.lastMonth > 0
                  ? `${(((summaryMetrics.thisMonth - summaryMetrics.lastMonth) / summaryMetrics.lastMonth) * 100).toFixed(1)}%`
                  : 'New'
                }
              </Text>
            </View>
          </View>

          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Average per Job</Text>
            <Text style={styles.summaryValue}>£{summaryMetrics.averagePerJob.toFixed(2)}</Text>
            <Text style={styles.summarySubtext}>{summaryMetrics.totalJobs} jobs completed</Text>
          </View>

          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Pending</Text>
            <Text style={styles.summaryValue}>£{summaryMetrics.pendingAmount.toFixed(2)}</Text>
            <Text style={styles.summarySubtext}>Awaiting payment</Text>
          </View>
        </View>

        {/* Time Period Filter */}
        <View style={styles.filterCard}>
          <Text style={styles.filterTitle}>Time Period</Text>
          <View style={styles.filterButtons}>
            {(['week', 'month', 'quarter', 'year'] as TimePeriod[]).map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.filterButton,
                  selectedPeriod === period && styles.activeFilterButton,
                ]}
                onPress={() => handlePeriodChange(period)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedPeriod === period && styles.activeFilterButtonText,
                  ]}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Earnings List */}
        <View style={styles.earningsCard}>
          <View style={styles.earningsHeader}>
            <Text style={styles.earningsTitle}>Recent Earnings</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {filteredEarnings.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="wallet-outline" size={48} color={colors.neutral?.main} />
              <Text style={styles.emptyStateText}>No earnings found for this period</Text>
            </View>
          ) : (
            filteredEarnings.slice(0, 10).map((earning) => (
              <View key={earning.id} style={styles.earningItem}>
                <View style={styles.earningIcon}>
                  <Ionicons
                    name={earning.category === 'bonus' ? 'star' : 'cash'}
                    size={24}
                    color={colors.primary.main}
                  />
                </View>
                <View style={styles.earningInfo}>
                  <Text style={styles.earningDescription}>{earning.description}</Text>
                  <Text style={styles.earningJobId}>Job #{earning.jobId.slice(0, 8)}</Text>
                  <Text style={styles.earningDate}>
                    {new Date(earning.date).toLocaleDateString('en-GB', {
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric',
                    })}
                  </Text>
                </View>
                <View style={styles.earningAmount}>
                  <Text style={styles.earningAmountText}>£{earning.amount.toFixed(2)}</Text>
                  {earning.bonus && earning.bonus > 0 && (
                    <Text style={styles.earningBonus}>+£{earning.bonus.toFixed(2)} bonus</Text>
                  )}
                  <View style={[
                    styles.earningStatus,
                    earning.status === 'paid' && styles.paidStatus,
                    earning.status === 'pending' && styles.pendingStatus,
                    earning.status === 'disputed' && styles.disputedStatus,
                  ]}>
                    <Text style={styles.earningStatusText}>
                      {earning.status.charAt(0).toUpperCase() + earning.status.slice(1)}
                    </Text>
                  </View>
                </View>
              </View>
            ))
          )}
        </View>

        {/* Performance Insights */}
        <View style={styles.insightsCard}>
          <Text style={styles.insightsTitle}>Performance Insights</Text>

          <View style={styles.insightItem}>
            <Ionicons name="trending-up" size={20} color={colors.success.main} />
            <Text style={styles.insightText}>
              Your earnings have <Text style={styles.insightHighlight}>increased by 15%</Text> compared to last month
            </Text>
          </View>

          <View style={styles.insightItem}>
            <Ionicons name="star" size={20} color={colors.warning.main} />
            <Text style={styles.insightText}>
              You earned <Text style={styles.insightHighlight}>£{(summaryMetrics.totalEarnings * 0.1).toFixed(2)} in bonuses</Text> this quarter
            </Text>
          </View>

          <View style={styles.insightItem}>
            <Ionicons name="time" size={20} color={colors.info?.main} />
            <Text style={styles.insightText}>
              Your average job completion time has <Text style={styles.insightHighlight}>improved by 20%</Text>
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default EarningsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary || '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary || '#f8f9fa',
  },
  loadingText: {
    ...typography.body.medium,
    color: colors.text.primary || '#6c757d',
    marginTop: spacing.md,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl * 2,
  },
  summaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  summaryCard: {
    flex: 1,
    minWidth: (screenWidth - spacing.lg * 2 - spacing.sm) / 2,
    backgroundColor: '#fff',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryCardPrimary: {
    backgroundColor: colors.primary.main || '#007AFF',
  },
  summaryLabel: {
    ...typography.body.small,
    color: colors.text.primary || '#6c757d',
    marginBottom: spacing.xs,
  },
  summaryValue: {
    ...typography.heading.h4,
    color: colors.text.primary || '#212529',
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  summarySubtext: {
    ...typography.body.small,
    color: colors.text.primary || '#6c757d',
  },
  summaryChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryChangeText: {
    ...typography.body.small,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  filterCard: {
    backgroundColor: '#fff',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filterTitle: {
    ...typography.heading.h5,
    color: colors.text.primary || '#212529',
    marginBottom: spacing.md,
    fontWeight: '600',
  },
  filterButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  filterButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: colors.neutral?.light || '#f8f9fa',
    alignItems: 'center',
  },
  activeFilterButton: {
    backgroundColor: colors.primary.main || '#007AFF',
  },
  filterButtonText: {
    ...typography.body.medium,
    color: colors.text.primary || '#6c757d',
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: colors.primary.contrast || '#fff',
  },
  earningsCard: {
    backgroundColor: '#fff',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  earningsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  earningsTitle: {
    ...typography.heading.h5,
    color: colors.text.primary || '#212529',
    fontWeight: '600',
  },
  viewAllText: {
    ...typography.body.medium,
    color: colors.primary.main || '#007AFF',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyStateText: {
    ...typography.body.medium,
    color: colors.text.primary || '#6c757d',
    marginTop: spacing.md,
  },
  earningItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.neutral?.light || '#e9ecef',
  },
  earningIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary.light || '#e3f2fd',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  earningInfo: {
    flex: 1,
  },
  earningDescription: {
    ...typography.body.medium,
    color: colors.text.primary || '#212529',
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  earningJobId: {
    ...typography.body.small,
    color: colors.text.primary || '#6c757d',
    marginBottom: spacing.xs,
  },
  earningDate: {
    ...typography.body.small,
    color: colors.text.primary || '#6c757d',
  },
  earningAmount: {
    alignItems: 'flex-end',
  },
  earningAmountText: {
    ...typography.body.large,
    color: colors.text.primary || '#212529',
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  earningBonus: {
    ...typography.body.small,
    color: colors.success.main || '#10b981',
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  earningStatus: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.neutral?.light || '#f8f9fa',
  },
  paidStatus: {
    backgroundColor: colors.success?.light || '#d1fae5',
  },
  pendingStatus: {
    backgroundColor: colors.warning?.light || '#fef3c7',
  },
  disputedStatus: {
    backgroundColor: colors.error.light || '#fee2e2',
  },
  earningStatusText: {
    ...typography.body.small,
    color: colors.text.primary || '#212529',
    fontWeight: '500',
  },
  insightsCard: {
    backgroundColor: '#fff',
    padding: spacing.lg,
    borderRadius: borderRadius.lg,
    marginBottom: spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightsTitle: {
    ...typography.heading.h5,
    color: colors.text.primary || '#212529',
    marginBottom: spacing.md,
    fontWeight: '600',
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  insightText: {
    ...typography.body.medium,
    color: colors.text.primary || '#6c757d',
    marginLeft: spacing.sm,
    flex: 1,
  },
  insightHighlight: {
    color: colors.primary.main || '#007AFF',
    fontWeight: '600',
  },
});
