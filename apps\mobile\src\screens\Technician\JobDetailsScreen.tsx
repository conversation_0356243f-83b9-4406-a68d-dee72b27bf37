// src/screens/Technician/JobDetailsScreen.tsx

import React, { useEffect, useState } from 'react';
import { View, Text, Image, Button, ActivityIndicator, Alert, ScrollView, StyleSheet } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { JobAPI } from '../../api/job.api';
import { Job } from '../../types/job';
import * as ImagePicker from 'expo-image-picker';
import { getPresignedUrl } from '../../api/uploadService';

export default function JobDetailsScreen() {
  const route = useRoute<RouteProp<{ params: { jobId: string } }, 'params'>>();
  const navigation = useNavigation();
  const { jobId } = route.params;

  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [proofImage, setProofImage] = useState<string | null>(null);

  useEffect(() => {
    fetchJob();
  }, []);

  const fetchJob = async () => {
    try {
      const job = await JobAPI.getJobDetails(jobId);
      setJob(job);
    } catch (err) {
      Alert.alert('Error', 'Failed to load job details');
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.7,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      const img = result.assets[0];
      setProofImage(img.uri);
    }
  };

  const handleStatusUpdate = async (status: 'IN_PROGRESS' | 'COMPLETED') => {
    try {
      setUploading(true);
      let photoUrl = undefined;

      if (status === 'COMPLETED') {
        if (!proofImage) {
          Alert.alert('Proof required', 'Please upload a photo before completing the job.');
          setUploading(false);
          return;
        }

        const presign = await getPresignedUrl({ fileType: 'image/jpeg' });
        await fetch(presign.url, {
          method: 'PUT',
          body: await fetch(proofImage).then(res => res.blob()),
          headers: { 'Content-Type': 'image/jpeg' },
        });

        photoUrl = presign.key;
      }

      await JobAPI.updateStatus(jobId, status, { photoUrl });
      Alert.alert('Success', `Job marked as ${status.replace('_', ' ')}`);
      navigation.goBack();
    } catch (err) {
      Alert.alert('Error', 'Failed to update job status');
    } finally {
      setUploading(false);
    }
  };

  if (loading) return <ActivityIndicator style={{ marginTop: 40 }} />;
  if (!job) return <Text style={{ margin: 20 }}>Job not found.</Text>;

  return (
    <ScrollView contentContainerStyle={{ padding: 20 }}>
      <Text style={styles.title}>{job.issue}</Text>
      <Text>Status: {job.status}</Text>
      <Text>Scheduled: {new Date(job.scheduledAt).toLocaleString()}</Text>

      {job.photoUrl && <Image source={{ uri: job.photoUrl }} style={styles.image} />}

      {job.status !== 'COMPLETED' && (
        <>
          <Button title="Upload Completion Proof" onPress={pickImage} />
          {proofImage && <Image source={{ uri: proofImage }} style={styles.image} />}

          {job.status === 'PENDING' && (
            <Button title="Start Job" onPress={() => handleStatusUpdate('IN_PROGRESS')} />
          )}

          {job.status === 'IN_PROGRESS' && (
            <Button
              title={uploading ? 'Submitting...' : 'Mark as Complete'}
              onPress={() => handleStatusUpdate('COMPLETED')}
              disabled={uploading}
            />
          )}
        </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  image: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
    marginVertical: 12,
    borderRadius: 8,
  },
});
