// apps/mobile/src/services/notification.service.ts

import * as Notifications from 'expo-notifications';
// Conditional import for Device
let Device: any = null;
try {
  Device = require('expo-device');
} catch (error) {
  console.warn('expo-device not available');
}
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { API_BASE_URL } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';
import { debugLogger } from '../utils/debugLogger';

// Configure notification behavior (only on native platforms)
if (Platform.OS !== 'web') {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
}

interface NotificationData {
  type?: string;
  jobId?: string;
  chatId?: string;
  [key: string]: any;
}

interface PushNotification {
  title: string;
  body: string;
  data?: NotificationData;
}

export class NotificationService {
  private static instance: NotificationService;
  private pushToken: string | null = null;
  private isRegistered = false;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize notification service and register for push notifications
   */
  async initialize(): Promise<void> {
    try {
      debugLogger.info('Initializing notification service...');

      // Skip initialization on web platform
      if (Platform.OS === 'web') {
        debugLogger.info('Skipping notification service on web platform');
        return;
      }

      // Request permissions
      const { status } = await this.requestPermissions();
      if (status !== 'granted') {
        debugLogger.warning('Notification permissions not granted');
        return;
      }

      // Get push token
      const token = await this.getPushToken();
      if (!token) {
        debugLogger.error('Failed to get push token');
        return;
      }

      // Register token with backend
      await this.registerPushToken(token);

      debugLogger.success('Notification service initialized successfully');
    } catch (error) {
      debugLogger.error('Failed to initialize notification service:', error);
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<{ status: string }> {
    try {
      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        if (finalStatus !== 'granted') {
          debugLogger.warning('Push notification permissions denied');
          return { status: finalStatus };
        }

        // Configure notification channel for Android
        if (Platform.OS === 'android') {
          await Notifications.setNotificationChannelAsync('default', {
            name: 'Default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
          });
        }

        return { status: finalStatus };
      } else {
        debugLogger.warning('Must use physical device for push notifications');
        return { status: 'denied' };
      }
    } catch (error) {
      debugLogger.error('Error requesting notification permissions:', error);
      return { status: 'denied' };
    }
  }

  /**
   * Get Expo push token
   */
  async getPushToken(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        debugLogger.warning('Must use physical device for push notifications');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.pushToken = token.data;
      debugLogger.info('Push token obtained:', token.data.substring(0, 20) + '...');
      return token.data;
    } catch (error) {
      debugLogger.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Register push token with backend
   */
  async registerPushToken(token: string): Promise<void> {
    try {
      const authToken = await getAuthToken();
      if (!authToken) {
        debugLogger.warning('No auth token available for push token registration');
        return;
      }

      const deviceInfo = {
        platform: Platform.OS,
        deviceId: Constants.deviceId,
        appVersion: Constants.expoConfig?.version,
      };

      const response = await fetch(`${API_BASE_URL}/api/notifications/register-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          pushToken: token,
          platform: Platform.OS,
          deviceInfo,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.isRegistered = true;
      debugLogger.success('Push token registered with backend');
    } catch (error) {
      debugLogger.error('Failed to register push token with backend:', error);
    }
  }

  /**
   * Unregister push token from backend
   */
  async unregisterPushToken(): Promise<void> {
    try {
      if (!this.pushToken) {
        return;
      }

      const authToken = await getAuthToken();
      if (!authToken) {
        return;
      }

      await fetch(`${API_BASE_URL}/api/notifications/unregister-token`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          pushToken: this.pushToken,
        }),
      });

      this.isRegistered = false;
      debugLogger.info('Push token unregistered from backend');
    } catch (error) {
      debugLogger.error('Failed to unregister push token:', error);
    }
  }

  /**
   * Handle notification received while app is in foreground
   */
  onNotificationReceived(callback: (notification: Notifications.Notification) => void): void {
    if (Platform.OS === 'web') {
      debugLogger.info('Notification listeners not supported on web');
      return;
    }
    Notifications.addNotificationReceivedListener(callback);
  }

  /**
   * Handle notification response (user tapped notification)
   */
  onNotificationResponse(callback: (response: Notifications.NotificationResponse) => void): void {
    if (Platform.OS === 'web') {
      debugLogger.info('Notification listeners not supported on web');
      return;
    }
    Notifications.addNotificationResponseReceivedListener(callback);
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(notification: PushNotification, delay = 0): Promise<string> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: 'default',
        },
        trigger: delay > 0 ? {
          type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
          seconds: delay
        } : null,
      });

      debugLogger.info('Local notification scheduled:', identifier);
      return identifier;
    } catch (error) {
      debugLogger.error('Failed to schedule local notification:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      debugLogger.info('Notification cancelled:', identifier);
    } catch (error) {
      debugLogger.error('Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      debugLogger.info('All notifications cancelled');
    } catch (error) {
      debugLogger.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<Notifications.NotificationPermissionsStatus> {
    return await Notifications.getPermissionsAsync();
  }

  /**
   * Set badge count
   */
  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      debugLogger.error('Failed to set badge count:', error);
    }
  }

  /**
   * Clear badge count
   */
  async clearBadgeCount(): Promise<void> {
    await this.setBadgeCount(0);
  }

  /**
   * Get current push token
   */
  getCurrentPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Check if push notifications are registered
   */
  isTokenRegistered(): boolean {
    return this.isRegistered;
  }

  /**
   * Remove notification listeners (web-safe)
   */
  removeListeners(subscription: any): void {
    if (Platform.OS === 'web') {
      debugLogger.info('Notification listener removal not needed on web');
      return;
    }

    if (subscription && typeof subscription.remove === 'function') {
      subscription.remove();
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();
