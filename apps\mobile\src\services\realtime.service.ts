// CTRON Home - Real-time Updates Service
// Comprehensive real-time job and notification management using Socket.IO

import { SocketService } from './socket.service';
// Conditional import for AsyncStorage
let AsyncStorage: any = null;
try {
  AsyncStorage = require('@react-native-async-storage/async-storage').default;
} catch (error) {
  console.warn('AsyncStorage not available');
  AsyncStorage = {
    getItem: () => Promise.resolve(null),
    setItem: () => Promise.resolve(),
    removeItem: () => Promise.resolve(),
  };
}
import * as Notifications from 'expo-notifications';

/**
 * Interface for real-time job update
 */
interface JobUpdate {
  jobId: string;
  status: 'assigned' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  technicianId: string;
  customerId: string;
  timestamp: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  message?: string;
  estimatedArrival?: string;
}

/**
 * Interface for real-time notification
 */
interface RealtimeNotification {
  id: string;
  type: 'job_assigned' | 'job_updated' | 'message_received' | 'emergency' | 'schedule_reminder';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

/**
 * Interface for location update
 */
interface LocationUpdate {
  technicianId: string;
  jobId?: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: string;
  speed?: number;
  heading?: number;
}

/**
 * Type for event listeners
 */
type EventListener<T = any> = (data: T) => void;

/**
 * Real-time service for managing Socket.IO events and notifications
 */
class RealtimeService {
  private listeners: Map<string, EventListener[]> = new Map();
  private isInitialized: boolean = false;
  private notificationQueue: RealtimeNotification[] = [];

  /**
   * Initialize the real-time service
   */
  public async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        return;
      }

      // Configure notifications
      await this.configureNotifications();

      // Set up Socket.IO event listeners
      this.setupSocketListeners();

      this.isInitialized = true;
      console.log('🔄 Real-time service initialized');
    } catch (error) {
      console.error('Failed to initialize real-time service:', error);
      throw error;
    }
  }

  /**
   * Configure notification settings
   */
  private async configureNotifications(): Promise<void> {
    try {
      await Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });

      // Request notification permissions
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
      }
    } catch (error) {
      console.error('Failed to configure notifications:', error);
    }
  }

  /**
   * Set up Socket.IO event listeners
   */
  private setupSocketListeners(): void {
    // Job updates - commented out until socket events are properly defined
    // SocketService.on('job:updated', this.handleJobUpdate.bind(this));
    // SocketService.on('job:assigned', this.handleJobAssigned.bind(this));
    // SocketService.on('job:cancelled', this.handleJobCancelled.bind(this));

    // Location updates - commented out until socket events are properly defined
    // SocketService.on('location:request', this.handleLocationRequest.bind(this));
    // SocketService.on('location:update', this.handleLocationUpdate.bind(this));

    // Messages and notifications - commented out until socket events are properly defined
    // SocketService.on('message:received', this.handleMessageReceived.bind(this));
    // SocketService.on('notification:push', this.handlePushNotification.bind(this));

    // Emergency alerts - commented out until socket events are properly defined
    // SocketService.on('emergency:alert', this.handleEmergencyAlert.bind(this));

    // Connection events
    SocketService.on('connect', this.handleConnect.bind(this));
    SocketService.on('disconnect', this.handleDisconnect.bind(this));
  }

  /**
   * Handle job update events
   */
  private async handleJobUpdate(data: JobUpdate): Promise<void> {
    try {
      console.log('📋 Job update received:', data);

      // Store update locally
      await this.storeJobUpdate(data);

      // Emit to listeners
      this.emit('jobUpdate', data);

      // Show notification if app is in background
      if (data.status === 'assigned' || data.status === 'cancelled') {
        await this.showNotification({
          id: `job_${data.jobId}_${Date.now()}`,
          type: 'job_updated',
          title: `Job ${data.status}`,
          message: `Job #${data.jobId} has been ${data.status}`,
          data: { jobId: data.jobId },
          timestamp: new Date().toISOString(),
          priority: 'normal',
        });
      }
    } catch (error) {
      console.error('Failed to handle job update:', error);
    }
  }

  /**
   * Handle job assigned events
   */
  private async handleJobAssigned(data: JobUpdate): Promise<void> {
    try {
      console.log('🎯 New job assigned:', data);

      // Store assignment locally
      await this.storeJobUpdate(data);

      // Emit to listeners
      this.emit('jobAssigned', data);

      // Show high-priority notification
      await this.showNotification({
        id: `job_assigned_${data.jobId}`,
        type: 'job_assigned',
        title: 'New Job Assigned!',
        message: `You have been assigned a new job. Tap to view details.`,
        data: { jobId: data.jobId },
        timestamp: new Date().toISOString(),
        priority: 'high',
      });
    } catch (error) {
      console.error('Failed to handle job assignment:', error);
    }
  }

  /**
   * Handle job cancelled events
   */
  private async handleJobCancelled(data: JobUpdate): Promise<void> {
    try {
      console.log('❌ Job cancelled:', data);

      // Store cancellation locally
      await this.storeJobUpdate(data);

      // Emit to listeners
      this.emit('jobCancelled', data);

      // Show notification
      await this.showNotification({
        id: `job_cancelled_${data.jobId}`,
        type: 'job_updated',
        title: 'Job Cancelled',
        message: `Job #${data.jobId} has been cancelled by the customer.`,
        data: { jobId: data.jobId },
        timestamp: new Date().toISOString(),
        priority: 'normal',
      });
    } catch (error) {
      console.error('Failed to handle job cancellation:', error);
    }
  }

  /**
   * Handle location request events
   */
  private handleLocationRequest(data: { jobId: string; requestId: string }): void {
    console.log('📍 Location request received:', data);
    this.emit('locationRequest', data);
  }

  /**
   * Handle location update events
   */
  private handleLocationUpdate(data: LocationUpdate): void {
    console.log('📍 Location update received:', data);
    this.emit('locationUpdate', data);
  }

  /**
   * Handle message received events
   */
  private async handleMessageReceived(data: any): Promise<void> {
    try {
      console.log('💬 Message received:', data);

      // Emit to listeners
      this.emit('messageReceived', data);

      // Show notification
      await this.showNotification({
        id: `message_${data.messageId}`,
        type: 'message_received',
        title: `Message from ${data.senderName}`,
        message: data.message,
        data: { messageId: data.messageId, chatId: data.chatId },
        timestamp: new Date().toISOString(),
        priority: 'normal',
      });
    } catch (error) {
      console.error('Failed to handle message received:', error);
    }
  }

  /**
   * Handle push notification events
   */
  private async handlePushNotification(data: RealtimeNotification): Promise<void> {
    try {
      console.log('🔔 Push notification received:', data);

      // Show notification
      await this.showNotification(data);

      // Emit to listeners
      this.emit('pushNotification', data);
    } catch (error) {
      console.error('Failed to handle push notification:', error);
    }
  }

  /**
   * Handle emergency alert events
   */
  private async handleEmergencyAlert(data: any): Promise<void> {
    try {
      console.log('🚨 Emergency alert received:', data);

      // Emit to listeners
      this.emit('emergencyAlert', data);

      // Show urgent notification
      await this.showNotification({
        id: `emergency_${Date.now()}`,
        type: 'emergency',
        title: 'Emergency Alert',
        message: data.message || 'Emergency situation detected',
        data: data,
        timestamp: new Date().toISOString(),
        priority: 'urgent',
      });
    } catch (error) {
      console.error('Failed to handle emergency alert:', error);
    }
  }

  /**
   * Handle connection events
   */
  private handleConnect(): void {
    console.log('🔌 Real-time service connected');
    this.emit('connected', {});
  }

  /**
   * Handle disconnection events
   */
  private handleDisconnect(): void {
    console.log('🔌 Real-time service disconnected');
    this.emit('disconnected', {});
  }

  /**
   * Show local notification
   */
  private async showNotification(notification: RealtimeNotification): Promise<void> {
    try {
      // Check if notifications are enabled
      const settings = await AsyncStorage.getItem('technician_settings');
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        if (!parsedSettings.notifications?.pushEnabled) {
          return;
        }
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.message,
          data: notification.data,
          priority: notification.priority === 'urgent' ? 'high' : 'default',
        },
        trigger: null, // Show immediately
      });
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }

  /**
   * Store job update locally
   */
  private async storeJobUpdate(update: JobUpdate): Promise<void> {
    try {
      const key = `job_update_${update.jobId}`;
      const existingUpdates = await AsyncStorage.getItem(key);
      const updates = existingUpdates ? JSON.parse(existingUpdates) : [];
      
      updates.push(update);
      
      // Keep only last 10 updates per job
      if (updates.length > 10) {
        updates.splice(0, updates.length - 10);
      }
      
      await AsyncStorage.setItem(key, JSON.stringify(updates));
    } catch (error) {
      console.error('Failed to store job update:', error);
    }
  }

  /**
   * Send location update
   */
  public sendLocationUpdate(location: LocationUpdate): void {
    try {
      // SocketService.emit('technician:locationUpdate', location); // Commented out until event is defined
      console.log('📍 Location update sent:', location);
    } catch (error) {
      console.error('Failed to send location update:', error);
    }
  }

  /**
   * Send job status update
   */
  public sendJobStatusUpdate(jobId: string, status: string, data?: any): void {
    try {
      const update = {
        jobId,
        status,
        timestamp: new Date().toISOString(),
        ...data,
      };
      
      // SocketService.emit('technician:jobStatusUpdate', update); // Commented out until event is defined
      console.log('📋 Job status update sent:', update);
    } catch (error) {
      console.error('Failed to send job status update:', error);
    }
  }

  /**
   * Add event listener
   */
  public on<T = any>(event: string, listener: EventListener<T>): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  /**
   * Remove event listener
   */
  public off(event: string, listener: EventListener): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit<T = any>(event: string, data: T): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    this.listeners.clear();
    this.isInitialized = false;
    console.log('🔄 Real-time service cleaned up');
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService();
export default realtimeService;
