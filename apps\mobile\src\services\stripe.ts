// src/services/stripe.ts

import { useStripe } from '@stripe/stripe-react-native';
import PaymentAPI from '../api/payment.api';

/**
 * Hook to initialize and present Stripe's native Payment Sheet
 */
export const useStripePayment = () => {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  /**
   * Step 1: Initialize Stripe's Payment Sheet
   * This pulls a client secret from your backend.
   */
  const initializePayment = async () => {
    try {
      const { clientSecret } = await PaymentAPI.createPaymentIntent(); // Your backend must return this
      const { error } = await initPaymentSheet({
        merchantDisplayName: 'CTRON Services',
        paymentIntentClientSecret: clientSecret,
      });
      return !error;
    } catch (error) {
      console.error('Stripe init error:', error);
      return false;
    }
  };

  /**
   * Step 2: Launch the Stripe sheet for payment
   */
  const openPaymentSheet = async () => {
    const { error } = await presentPaymentSheet();
    if (error) {
      throw new Error(error.message);
    }
  };

  return {
    initializePayment,
    openPaymentSheet,
  };
};
