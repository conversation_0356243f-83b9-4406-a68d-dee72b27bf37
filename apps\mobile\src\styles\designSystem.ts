// apps/mobile/src/styles/designSystem.ts
// Modern Design System for CTRON Home Mobile App

import { Platform } from 'react-native';

// 🎨 Color System - Based on iOS Human Interface Guidelines
export const colors = {
  // Primary Colors - Object structure for consistency
  primary: {
    50: '#F0F4F8',
    100: '#D9E2EC',
    200: '#BCCCDC',
    300: '#9FB3C8',
    400: '#829AB1',
    500: '#627D98',
    600: '#486581',
    700: '#334E68',
    800: '#243B53',
    900: '#1B365D',
    main: '#007AFF',      // iOS Blue - Trust and reliability
    dark: '#0056CC',      // Darker variant for pressed states
    light: '#4DA6FF',     // Lighter variant for backgrounds
    contrast: '#FFFFFF',
  },

  // Secondary Colors - Object structure
  secondary: {
    50: '#FFF5F0',
    100: '#FFE4D6',
    200: '#FFCAB0',
    300: '#FFAB7A',
    400: '#FF8C42',
    500: '#FF6B35',
    600: '#E55A2B',
    700: '#CC4A1F',
    800: '#B33A13',
    900: '#992A07',
    main: '#34C759',      // iOS Green - Success and completion
    dark: '#248A3D',
    light: '#68D389',
    contrast: '#FFFFFF',
  },

  // Semantic Colors
  success: '#34C759',      // Green for success states
  warning: '#FF9500',      // Orange for warnings
  destructive: '#FF3B30',  // Red for errors (renamed from error)
  info: '#007AFF',         // Blue for information
  
  // Neutral Colors
  black: '#000000',
  white: '#FFFFFF',
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
  
  // iOS System Colors
  systemBackground: '#FFFFFF',
  secondarySystemBackground: '#F2F2F7',
  tertiarySystemBackground: '#FFFFFF',
  systemGroupedBackground: '#F2F2F7',
  secondarySystemGroupedBackground: '#FFFFFF',
  
  // Text Colors
  label: '#000000',
  secondaryLabel: '#3C3C43',
  tertiaryLabel: '#3C3C43',
  quaternaryLabel: '#3C3C43',
  
  // Separator Colors
  separator: '#C6C6C8',
  opaqueSeparator: '#C6C6C8',
  
  // Chat-specific Colors
  chatBackground: '#F2F2F7',
  ownMessageBubble: '#007AFF',
  otherMessageBubble: '#E5E5EA',
  typingIndicator: '#8E8E93',
  
  // Status Colors
  online: '#34C759',
  away: '#FF9500',
  busy: '#FF3B30',
  offline: '#8E8E93',
};

// 📏 Spacing System - 8pt Grid
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// 🔤 Typography System
export const typography = {
  // Large Titles
  largeTitle: {
    fontSize: 34,
    lineHeight: 41,
    fontWeight: '700' as const,
    letterSpacing: 0.37,
  },
  
  // Titles
  title1: {
    fontSize: 28,
    lineHeight: 34,
    fontWeight: '700' as const,
    letterSpacing: 0.36,
  },
  title2: {
    fontSize: 22,
    lineHeight: 28,
    fontWeight: '700' as const,
    letterSpacing: 0.35,
  },
  title3: {
    fontSize: 20,
    lineHeight: 25,
    fontWeight: '600' as const,
    letterSpacing: 0.38,
  },
  
  // Headlines
  headline: {
    fontSize: 17,
    lineHeight: 22,
    fontWeight: '600' as const,
    letterSpacing: -0.41,
  },
  
  // Body Text
  body: {
    fontSize: 17,
    lineHeight: 22,
    fontWeight: '400' as const,
    letterSpacing: -0.41,
  },
  bodyEmphasized: {
    fontSize: 17,
    lineHeight: 22,
    fontWeight: '600' as const,
    letterSpacing: -0.41,
  },
  
  // Callouts
  callout: {
    fontSize: 16,
    lineHeight: 21,
    fontWeight: '400' as const,
    letterSpacing: -0.32,
  },
  calloutEmphasized: {
    fontSize: 16,
    lineHeight: 21,
    fontWeight: '600' as const,
    letterSpacing: -0.32,
  },
  
  // Subheadlines
  subheadline: {
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '400' as const,
    letterSpacing: -0.24,
  },
  subheadlineEmphasized: {
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '600' as const,
    letterSpacing: -0.24,
  },
  
  // Footnotes
  footnote: {
    fontSize: 13,
    lineHeight: 18,
    fontWeight: '400' as const,
    letterSpacing: -0.08,
  },
  footnoteEmphasized: {
    fontSize: 13,
    lineHeight: 18,
    fontWeight: '600' as const,
    letterSpacing: -0.08,
  },
  
  // Captions
  caption1: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },
  caption1Emphasized: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '600' as const,
    letterSpacing: 0,
  },
  caption2: {
    fontSize: 11,
    lineHeight: 13,
    fontWeight: '400' as const,
    letterSpacing: 0.07,
  },
  caption2Emphasized: {
    fontSize: 11,
    lineHeight: 13,
    fontWeight: '600' as const,
    letterSpacing: 0.07,
  },
};

// 🔘 Border Radius System
export const borderRadius = {
  none: 0,
  xs: 4,
  sm: 6,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,
  
  // Chat-specific
  messageBubble: 18,
  messageTail: 4,
  inputField: 20,
};

// 🌫️ Shadow System
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 16,
  },
};

// 📱 Component Sizes
export const sizes = {
  // Touch Targets
  touchTarget: 44,
  minTouchTarget: 44,
  
  // Buttons
  buttonHeight: {
    sm: 32,
    md: 44,
    lg: 56,
  },
  
  // Input Fields
  inputHeight: {
    sm: 36,
    md: 44,
    lg: 52,
  },
  
  // Icons
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  
  // Avatar
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64,
    xxl: 96,
  },
};

// 🎭 Animation System
export const animations = {
  // Durations (in milliseconds)
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },
  
  // Easing Functions
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
  
  // Spring Configurations
  spring: {
    gentle: {
      tension: 120,
      friction: 14,
    },
    wobbly: {
      tension: 180,
      friction: 12,
    },
    stiff: {
      tension: 210,
      friction: 20,
    },
  },
};

// 📐 Layout System
export const layout = {
  // Container Widths
  container: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
  
  // Screen Padding
  screenPadding: {
    horizontal: spacing.md,
    vertical: spacing.lg,
  },
  
  // Header Heights
  headerHeight: Platform.select({
    ios: 44,
    android: 56,
    default: 44,
  }),
  
  // Tab Bar Height
  tabBarHeight: Platform.select({
    ios: 83, // 49 + 34 (safe area)
    android: 56,
    default: 56,
  }),
};

// 🎯 Accessibility
export const accessibility = {
  // Minimum touch target size
  minTouchTarget: 44,
  
  // Text scaling
  textScale: {
    min: 0.85,
    max: 1.3,
  },
  
  // Color contrast ratios (WCAG AA)
  contrastRatio: {
    normal: 4.5,
    large: 3.0,
  },
};

// Export the complete design system
export const designSystem = {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
  sizes,
  animations,
  layout,
  accessibility,
};

export default designSystem;
