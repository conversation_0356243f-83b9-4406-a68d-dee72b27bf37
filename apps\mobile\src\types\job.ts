// src/types/job.ts

// src/types/job.ts

export type JobStatus =
  | 'PENDING'
  | 'ACCEPTED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';

export interface Job {
  id: string;
  userId: string;
  technicianId?: string;
  issue: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  photoUrl?: string;
  status: JobStatus;
  scheduledAt: string;
  createdAt: string;
  updatedAt: string;
  latitude?: number;
  longitude?: number;
  address?: string; // Add address property
  technician?: {
    id: string;
    user: {
      fullName: string;
      phone?: string;
    };
    specialization: string;
    rating?: number;
  };
}
