// src/types/navigation.ts

export type RootStackParamList = {
  Auth: undefined;
  TechnicianStack: undefined;
  HomeownerStack: undefined;
  AdminStack: undefined;
  AccessDenied: undefined;
};

// Admin Stack Navigation Types
export type AdminStackParamList = {
  AdminDashboard: undefined;
  AdminJobs: undefined;
  AdminTechnicians: undefined;
  AdminSettings: undefined;
  AdminJobDetails: { jobId: string };
  ChatList: undefined;
  Chat: { chatId: string; jobTitle: string };
};

// Homeowner Stack Navigation Types
export type HomeownerStackParamList = {
  Home: undefined;
  BookJob: undefined;
  CreateJob: undefined;
  MyJobs: undefined;
  JobDetails: { jobId: string };
  EditJob: { jobId: string };
  Payment: { jobId: string; amount: number };
  TrackTechnician: { jobId: string };
  ChatList: undefined;
  Chat: { chatId: string; jobTitle: string };
};

// Technician Stack Navigation Types
export type TechnicianStackParamList = {
  TechnicianHome: undefined;
  TechnicianDashboard: undefined;
  AvailableJobs: undefined;
  AssignedJobs: undefined;
  ActiveJob: { jobId: string };
  AcceptJob: { jobId: string };
  JobDetails: { jobId: string };
  Earnings: undefined;
  Profile: undefined;
  Settings: undefined;
  ChatList: undefined;
  Chat: { chatId: string; jobTitle: string };
};

// Technician Drawer Navigation Types
export type TechnicianDrawerParamList = {
  Dashboard: undefined;
  Profile: undefined;
  Settings: undefined;
};

// Auth Stack Navigation Types
export type AuthStackParamList = {
  Login: undefined;
  Signup: undefined;
};

// Chat Navigation Types
export type ChatStackParamList = {
  ChatList: undefined;
  Chat: { chatId: string; jobTitle: string };
};
