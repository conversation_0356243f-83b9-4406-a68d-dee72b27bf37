// src/utils/navigationOptions.ts
import React from 'react';
import { Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';

export const useDashboardOptions = () => {
  const { user } = useAuth();
  const firstName = user?.fullName?.split(' ')[0] || 'Technician';

  return {
    headerTitle: () => (
      <Text style={{ fontSize: 18, fontWeight: 'bold' }}>
        Welcome, {firstName}
      </Text>
    ),
    drawerIcon: ({ color, size }: { color: string; size: number }) => (
      <Ionicons name="home-outline" size={size} color={color} />
    ),
  };
};
