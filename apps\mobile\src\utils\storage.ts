// src/utils/storage.ts
import * as SecureStore from 'expo-secure-store';

const isWeb = typeof window !== 'undefined';

export const saveItem = async (key: string, value: any) => {
  const serialized = typeof value === 'string' ? value : JSON.stringify(value);
  if (isWeb) {
    localStorage.setItem(key, serialized);
  } else {
    await SecureStore.setItemAsync(key, serialized);
  }
};

export const getItem = async <T = string>(key: string): Promise<T | null> => {
  const raw = isWeb ? localStorage.getItem(key) : await SecureStore.getItemAsync(key);
  try {
    return raw ? (JSON.parse(raw) as T) : null;
  } catch {
    return raw as unknown as T; // fallback if value is primitive
  }
};

export const deleteItem = async (key: string) => {
  if (isWeb) {
    localStorage.removeItem(key);
  } else {
    await SecureStore.deleteItemAsync(key);
  }
};
