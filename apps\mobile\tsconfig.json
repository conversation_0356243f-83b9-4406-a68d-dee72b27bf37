{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "jsx": "react-native", "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "resolveJsonModule": true, "moduleResolution": "node", "isolatedModules": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@screens/*": ["screens/*"], "@api/*": ["api/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"], "@services/*": ["services/*"], "@navigation/*": ["navigation/*"], "@context/*": ["context/*"], "@hooks/*": ["hooks/*"]}}, "include": ["src", "App.tsx", "src/types/env.d.ts", "src/types/global.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "coverage", "android", "ios", "web-build"]}