<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTRON Home</title>

    <!-- CRITICAL: Consolidated bridge setup - MUST load before React Native bundle -->
    <script>
        console.log('🚨 CONSOLIDATED SCRIPT: Bridge setup starting...');

        // Detect web platform
        if (typeof window !== 'undefined' && typeof document !== 'undefined') {
            console.log('🚨 CONSOLIDATED SCRIPT: Web platform detected');

            // Set up __fbBatchedBridgeConfig IMMEDIATELY
            window.__fbBatchedBridgeConfig = {
                remoteModuleConfig: [],
                localModulesConfig: []
            };
            console.log('🚨 CONSOLIDATED SCRIPT: __fbBatchedBridgeConfig created');

            // Set up __fbBatchedBridge IMMEDIATELY
            window.__fbBatchedBridge = {
                callFunctionReturnFlushedQueue: () => null,
                callFunctionReturnResultAndFlushedQueue: () => null,
                flushedQueue: () => null,
                invokeCallbackAndReturnFlushedQueue: () => null,
                setGlobalHandler: () => null,
                registerCallableModule: () => null,
                registerLazyCallableModule: () => null,
            };
            console.log('🚨 CONSOLIDATED SCRIPT: __fbBatchedBridge created');

            // Set up global object
            window.global = window.global || window;
            window.global.__fbBatchedBridgeConfig = window.__fbBatchedBridgeConfig;
            window.global.__fbBatchedBridge = window.__fbBatchedBridge;
            console.log('🚨 CONSOLIDATED SCRIPT: global objects created');

            // Set up NativeModules proxy
            window.NativeModules = new Proxy({}, {
                get: (target, prop) => {
                    return new Proxy({}, {
                        get: (moduleTarget, moduleProp) => {
                            if (typeof moduleProp === 'string') {
                                return (...args) => {
                                    console.log(`🚨 CONSOLIDATED SCRIPT: NativeModules.${prop}.${moduleProp} called`);
                                    return Promise.resolve(null);
                                };
                            }
                            return null;
                        }
                    });
                }
            });
            window.global.NativeModules = window.NativeModules;
            console.log('🚨 CONSOLIDATED SCRIPT: NativeModules proxy created');

            // Set up console error reporter
            window.installConsoleErrorReporter = () => {
                console.log('🚨 CONSOLIDATED SCRIPT: installConsoleErrorReporter called');
                return { uninstall: () => {} };
            };
            window.global.installConsoleErrorReporter = window.installConsoleErrorReporter;
            console.log('🚨 CONSOLIDATED SCRIPT: installConsoleErrorReporter created');

            // Override console.error to suppress bridge errors
            const originalConsoleError = console.error;
            console.error = (...args) => {
                const message = args.join(' ');
                
                if (message.includes('__fbBatchedBridgeConfig') ||
                    message.includes('cannot invoke native modules') ||
                    message.includes('installConsoleErrorReporter')) {
                    console.warn('🚨 CONSOLIDATED SCRIPT: Bridge error suppressed:', message);
                    return;
                }
                
                originalConsoleError.apply(console, args);
            };

            console.log('🚨 CONSOLIDATED SCRIPT: Bridge setup complete');
            
            // Verify setup
            console.log('🚨 CONSOLIDATED SCRIPT: Verification:', {
                '__fbBatchedBridgeConfig': !!window.__fbBatchedBridgeConfig,
                '__fbBatchedBridge': !!window.__fbBatchedBridge,
                'NativeModules': !!window.NativeModules,
                'installConsoleErrorReporter': !!window.installConsoleErrorReporter
            });
        }
    </script>

    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body, #root {
            height: 100%;
            width: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #ffffff;
            color: #000000;
        }

        /* Loading screen styles */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .loading-subtext {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Hide loading screen when app loads */
        .app-loaded .loading-container {
            display: none;
        }

        /* React Native Web root container */
        #root {
            display: flex;
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- React Native Web app will mount here -->
    <script>
        console.log('🚀 CTRON Home: HTML template loaded');
        console.log('🌐 Platform: Web Browser');
        console.log('🔧 Bridge setup: Pre-loaded');

        // Fallback: hide loading screen after 10 seconds regardless
        setTimeout(() => {
            console.log('⚠️ Loading screen timeout - hiding anyway');
            document.body.classList.add('app-loaded');
            // Ensure the root div is visible if it was hidden by the loading screen
            const root = document.getElementById('root');
            if (root) {
                root.style.display = 'flex';
            }
        }, 10000);
    </script>
    <script src="./index.bundle?platform=web"></script>
</body>
</html>
