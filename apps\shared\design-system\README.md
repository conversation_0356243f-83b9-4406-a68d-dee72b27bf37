# CTRON Home Design System

## Overview

The CTRON Home Design System is a comprehensive UI/UX framework designed specifically for professional home services platforms. It provides a consistent, accessible, and trust-building interface across mobile (React Native) and web (React) applications.

## Design Philosophy

### Trust & Professionalism
- **Trust-first approach**: Every design decision prioritizes building user confidence
- **Professional aesthetics**: Clean, modern interface that conveys expertise
- **Safety-conscious**: Color choices and interactions emphasize safety and reliability

### Accessibility & Inclusion
- **WCAG 2.1 AA compliant**: Meets accessibility standards for all users
- **Mobile-first design**: Optimized for touch interactions and mobile workflows
- **Multi-user consideration**: Designed for homeowners, technicians, and administrators

### Industry-Specific Design
- **Service marketplace patterns**: Follows best practices from TaskRabbit, Thumbtack, Uber Services
- **Home services context**: Colors and imagery appropriate for tools, safety, expertise
- **Professional workflows**: Streamlined interfaces for job management and completion

## Color System

### Primary Palette - Trust Blue Family
```css
/* Deep Navy - Primary Brand Color */
--color-primary-900: #1B365D;
/* Usage: Headers, primary CTAs, navigation */
/* Conveys: Trust, professionalism, stability */

/* Professional Blue - Secondary Brand */
--color-primary-800: #243B53;
--color-primary-700: #334E68;
--color-primary-600: #486581;
/* Usage: Secondary buttons, links, accents */
/* Conveys: Reliability, expertise */

/* Confident Blue - Interactive Elements */
--color-primary-500: #627D98;
--color-primary-400: #829AB1;
/* Usage: Hover states, active elements */
/* Conveys: Approachability, competence */
```

### Secondary Palette - Safety Orange Family
```css
/* Safety Orange - Urgent Actions */
--color-secondary-500: #FF6B35;
/* Usage: Emergency services, urgent actions */
/* Conveys: Urgency, attention, safety */

/* Warm Orange - Notifications */
--color-secondary-400: #FF8C42;
/* Usage: Notifications, warnings */
/* Conveys: Caution, warmth */
```

### Success Palette - Professional Green Family
```css
/* Professional Green - Success States */
--color-success-600: #16A34A;
/* Usage: Completed jobs, success states */
/* Conveys: Completion, growth, positive outcomes */

/* Fresh Green - Available Status */
--color-success-500: #22C55E;
/* Usage: Available technicians, positive feedback */
/* Conveys: Freshness, availability */
```

### Status Colors
```css
--color-status-pending: #F59E0B;     /* Amber - Waiting */
--color-status-assigned: #3B82F6;    /* Blue - In Progress */
--color-status-active: #EF4444;      /* Red - Active Work */
--color-status-completed: #10B981;   /* Green - Success */
--color-status-cancelled: #6B7280;   /* Gray - Cancelled */
--color-status-overdue: #DC2626;     /* Dark Red - Urgent */
```

## Typography

### Font Hierarchy
- **Primary Font**: Inter - Modern, professional, highly readable
- **Secondary Font**: Source Sans Pro - Friendly, approachable for body text
- **Monospace Font**: JetBrains Mono - Technical information, IDs, codes

### Scale & Usage
```css
/* Headings */
h1: 2.5rem (40px) - Page titles
h2: 2rem (32px) - Section headers
h3: 1.5rem (24px) - Card titles
h4: 1.25rem (20px) - Subsections
h5: 1.125rem (18px) - Small headers

/* Body Text */
Large: 1.125rem (18px) - Important content
Base: 1rem (16px) - Default body text
Small: 0.875rem (14px) - Secondary information
Extra Small: 0.75rem (12px) - Captions, labels
```

## Spacing System

### 8px Grid System
All spacing follows an 8px base unit for visual consistency:

```css
--space-1: 4px    /* Tight spacing */
--space-2: 8px    /* Base unit */
--space-3: 12px   /* Small gaps */
--space-4: 16px   /* Standard spacing */
--space-6: 24px   /* Medium spacing */
--space-8: 32px   /* Large spacing */
--space-12: 48px  /* Section spacing */
--space-16: 64px  /* Page spacing */
```

## Component Library

### Buttons
- **Primary**: Main actions, job booking, confirmations
- **Secondary**: Alternative actions, navigation
- **Emergency**: Urgent services, emergency calls
- **Ghost**: Subtle actions, secondary navigation
- **Outline**: Form actions, filters

### Cards
- **Job Cards**: Display job information with status indicators
- **Technician Cards**: Show technician profiles with verification badges
- **Content Cards**: General content display with flexible layouts

### Status Indicators
- **Status Badges**: Visual job and technician status
- **Verification Badges**: Trust indicators for verified technicians
- **Priority Badges**: Job priority and urgency levels

### Forms
- **Input Fields**: Accessible form inputs with clear validation
- **Select Dropdowns**: Styled select elements with custom indicators
- **Form Groups**: Consistent form layout and spacing

## Theme Variations

### Light Mode (Default)
- Clean, bright interface for daytime use
- High contrast for outdoor visibility
- Professional appearance for business contexts

### Dark Mode
- Reduced eye strain for evening use
- Battery-friendly for mobile devices
- Modern aesthetic for tech-savvy users

### Professional Blue Theme
- Enhanced trust and authority
- Ideal for administrator interfaces
- Corporate-friendly appearance

## Accessibility Guidelines

### Color Contrast
- Minimum 4.5:1 contrast ratio for normal text
- Minimum 3:1 contrast ratio for large text
- Color-blind friendly palette with sufficient differentiation

### Touch Targets
- Minimum 44px × 44px touch targets
- Adequate spacing between interactive elements
- Clear visual feedback for interactions

### Screen Reader Support
- Semantic HTML structure
- Proper ARIA labels and descriptions
- Logical tab order and keyboard navigation

## Animation Guidelines

### Micro-Interactions
- **Subtle hover effects**: 2px upward translation
- **Button feedback**: Scale down on press (0.98)
- **Loading states**: Smooth spinner animations

### Page Transitions
- **Fade in**: 300ms ease-out for new content
- **Slide transitions**: 300ms ease-out for navigation
- **Scale animations**: 200ms ease-out for modals

### Performance Considerations
- Hardware-accelerated transforms
- Reduced motion support for accessibility
- Optimized animations for 60fps performance

## Implementation Guidelines

### Mobile (React Native)
- Use StyleSheet.create for performance
- Implement theme context for dynamic switching
- Follow platform-specific interaction patterns

### Web (React)
- Use CSS custom properties for theming
- Implement responsive design with mobile-first approach
- Ensure keyboard navigation support

### Shared Components
- Maintain API consistency across platforms
- Use TypeScript for type safety
- Document component props and usage examples

## Usage Examples

### Button Implementation
```tsx
// Primary action button
<Button variant="primary" size="lg" fullWidth>
  Book Service
</Button>

// Emergency service button
<Button variant="emergency" icon={<PhoneIcon />}>
  Emergency Call
</Button>

// Loading state
<Button variant="primary" loading>
  Processing...
</Button>
```

### Card Implementation
```tsx
// Job card with status
<JobCard status="active" priority="high">
  <CardHeader 
    title="Plumbing Repair" 
    subtitle="Kitchen sink leak"
    action={<StatusBadge status="active" />}
  />
  <CardContent>
    Urgent repair needed for kitchen sink leak...
  </CardContent>
  <CardFooter>
    <Button variant="primary" size="sm">
      View Details
    </Button>
  </CardFooter>
</JobCard>
```

## Best Practices

### Do's
- ✅ Use consistent spacing from the 8px grid
- ✅ Implement proper loading states for all actions
- ✅ Provide clear visual feedback for user interactions
- ✅ Use semantic colors that match user expectations
- ✅ Test with screen readers and keyboard navigation

### Don'ts
- ❌ Use arbitrary spacing values outside the system
- ❌ Implement animations that could trigger motion sensitivity
- ❌ Use color alone to convey important information
- ❌ Create touch targets smaller than 44px
- ❌ Override accessibility features without alternatives

## Resources

### Design Files
- Figma Design System Library
- Component Specifications
- Color Palette Swatches
- Typography Specimens

### Development Resources
- Component Storybook
- TypeScript Definitions
- Usage Documentation
- Testing Guidelines

---

*This design system is continuously evolving based on user feedback and industry best practices. For questions or contributions, please refer to the project documentation.*
