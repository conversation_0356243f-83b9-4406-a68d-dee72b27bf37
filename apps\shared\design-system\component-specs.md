# CTRON Home Component Specifications

## Button Components

### Primary Button
**Purpose**: Main call-to-action buttons for critical user actions

**Visual Specifications**:
- Background: Gradient from `#1B365D` to `#2E5984`
- Text Color: White (`#FFFFFF`)
- Border Radius: 12px
- Padding: 16px horizontal, 12px vertical
- Minimum Height: 48px (touch-friendly)
- Font Weight: Semibold (600)
- Shadow: `0 2px 8px rgba(27, 54, 93, 0.2)`

**Interaction States**:
- Hover: Translate Y -1px, increase shadow
- Active: Translate Y 0px, reduce shadow
- Disabled: 50% opacity, no pointer events
- Loading: Show spinner, "Loading..." text

**Usage Examples**:
- "Book Service" buttons
- "Confirm Booking" actions
- "Save Changes" in forms

### Emergency Button
**Purpose**: Urgent actions requiring immediate attention

**Visual Specifications**:
- Background: Gradient from `#FF6B35` to `#FF8C42`
- Text Color: White (`#FFFFFF`)
- Border Radius: 12px
- Font Weight: Bold (700)
- Animation: Subtle pulse effect (2s infinite)
- Shadow: `0 4px 12px rgba(255, 107, 53, 0.3)`

**Interaction States**:
- Hover: Increase pulse intensity
- Active: Stop pulse, scale down slightly
- Focus: High contrast outline for accessibility

**Usage Examples**:
- "Emergency Service" calls
- "Urgent Repair" requests
- Critical system alerts

### Secondary Button
**Purpose**: Alternative actions, less prominent than primary

**Visual Specifications**:
- Background: Transparent
- Border: 2px solid `#1B365D`
- Text Color: `#1B365D`
- Border Radius: 12px
- Padding: 14px horizontal, 10px vertical

**Interaction States**:
- Hover: Fill with `#1B365D`, text becomes white
- Active: Darker fill color
- Focus: Outline for accessibility

**Usage Examples**:
- "Cancel" actions
- "View Details" links
- Secondary navigation

## Card Components

### Job Card
**Purpose**: Display job information with status and priority indicators

**Visual Specifications**:
- Background: White (`#FFFFFF`)
- Border Radius: 16px
- Padding: 24px
- Border: 1px solid `#E5E7EB`
- Shadow: `0 2px 12px rgba(0, 0, 0, 0.05)`
- Margin Bottom: 16px

**Status Indicators**:
- Left border color indicates status:
  - Pending: `#F59E0B` (amber)
  - Assigned: `#3B82F6` (blue)
  - Active: `#EF4444` (red)
  - Completed: `#10B981` (green)
  - Cancelled: `#6B7280` (gray)

**Priority Indicators**:
- Emergency: 2px border in `#FF6B35`, enhanced shadow
- High: Orange accent elements
- Medium: Yellow accent elements
- Low: Gray accent elements

**Content Structure**:
```
┌─────────────────────────────────┐
│ [Status Badge]    [Priority]    │
│ Job Title                       │
│ Brief Description               │
│ ─────────────────────────────── │
│ [Technician] [Time] [Location]  │
│ [Action Button]                 │
└─────────────────────────────────┘
```

### Technician Card
**Purpose**: Display technician profiles with verification and availability

**Visual Specifications**:
- Background: White (`#FFFFFF`)
- Border Radius: 16px
- Padding: 24px
- Top border: 4px solid `#10B981` (if verified)
- Background tint: `#F0FDF4` (if available)

**Verification Badge**:
- Background: Gradient green (`#10B981` to `#22C55E`)
- Text: "Verified" with checkmark icon
- Border Radius: 12px
- Padding: 4px 8px

**Content Structure**:
```
┌─────────────────────────────────┐
│ [Avatar] [Verification Badge]   │
│ Technician Name                 │
│ Specialization                  │
│ [Rating Stars] (4.8)            │
│ ─────────────────────────────── │
│ [Skills] [Availability]         │
│ [Contact Button]                │
└─────────────────────────────────┘
```

## Status Badge Components

### Job Status Badges
**Purpose**: Visual indicators for job progression states

**Specifications by Status**:

**Pending**:
- Background: `rgba(245, 158, 11, 0.1)`
- Text Color: `#F59E0B`
- Text: "PENDING"

**Assigned**:
- Background: `rgba(59, 130, 246, 0.1)`
- Text Color: `#3B82F6`
- Text: "ASSIGNED"

**Active**:
- Background: `rgba(239, 68, 68, 0.1)`
- Text Color: `#EF4444`
- Text: "IN PROGRESS"

**Completed**:
- Background: `rgba(16, 185, 129, 0.1)`
- Text Color: `#10B981`
- Text: "COMPLETED"

**Common Properties**:
- Border Radius: 20px (pill shape)
- Padding: 4px 12px
- Font Size: 12px
- Font Weight: Semibold (600)
- Text Transform: Uppercase
- Letter Spacing: 0.5px

### Verification Badges
**Purpose**: Trust indicators for verified technicians

**Visual Specifications**:
- Background: `#10B981`
- Text Color: White
- Icon: Checkmark (✓)
- Border Radius: 12px
- Padding: 4px 8px
- Font Size: 12px
- Font Weight: Semibold

## Form Components

### Input Fields
**Purpose**: Text input for forms with clear validation states

**Default State**:
- Border: 2px solid `#D1D5DB`
- Border Radius: 12px
- Padding: 16px
- Background: White
- Font Size: 16px
- Minimum Height: 48px

**Focus State**:
- Border Color: `#1B365D`
- Box Shadow: `0 0 0 3px rgba(27, 54, 93, 0.1)`
- Outline: None

**Error State**:
- Border Color: `#EF4444`
- Box Shadow: `0 0 0 3px rgba(239, 68, 68, 0.1)`
- Error message below in red

**Label Specifications**:
- Font Size: 14px
- Font Weight: Semibold (600)
- Color: `#111827`
- Margin Bottom: 8px

### Select Dropdowns
**Purpose**: Selection inputs with custom styling

**Visual Specifications**:
- Same base styling as input fields
- Custom dropdown arrow: `▼` in `#6B7280`
- Arrow position: Right side, 16px from edge
- Remove default browser styling

**Dropdown Menu**:
- Background: White
- Border: 1px solid `#E5E7EB`
- Border Radius: 8px
- Shadow: `0 4px 6px rgba(0, 0, 0, 0.1)`
- Max Height: 200px with scroll

## Navigation Components

### Bottom Navigation (Mobile)
**Purpose**: Primary navigation for mobile app

**Visual Specifications**:
- Background: White
- Border Top: 1px solid `#E5E7EB`
- Padding: 8px 16px
- Height: 80px
- Shadow: `0 -2px 12px rgba(0, 0, 0, 0.05)`

**Navigation Items**:
- Minimum Touch Target: 44px × 44px
- Icon Size: 24px × 24px
- Font Size: 12px
- Active Color: `#1B365D`
- Inactive Color: `#6B7280`

**Active State Indicator**:
- Background: `rgba(27, 54, 93, 0.1)`
- Border Radius: 12px
- Padding: 8px

### Header Navigation (Web)
**Purpose**: Top navigation for web admin panel

**Visual Specifications**:
- Background: White
- Border Bottom: 1px solid `#E5E7EB`
- Padding: 16px
- Height: 64px
- Sticky positioning

**Content Layout**:
- Logo/Title: Left aligned
- Navigation Links: Center
- User Actions: Right aligned
- Responsive collapse on mobile

## Loading States

### Spinner Component
**Purpose**: Indicate loading or processing states

**Visual Specifications**:
- Size: 40px × 40px
- Border: 4px solid `#E5E7EB`
- Border Top: 4px solid `#1B365D`
- Border Radius: 50%
- Animation: 1s linear infinite rotation

### Skeleton Loading
**Purpose**: Content placeholders during data loading

**Visual Specifications**:
- Background: Linear gradient shimmer effect
- Colors: `#F3F4F6` to `#E5E7EB` to `#F3F4F6`
- Animation: 1.5s infinite shimmer
- Border Radius: 8px

**Common Skeleton Shapes**:
- Text Line: Height 16px, various widths
- Title: Height 24px, 60% width
- Avatar: 40px × 40px circle
- Button: Height 44px, 120px width

## Accessibility Specifications

### Color Contrast Requirements
- Normal text: Minimum 4.5:1 contrast ratio
- Large text (18px+): Minimum 3:1 contrast ratio
- Interactive elements: Minimum 3:1 contrast ratio

### Touch Target Requirements
- Minimum size: 44px × 44px
- Adequate spacing: 8px minimum between targets
- Clear visual feedback for all interactions

### Screen Reader Support
- Semantic HTML structure
- Proper ARIA labels and descriptions
- Logical tab order
- Skip navigation links
- Status announcements for dynamic content

### Keyboard Navigation
- All interactive elements focusable
- Clear focus indicators
- Logical tab order
- Escape key support for modals
- Enter/Space activation for buttons

## Animation Specifications

### Micro-Interactions
- Button hover: `transform: translateY(-1px)` in 200ms
- Card hover: `transform: translateY(-2px)` in 200ms
- Button press: `transform: scale(0.98)` in 100ms

### Page Transitions
- Fade in: Opacity 0 to 1, translateY 20px to 0, 300ms ease-out
- Slide in: translateX 100% to 0, 300ms ease-out
- Modal: Scale 0.9 to 1, opacity 0 to 1, 200ms ease-out

### Loading Animations
- Spinner: 360° rotation, 1s linear infinite
- Pulse: Opacity 1 to 0.5 to 1, 2s ease-in-out infinite
- Shimmer: Background position -200% to 200%, 1.5s ease-in-out infinite

---

*These specifications ensure consistent implementation across all platforms and maintain the professional, trustworthy appearance essential for the CTRON Home brand.*
