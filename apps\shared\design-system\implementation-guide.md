# CTRON Home Design System Implementation Guide

## Getting Started

This guide provides step-by-step instructions for implementing the CTRON Home Design System across mobile (React Native) and web (React) applications.

## Installation & Setup

### Mobile App (React Native)

1. **Install Required Dependencies**
```bash
npm install react-native-vector-icons
npm install @react-native-async-storage/async-storage
npm install react-native-safe-area-context
```

2. **Setup Theme Provider**
```tsx
// App.tsx
import React from 'react';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { lightTheme } from './src/styles/theme';

export default function App() {
  return (
    <ThemeProvider theme={lightTheme}>
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

3. **Create Theme Context**
```tsx
// src/contexts/ThemeContext.tsx
import React, { createContext, useContext, useState } from 'react';
import { lightTheme, darkTheme, professionalTheme, Theme } from '../styles/theme';

type ThemeContextType = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode; theme: Theme }> = ({
  children,
  theme: initialTheme,
}) => {
  const [theme, setTheme] = useState(initialTheme);

  const toggleTheme = () => {
    setTheme(current => current === lightTheme ? darkTheme : lightTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

### Web App (React)

1. **Install Required Dependencies**
```bash
npm install tailwindcss @tailwindcss/forms
npm install class-variance-authority clsx tailwind-merge
```

2. **Configure Tailwind CSS**
```js
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#F0F4F8',
          100: '#D9E2EC',
          200: '#BCCCDC',
          300: '#9FB3C8',
          400: '#829AB1',
          500: '#627D98',
          600: '#486581',
          700: '#334E68',
          800: '#243B53',
          900: '#1B365D',
        },
        secondary: {
          50: '#FFF5F0',
          100: '#FFE4D6',
          200: '#FFCAB0',
          300: '#FFAB7A',
          400: '#FF8C42',
          500: '#FF6B35',
          600: '#E55A2B',
          700: '#CC4A1F',
          800: '#B33A13',
          900: '#992A07',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
};
```

3. **Setup CSS Custom Properties**
```css
/* Import the globals.css file created earlier */
@import './styles/globals.css';
```

## Component Usage Examples

### Buttons

#### Mobile (React Native)
```tsx
import { Button } from '../components/ui/Button';

// Primary action
<Button 
  title="Book Service" 
  onPress={handleBooking}
  variant="primary"
  size="lg"
  fullWidth
/>

// Emergency button
<Button 
  title="Emergency Call" 
  onPress={handleEmergency}
  variant="emergency"
  icon={<PhoneIcon />}
/>

// Loading state
<Button 
  title="Processing..." 
  onPress={handleSubmit}
  variant="primary"
  loading={isLoading}
/>
```

#### Web (React)
```tsx
import { Button } from '../components/ui/Button';

// Primary action
<Button 
  variant="primary" 
  size="lg" 
  fullWidth
  onClick={handleBooking}
>
  Book Service
</Button>

// Emergency button
<Button 
  variant="emergency"
  icon={<PhoneIcon />}
  onClick={handleEmergency}
>
  Emergency Call
</Button>

// Loading state
<Button 
  variant="primary"
  loading={isLoading}
  onClick={handleSubmit}
>
  Processing...
</Button>
```

### Cards

#### Mobile (React Native)
```tsx
import { JobCard } from '../components/ui/Card';
import { StatusBadge } from '../components/ui/StatusBadge';

<JobCard 
  status="active" 
  priority="high"
  onPress={() => navigation.navigate('JobDetails', { jobId })}
>
  <View style={styles.cardHeader}>
    <Text style={styles.jobTitle}>Plumbing Repair</Text>
    <StatusBadge status="active" />
  </View>
  <Text style={styles.jobDescription}>
    Kitchen sink leak requiring immediate attention
  </Text>
  <View style={styles.cardFooter}>
    <Text style={styles.technicianName}>John Smith</Text>
    <Text style={styles.estimatedTime}>2 hours</Text>
  </View>
</JobCard>
```

#### Web (React)
```tsx
import { JobCard, CardHeader, CardContent, CardFooter } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

<JobCard 
  status="active" 
  priority="high"
  onClick={() => navigate(`/jobs/${jobId}`)}
>
  <CardHeader 
    title="Plumbing Repair"
    subtitle="Kitchen sink leak"
    action={<Badge variant="active">In Progress</Badge>}
  />
  <CardContent>
    Kitchen sink leak requiring immediate attention. 
    Customer reports water damage spreading to adjacent areas.
  </CardContent>
  <CardFooter>
    <div className="flex justify-between items-center">
      <span className="text-sm text-gray-600">John Smith</span>
      <span className="text-sm font-medium">Est. 2 hours</span>
    </div>
  </CardFooter>
</JobCard>
```

### Status Badges

#### Mobile (React Native)
```tsx
import { StatusBadge, VerificationBadge, PriorityBadge } from '../components/ui/StatusBadge';

// Job status
<StatusBadge status="pending" size="md" />
<StatusBadge status="completed" size="lg" />

// Technician verification
<VerificationBadge verified={true} showIcon={true} />

// Job priority
<PriorityBadge priority="emergency" />
<PriorityBadge priority="high" />
```

#### Web (React)
```tsx
import { Badge } from '../components/ui/Badge';

// Job status
<Badge variant="pending">Pending</Badge>
<Badge variant="completed">Completed</Badge>

// Technician verification
<Badge variant="verified" icon={<CheckIcon />}>
  Verified
</Badge>

// Job priority
<Badge variant="emergency">Emergency</Badge>
<Badge variant="high">High Priority</Badge>
```

## Form Components

### Input Fields

#### Mobile (React Native)
```tsx
import { TextInput, View, Text } from 'react-native';
import { colors, spacing, borderRadius } from '../styles/theme';

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: spacing[6],
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing[2],
  },
  input: {
    borderWidth: 2,
    borderColor: colors.border.medium,
    borderRadius: borderRadius.md,
    padding: spacing[4],
    fontSize: 16,
    backgroundColor: colors.background.primary,
    minHeight: 48,
  },
  inputFocused: {
    borderColor: colors.primary[900],
  },
  inputError: {
    borderColor: colors.error,
  },
});

<View style={styles.formGroup}>
  <Text style={styles.label}>Service Address</Text>
  <TextInput
    style={[styles.input, isFocused && styles.inputFocused]}
    placeholder="Enter your address"
    value={address}
    onChangeText={setAddress}
    onFocus={() => setIsFocused(true)}
    onBlur={() => setIsFocused(false)}
  />
</View>
```

#### Web (React)
```tsx
// Form input component
<div className="mb-6">
  <label className="block text-sm font-semibold text-gray-900 mb-2">
    Service Address
  </label>
  <input
    type="text"
    className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg 
               focus:border-blue-900 focus:ring-2 focus:ring-blue-100 
               focus:outline-none transition-colors"
    placeholder="Enter your address"
    value={address}
    onChange={(e) => setAddress(e.target.value)}
  />
</div>
```

## Navigation Components

### Bottom Navigation (Mobile)
```tsx
import { View, TouchableOpacity, Text } from 'react-native';
import { colors, spacing } from '../styles/theme';

const BottomNavigation = ({ activeTab, onTabPress }) => {
  const tabs = [
    { id: 'home', label: 'Home', icon: HomeIcon },
    { id: 'jobs', label: 'Jobs', icon: JobsIcon },
    { id: 'technicians', label: 'Technicians', icon: TechnicianIcon },
    { id: 'profile', label: 'Profile', icon: ProfileIcon },
  ];

  return (
    <View style={styles.bottomNav}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={[
            styles.navItem,
            activeTab === tab.id && styles.navItemActive
          ]}
          onPress={() => onTabPress(tab.id)}
        >
          <tab.icon 
            size={24} 
            color={activeTab === tab.id ? colors.primary[900] : colors.gray[500]} 
          />
          <Text style={[
            styles.navLabel,
            activeTab === tab.id && styles.navLabelActive
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};
```

### Header Navigation (Web)
```tsx
const HeaderNavigation = () => {
  return (
    <header className="bg-white border-b border-gray-200 px-4 py-4 sticky top-0 z-50">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <img src="/logo.svg" alt="CTRON Home" className="h-8" />
          <h1 className="text-xl font-bold text-gray-900">Admin Panel</h1>
        </div>
        
        <nav className="hidden md:flex space-x-6">
          <a href="/dashboard" className="text-gray-600 hover:text-blue-900">
            Dashboard
          </a>
          <a href="/jobs" className="text-gray-600 hover:text-blue-900">
            Jobs
          </a>
          <a href="/technicians" className="text-gray-600 hover:text-blue-900">
            Technicians
          </a>
        </nav>
        
        <div className="flex items-center space-x-4">
          <button className="p-2 text-gray-600 hover:text-blue-900">
            <BellIcon className="h-5 w-5" />
          </button>
          <button className="p-2 text-gray-600 hover:text-blue-900">
            <UserIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </header>
  );
};
```

## Theme Switching

### Mobile Implementation
```tsx
import { useTheme } from '../contexts/ThemeContext';

const ThemeSelector = () => {
  const { theme, setTheme } = useTheme();
  
  return (
    <View style={styles.themeSelector}>
      <Button
        title="Light"
        onPress={() => setTheme(lightTheme)}
        variant={theme === lightTheme ? 'primary' : 'secondary'}
      />
      <Button
        title="Dark"
        onPress={() => setTheme(darkTheme)}
        variant={theme === darkTheme ? 'primary' : 'secondary'}
      />
      <Button
        title="Professional"
        onPress={() => setTheme(professionalTheme)}
        variant={theme === professionalTheme ? 'primary' : 'secondary'}
      />
    </View>
  );
};
```

### Web Implementation
```tsx
const ThemeSelector = () => {
  const [theme, setTheme] = useState('light');
  
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);
  
  return (
    <div className="flex space-x-2">
      <button
        onClick={() => setTheme('light')}
        className={`px-3 py-2 rounded ${
          theme === 'light' ? 'bg-blue-900 text-white' : 'bg-gray-200'
        }`}
      >
        Light
      </button>
      <button
        onClick={() => setTheme('dark')}
        className={`px-3 py-2 rounded ${
          theme === 'dark' ? 'bg-blue-900 text-white' : 'bg-gray-200'
        }`}
      >
        Dark
      </button>
      <button
        onClick={() => setTheme('professional')}
        className={`px-3 py-2 rounded ${
          theme === 'professional' ? 'bg-blue-900 text-white' : 'bg-gray-200'
        }`}
      >
        Professional
      </button>
    </div>
  );
};
```

## Accessibility Implementation

### Screen Reader Support
```tsx
// Mobile
<TouchableOpacity
  accessible={true}
  accessibilityRole="button"
  accessibilityLabel="Book emergency plumbing service"
  accessibilityHint="Connects you with available emergency plumbers"
>
  <Text>Emergency Service</Text>
</TouchableOpacity>

// Web
<button
  aria-label="Book emergency plumbing service"
  aria-describedby="emergency-help-text"
>
  Emergency Service
</button>
<div id="emergency-help-text" className="sr-only">
  Connects you with available emergency plumbers in your area
</div>
```

### Focus Management
```tsx
// Mobile - Focus management with refs
const buttonRef = useRef<TouchableOpacity>(null);

useEffect(() => {
  if (shouldFocus) {
    buttonRef.current?.focus();
  }
}, [shouldFocus]);

// Web - Focus management
const buttonRef = useRef<HTMLButtonElement>(null);

useEffect(() => {
  if (shouldFocus) {
    buttonRef.current?.focus();
  }
}, [shouldFocus]);
```

## Performance Optimization

### Mobile Optimizations
```tsx
// Use StyleSheet.create for better performance
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
});

// Memoize expensive components
const JobCard = React.memo(({ job, onPress }) => {
  return (
    <Card onPress={onPress}>
      {/* Card content */}
    </Card>
  );
});

// Use FlatList for large datasets
<FlatList
  data={jobs}
  renderItem={({ item }) => <JobCard job={item} />}
  keyExtractor={(item) => item.id}
  getItemLayout={(data, index) => ({
    length: ITEM_HEIGHT,
    offset: ITEM_HEIGHT * index,
    index,
  })}
/>
```

### Web Optimizations
```tsx
// Use CSS-in-JS libraries sparingly, prefer CSS classes
const JobCard = React.memo(({ job, onClick }) => {
  return (
    <div className="job-card" onClick={onClick}>
      {/* Card content */}
    </div>
  );
});

// Lazy load components
const TechnicianDetails = React.lazy(() => import('./TechnicianDetails'));

// Use virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window';

<List
  height={600}
  itemCount={jobs.length}
  itemSize={120}
  itemData={jobs}
>
  {JobCardRow}
</List>
```

## Testing Guidelines

### Component Testing
```tsx
// Mobile testing with React Native Testing Library
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '../Button';

test('button calls onPress when pressed', () => {
  const onPress = jest.fn();
  const { getByText } = render(
    <Button title="Test Button" onPress={onPress} />
  );
  
  fireEvent.press(getByText('Test Button'));
  expect(onPress).toHaveBeenCalled();
});

// Web testing with React Testing Library
import { render, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

test('button calls onClick when clicked', () => {
  const onClick = jest.fn();
  const { getByText } = render(
    <Button onClick={onClick}>Test Button</Button>
  );
  
  fireEvent.click(getByText('Test Button'));
  expect(onClick).toHaveBeenCalled();
});
```

### Accessibility Testing
```tsx
// Test accessibility attributes
test('button has correct accessibility attributes', () => {
  const { getByRole } = render(
    <Button aria-label="Emergency service">Emergency</Button>
  );
  
  const button = getByRole('button');
  expect(button).toHaveAttribute('aria-label', 'Emergency service');
});
```

## Deployment Checklist

### Pre-deployment
- [ ] All components follow design system specifications
- [ ] Accessibility requirements met (WCAG 2.1 AA)
- [ ] Cross-platform consistency verified
- [ ] Performance benchmarks met
- [ ] All themes tested and functional
- [ ] Component documentation updated
- [ ] Unit tests passing
- [ ] Integration tests passing
- [ ] Visual regression tests passing

### Post-deployment
- [ ] Monitor performance metrics
- [ ] Collect user feedback
- [ ] Track accessibility compliance
- [ ] Update design system based on learnings
- [ ] Document any customizations or extensions

---

*This implementation guide ensures consistent application of the CTRON Home Design System across all platforms while maintaining flexibility for platform-specific optimizations.*
