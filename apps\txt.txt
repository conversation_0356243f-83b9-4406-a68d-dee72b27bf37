
scan the current codebase before implementing this

Please note that we’re not to modify stripe’s Identity SDK. It’s in their privacy and agreement policy

For the MVP, let’s keep qualification checks simple without adding a new table. Here’s what I need you to do:

Technician Schema Update (Prisma)

Please update the existing Technician model to include:

ts
model Technician {
  id                  String   @id @default(uuid())
  userId              String   @unique
  specialization      String
  isAvailable         Boolean  @default(true)
  rating              Float?
  kycStatus           String   @default("PENDING")

  // ✅ New fields for qualification verification
  qualificationFile   String?     // URL to uploaded certificate (e.g. Cloudinary or S3)
  qualificationStatus String      @default("PENDING") // Can be: 'PENDING', 'APPROVED', 'REJECTED'

  createdAt           DateTime @default(now())
  user                User     @relation(fields: [userId], references: [id])
}


✅ What it should do:

* During technician onboarding, allow file upload for qualification (PDF or image).
* Save the uploaded file's URL to qualificationFile.
* Default qualificationStatus should be 'PENDING'.
* <PERSON><PERSON> should be able to see the file and set status to 'APPROVED' or 'REJECTED'.

Let’s keep this lean and simple we’ll expand it into a separate table later if we support multiple certs or expiry dates.

## CTRON Home – KYC Verification Setup (Stripe Identity)

### ✅ Overview

We’re using **Stripe Identity** to verify technician documents and selfies during onboarding. This ensures only verified, real professionals can accept jobs.

This guide explains how to:

1. Create verification sessions via Stripe
2. Send technicians to a secure verification link
3. Receive webhook events from Stripe
4. Update technician KYC status in our PostgreSQL DB (via Prisma)
5. Display verification status in Admin dashboard

---

## 🔧 Step-by-Step Setup

### 1️⃣ Stripe Identity Setup

* Go to: 
* Make sure Identity is enabled on the account
* Get your **Secret Key** from Stripe → store in `.env` as:

```env
STRIPE_SECRET_KEY=sk_test_xxx
```

Install Stripe SDK in backend:

```bash
npm install stripe
```

---

### 2️⃣ Create Verification Session (Backend)

In your `technician.controller.ts`, add:

```ts
// import Stripe
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: '2023-10-16' });

export const createVerificationSession = asyncHandler(async (req, res) => {
  const { technicianId } = req.body;

  const session = await stripe.identity.verificationSessions.create({
    type: 'document',
    metadata: { technicianId },
    return_url: '', // update this
  });

  res.json({ url: session.url });
});
```

---

### 3️⃣ Create Endpoint Route

In `technician.routes.ts`:

```ts
router.post('/kyc/start', authMiddleware, roleMiddleware(['TECHNICIAN']), createVerificationSession);
```

---

### 4️⃣ Set Up Stripe Webhook Listener

Add this to your backend (or webhook handler file):

```ts
app.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature']!;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === 'identity.verification_session.verified') {
    const session = event.data.object;
    const technicianId = session.metadata?.technicianId;

    await prisma.technician.update({
      where: { id: technicianId },
      data: { kycStatus: 'APPROVED' },
    });
  }

  res.status(200).send();
});
```

---

### 5️⃣ Update Prisma Technician Model

Ensure this field exists:

```ts
model Technician {
  id          String   @id @default(uuid())
  ...
  kycStatus   String   @default("PENDING") // 'PENDING' | 'APPROVED' | 'REJECTED'
}
```

---

### 6️⃣ Admin Dashboard

In the Admin panel (frontend):

* Show technician's `kycStatus` (PENDING / APPROVED)
* Optionally allow manual override (PATCH `/technicians/:id/approve`)

---

### ✅ Final Notes

* Stripe hosts the verification UI – no need to build your own doc capture UI.
* Make sure `webhook` endpoint is **publicly reachable**.
* Stripe handles all fraud, document validation, and liveness checks.

---

### 📦 Optional (Test Webhooks)

To test webhooks locally:

```bash
stripe listen --forward-to localhost:5000/webhook
```

---

### 🎯 Success Criteria

* Technician clicks “Verify ID” → redirected to Stripe page


* On success, webhook updates `kycStatus` to “APPROVED”
* Admin can see status instantly

---


