# ===========================================
# CTRON HOME WEB APP - DEVELOPMENT ENVIRONMENT
# ===========================================

# API Configuration
VITE_API_URL=http://*************:3001/api
VITE_API_BASE_URL=http://*************:3001
VITE_SOCKET_URL=http://*************:3001

# Stripe Configuration (Test Keys)
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here

# App Configuration
VITE_APP_NAME=CTRON Home
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# External Services
VITE_GOOGLE_MAPS_API_KEY=
VITE_ANALYTICS_ID=

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK_DATA=false

# Upload Configuration
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Timeouts and Limits
VITE_API_TIMEOUT=10000
VITE_SOCKET_TIMEOUT=5000
