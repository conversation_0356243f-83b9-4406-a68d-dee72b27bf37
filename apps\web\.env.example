# CTRON Home Web Application Environment Configuration
# Copy this file to .env.local and update the values

# API Configuration
VITE_API_URL=http://localhost:3001/api
VITE_API_BASE_URL=http://localhost:3001
VITE_SOCKET_URL=http://localhost:3001

# Stripe Configuration (for payments)
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here

# Application Configuration
VITE_APP_NAME=CTRON Home
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# External Services
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
VITE_ANALYTICS_ID=your_analytics_id_here

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK_DATA=true

# Upload Configuration
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Timeouts and Limits
VITE_API_TIMEOUT=10000
VITE_SOCKET_TIMEOUT=5000

# Production Environment Variables (for reference)
# VITE_API_URL=https://api.ctronhome.com/api
# VITE_API_BASE_URL=https://api.ctronhome.com
# VITE_SOCKET_URL=https://api.ctronhome.com
# VITE_STRIPE_PUBLIC_KEY=pk_live_your_live_stripe_key
# VITE_ENABLE_ANALYTICS=true
# VITE_ENABLE_DEBUG=false
# VITE_ENABLE_MOCK_DATA=false
