{"name": "ctron-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "dev:staging": "vite --mode staging", "dev:production": "vite --mode production", "build": "vite build --mode production", "build:staging": "vite build --mode staging", "build:development": "vite build --mode development", "preview": "vite preview", "preview:staging": "vite preview --mode staging"}, "dependencies": {"axios": "^1.9.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.513.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.14.1", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "zustand": "^4.3.9"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5"}}