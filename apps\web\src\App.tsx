// 📁 File: apps/web/src/App.tsx

import { Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Dashboard from './pages/admin/Dashboard';
import Technicians from './pages/admin/Technicians';
import { AssistantPage } from './pages/admin/AssistantPage'; // ✅ Enhanced AI Assistant Page

import Sidebar from './components/Sidebar';
import PrivateRoute from './components/PrivateRoute';
import RoleRoute from './components/RoleRoute';
import ErrorBoundary from './components/ErrorBoundary';

const App = () => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // In production, send to error tracking service
        if (!import.meta.env.DEV) {
          // Example: Sentry.captureException(error, { contexts: { react: errorInfo } });
          console.error('Application error:', error, errorInfo);
        }
      }}
    >
      <Routes>
        {/* Public route */}
        <Route path="/login" element={<Login />} />

        {/* Protected route: Any authenticated user */}
        <Route
          path="/dashboard"
          element={
            <PrivateRoute>
              <ErrorBoundary>
                <Layout title="Dashboard">
                  <Dashboard />
                </Layout>
              </ErrorBoundary>
            </PrivateRoute>
          }
        />

        {/* Protected route: Admin only */}
        <Route
          path="/technicians"
          element={
            <RoleRoute allowedRoles={['ADMIN']}>
              <ErrorBoundary>
                <Layout title="Technicians">
                  <Technicians />
                </Layout>
              </ErrorBoundary>
            </RoleRoute>
          }
        />

        {/* Jobs Routes */}
        <Route
          path="/jobs"
          element={
            <RoleRoute allowedRoles={['ADMIN', 'DISPATCHER']}>
              <ErrorBoundary>
                <Layout title="Jobs">
                  <Jobs />
                </Layout>
              </ErrorBoundary>
            </RoleRoute>
          }
        />
        <Route
          path="/jobs/:id"
          element={
            <PrivateRoute>
              <ErrorBoundary>
                <Layout title="Job Details">
                  <JobDetails />
                </Layout>
              </ErrorBoundary>
            </PrivateRoute>
          }
        />

        {/* Settings Route */}
        <Route
          path="/settings"
          element={
            <PrivateRoute>
              <ErrorBoundary>
                <Layout title="Settings">
                  <Settings />
                </Layout>
              </ErrorBoundary>
            </PrivateRoute>
          }
        />

        {/* Assistant Page */}
        <Route
          path="/assistant"
          element={
            <RoleRoute allowedRoles={['ADMIN']}>
              <ErrorBoundary>
                <Layout title="AI Assistant">
                  <AssistantPage />
                </Layout>
              </ErrorBoundary>
            </RoleRoute>
          }
        />

        {/* Fallback: redirect unknown routes */}
        <Route path="*" element={<Navigate to="/login" />} />
      </Routes>
    </ErrorBoundary>
  );
};

export default App;
