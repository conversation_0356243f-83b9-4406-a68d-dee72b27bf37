// apps/web/src/api/ai.api.ts

import api from '../services/api';

interface AIQueryRequest {
  query: string;
  context?: string;
}

interface AIResponse {
  response: string;
  confidence: number;
  sources?: string[];
  suggestions?: string[];
}

interface AIFeedbackRequest {
  queryId: string;
  rating: number;
  feedback?: string;
}

interface QueryHistory {
  queries: Array<{
    id: string;
    query: string;
    response: string;
    timestamp: string;
    rating?: number;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface AIStatus {
  available: boolean;
  model: string;
  capabilities: string[];
  supportedRoles: string[];
  maxQueryLength: number;
  responseTime: string;
}

class AIAPIService {
  /**
   * Process AI query
   */
  async processQuery(data: AIQueryRequest): Promise<{ data: AIResponse }> {
    const response = await api.post('/ai/query', data);
    return response.data;
  }

  /**
   * Get query templates for current user role
   */
  async getTemplates(): Promise<{ data: { templates: string[] } }> {
    const response = await api.get('/ai/templates');
    return response.data;
  }

  /**
   * Get AI-powered suggestions
   */
  async getSuggestions(): Promise<{ data: { suggestions: string[] } }> {
    const response = await api.get('/ai/suggestions');
    return response.data;
  }

  /**
   * Submit feedback on AI response
   */
  async submitFeedback(data: AIFeedbackRequest): Promise<{ data: { message: string } }> {
    const response = await api.post('/ai/feedback', data);
    return response.data;
  }

  /**
   * Get query history
   */
  async getHistory(params?: {
    page?: number;
    limit?: number;
  }): Promise<{ data: QueryHistory }> {
    const response = await api.get('/ai/history', { params });
    return response.data;
  }

  /**
   * Get AI service status
   */
  async getStatus(): Promise<{ data: AIStatus }> {
    const response = await api.get('/ai/status');
    return response.data;
  }
}

export const aiAPI = new AIAPIService();
