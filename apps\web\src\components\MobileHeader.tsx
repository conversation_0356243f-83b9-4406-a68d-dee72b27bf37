// apps/web/src/components/MobileHeader.tsx

import { <PERSON><PERSON>, <PERSON>, User } from 'lucide-react';

interface MobileHeaderProps {
  onMenuClick: () => void;
  title?: string;
}

const MobileHeader = ({ onMenuClick, title = "CTRON Admin" }: MobileHeaderProps) => {
  return (
    <header className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between shadow-sm">
      <div className="flex items-center gap-3">
        <button
          onClick={onMenuClick}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors min-h-touch min-w-touch"
          aria-label="Open menu"
        >
          <Menu className="w-6 h-6 text-gray-700" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900 truncate">{title}</h1>
      </div>
      
      <div className="flex items-center gap-2">
        <button
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors min-h-touch min-w-touch relative"
          aria-label="Notifications"
        >
          <Bell className="w-5 h-5 text-gray-700" />
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
        </button>
        
        <button
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors min-h-touch min-w-touch"
          aria-label="User menu"
        >
          <User className="w-5 h-5 text-gray-700" />
        </button>
      </div>
    </header>
  );
};

export default MobileHeader;
