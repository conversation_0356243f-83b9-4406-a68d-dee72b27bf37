// 📁 File: apps/web/src/components/PrivateRoute.tsx

import React, { JSX } from 'react';
import { Navigate } from 'react-router-dom';
import isTokenExpired from '../utils/checkTokenExpiry';
import { toast } from 'react-toastify';

const PrivateRoute = ({ children }: { children: JSX.Element }) => {
  const token = localStorage.getItem('token');

  if (!token || isTokenExpired(token)) {
    localStorage.removeItem('token');
    toast.warning('Session expired. Please login again.');
    return <Navigate to="/login" />;
  }

  return children;
};

export default PrivateRoute;
