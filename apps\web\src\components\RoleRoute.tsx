//apps/web/src/components/RoleRoute.tsx
import React, { JSX } from 'react';
import { Navigate } from 'react-router-dom';

type Props = {
  allowedRoles: string[];
  children: JSX.Element;
};

interface JWTPayload {
  userId: string;
  role: string;
  email: string;
  exp: number;
}

const RoleRoute = ({ allowedRoles, children }: Props) => {
  const token = localStorage.getItem('token');

  if (!token) {
    return <Navigate to="/login" />;
  }

  try {
    // Decode JWT token to get role information
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    const decoded: JWTPayload = JSON.parse(jsonPayload);

    // Check if token is expired
    if (decoded.exp && decoded.exp < Date.now() / 1000) {
      localStorage.removeItem('token');
      localStorage.removeItem('role'); // Clean up old role storage
      return <Navigate to="/login" />;
    }

    // Validate role from JWT token
    if (!decoded.role || !allowedRoles.includes(decoded.role)) {
      return <Navigate to="/login" />;
    }

    return children;
  } catch (error) {
    // Invalid token format or parsing error
    console.error('Invalid token format:', error);
    localStorage.removeItem('token');
    localStorage.removeItem('role'); // Clean up old role storage
    return <Navigate to="/login" />;
  }
};

export default RoleRoute;
