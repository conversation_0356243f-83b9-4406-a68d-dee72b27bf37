//web/src/components/Sidebar.tsx
import { Link, useLocation } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Menu, X, Home, Users, Bot, Briefcase, Settings } from 'lucide-react';

interface SidebarProps {
  isMobileMenuOpen?: boolean;
  setIsMobileMenuOpen?: (open: boolean) => void;
}

const Sidebar = ({ isMobileMenuOpen = false, setIsMobileMenuOpen }: SidebarProps) => {
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const linkClass = (path: string) =>
    `flex items-center gap-3 py-3 px-4 rounded-lg font-medium transition-all duration-200 min-h-touch ${
      location.pathname === path
        ? 'bg-blue-600 text-white shadow-lg'
        : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
    }`;

  const handleLinkClick = () => {
    if (isMobile && setIsMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  };

  const sidebarContent = (
    <>
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-xl font-bold text-gray-900">CTRON Admin</h2>
        {isMobile && (
          <button
            onClick={() => setIsMobileMenuOpen?.(false)}
            className="p-2 rounded-lg hover:bg-gray-200 transition-colors min-h-touch min-w-touch"
            aria-label="Close menu"
          >
            <X className="w-6 h-6" />
          </button>
        )}
      </div>

      <nav className="space-y-2">
        <Link
          to="/dashboard"
          className={linkClass('/dashboard')}
          onClick={handleLinkClick}
        >
          <Home className="w-5 h-5" />
          <span>Dashboard</span>
        </Link>
        <Link
          to="/technicians"
          className={linkClass('/technicians')}
          onClick={handleLinkClick}
        >
          <Users className="w-5 h-5" />
          <span>Technicians</span>
        </Link>
        <Link
          to="/jobs"
          className={linkClass('/jobs')}
          onClick={handleLinkClick}
        >
          <Briefcase className="w-5 h-5" />
          <span>Jobs</span>
        </Link>
        <Link
          to="/assistant"
          className={linkClass('/assistant')}
          onClick={handleLinkClick}
        >
          <Bot className="w-5 h-5" />
          <span>AI Assistant</span>
        </Link>
        <Link
          to="/settings"
          className={linkClass('/settings')}
          onClick={handleLinkClick}
        >
          <Settings className="w-5 h-5" />
          <span>Settings</span>
        </Link>
      </nav>
    </>
  );

  if (isMobile) {
    return (
      <>
        {/* Mobile Overlay */}
        {isMobileMenuOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-mobile-overlay md:hidden"
            onClick={() => setIsMobileMenuOpen?.(false)}
          />
        )}

        {/* Mobile Sidebar */}
        <aside className={`
          fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-mobile-nav
          transform transition-transform duration-300 ease-in-out md:hidden
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="p-6 h-full overflow-y-auto">
            {sidebarContent}
          </div>
        </aside>
      </>
    );
  }

  // Desktop Sidebar
  return (
    <aside className="hidden md:flex md:flex-col w-64 h-screen bg-white border-r border-gray-200 shadow-sm">
      <div className="p-6 h-full overflow-y-auto">
        {sidebarContent}
      </div>
    </aside>
  );
};

export default Sidebar;
