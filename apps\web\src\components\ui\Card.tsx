// CTRON Home Design System - Web Card Component
// Flexible card component for jobs, technicians, and content display

import React from 'react';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'job' | 'technician';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  clickable?: boolean;
}

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({
    className = '',
    variant = 'default',
    padding = 'md',
    hover = false,
    clickable = false,
    children,
    ...props
  }, ref) => {
    const baseStyles = [
      'bg-white rounded-lg overflow-hidden transition-all duration-200',
    ].join(' ');

    const variants = {
      default: 'border border-gray-200',
      elevated: 'shadow-md border border-gray-200',
      outlined: 'border-2 border-gray-300',
      job: 'shadow-sm border border-gray-200 mb-4',
      technician: 'shadow-sm border border-gray-200 relative',
    };

    const paddings = {
      none: '',
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8',
    };

    const interactionStyles = [
      hover && 'hover:transform hover:-translate-y-1 hover:shadow-lg',
      clickable && 'cursor-pointer',
    ].filter(Boolean).join(' ');

    const cardClasses = [
      baseStyles,
      variants[variant],
      paddings[padding],
      interactionStyles,
      className,
    ].filter(Boolean).join(' ');

    return (
      <div
        className={cardClasses}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Job Card Specific Component
export interface JobCardProps extends CardProps {
  status?: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled';
  priority?: 'low' | 'medium' | 'high' | 'emergency';
}

export const JobCard = React.forwardRef<HTMLDivElement, JobCardProps>(
  ({
    className = '',
    status,
    priority,
    children,
    ...props
  }, ref) => {
    const statusBorders = {
      pending: 'border-l-4 border-l-yellow-500',
      assigned: 'border-l-4 border-l-blue-500',
      active: 'border-l-4 border-l-red-500',
      completed: 'border-l-4 border-l-green-500',
      cancelled: 'border-l-4 border-l-gray-500',
    };

    const priorityStyles = {
      emergency: 'border-2 border-orange-500 shadow-lg',
    };

    const jobCardClasses = [
      status && statusBorders[status],
      priority === 'emergency' && priorityStyles.emergency,
      className,
    ].filter(Boolean).join(' ');

    return (
      <Card
        variant="job"
        className={jobCardClasses}
        hover
        ref={ref}
        {...props}
      >
        {children}
      </Card>
    );
  }
);

JobCard.displayName = 'JobCard';

// Technician Card Specific Component
export interface TechnicianCardProps extends CardProps {
  verified?: boolean;
  available?: boolean;
}

export const TechnicianCard = React.forwardRef<HTMLDivElement, TechnicianCardProps>(
  ({
    className = '',
    verified,
    available,
    children,
    ...props
  }, ref) => {
    const technicianCardClasses = [
      verified && 'border-t-4 border-t-green-500',
      available && 'bg-green-50',
      className,
    ].filter(Boolean).join(' ');

    return (
      <Card
        variant="technician"
        className={technicianCardClasses}
        hover
        ref={ref}
        {...props}
      >
        {children}
      </Card>
    );
  }
);

TechnicianCard.displayName = 'TechnicianCard';

// Card Header Component
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
}

export const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({
    className = '',
    title,
    subtitle,
    action,
    children,
    ...props
  }, ref) => {
    return (
      <div
        className={`flex justify-between items-start mb-4 ${className}`}
        ref={ref}
        {...props}
      >
        <div className="flex-1">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600">
              {subtitle}
            </p>
          )}
          {children}
        </div>
        {action && (
          <div className="ml-4 flex-shrink-0">
            {action}
          </div>
        )}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// Card Content Component
export const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className = '', children, ...props }, ref) => {
    return (
      <div
        className={`text-gray-700 ${className}`}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardContent.displayName = 'CardContent';

// Card Footer Component
export const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className = '', children, ...props }, ref) => {
    return (
      <div
        className={`mt-4 pt-4 border-t border-gray-200 ${className}`}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';

export default Card;
