// apps/web/src/components/ui/Input.tsx

import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      variant = 'default',
      size = 'md',
      fullWidth = false,
      className = '',
      ...props
    },
    ref
  ) => {
    const baseClasses = 'border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500';
    
    const variantClasses = {
      default: 'border-gray-300 bg-white',
      filled: 'border-gray-300 bg-gray-50',
      outlined: 'border-2 border-gray-300 bg-transparent'
    };

    const sizeClasses = {
      sm: 'px-3 py-3 text-sm min-h-touch', // Touch-friendly minimum height
      md: 'px-4 py-3 text-base min-h-touch-lg', // Larger touch target
      lg: 'px-4 py-4 text-lg min-h-[52px]'
    };

    const errorClasses = error ? 'border-red-500 focus:ring-red-500' : '';
    const widthClasses = fullWidth ? 'w-full' : '';

    const inputClasses = [
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      errorClasses,
      widthClasses,
      className
    ].filter(Boolean).join(' ');

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <input
          ref={ref}
          className={inputClasses}
          {...props}
        />
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500">{helperText}</p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';
