// CTRON Home Design System - Badge Component
// Status indicators and labels for jobs, technicians, and system states

import React from 'react';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' | 'overdue' |
           'verified' | 'available' | 'offline' | 'low' | 'medium' | 'high' | 'emergency' |
           'default' | 'secondary' | 'destructive' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
}

export const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className = '', variant = 'default', size = 'md', icon, children, ...props }, ref) => {
    const baseStyles = 'inline-flex items-center rounded-full font-semibold uppercase tracking-wide transition-colors';

    const variants = {
      // Job Status Variants
      pending: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      assigned: 'bg-blue-100 text-blue-800 border border-blue-200',
      active: 'bg-red-100 text-red-800 border border-red-200',
      completed: 'bg-green-100 text-green-800 border border-green-200',
      cancelled: 'bg-gray-100 text-gray-800 border border-gray-200',
      overdue: 'bg-red-200 text-red-900 border border-red-300',

      // Technician Status Variants
      verified: 'bg-green-500 text-white border border-green-600',
      available: 'bg-green-100 text-green-800 border border-green-200',
      offline: 'bg-gray-100 text-gray-600 border border-gray-200',

      // Priority Variants
      low: 'bg-gray-100 text-gray-600 border border-gray-200',
      medium: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      high: 'bg-orange-100 text-orange-800 border border-orange-200',
      emergency: 'bg-red-500 text-white border border-red-600 animate-pulse',

      // General Variants
      default: 'bg-blue-100 text-blue-800 border border-blue-200',
      secondary: 'bg-gray-100 text-gray-800 border border-gray-200',
      destructive: 'bg-red-100 text-red-800 border border-red-200',
      outline: 'text-gray-700 border border-gray-300 bg-transparent',
    };

    const sizes = {
      sm: 'px-2 py-0.5 text-xs',
      md: 'px-3 py-1 text-xs',
      lg: 'px-4 py-2 text-sm',
    };

    const badgeClasses = [
      baseStyles,
      variants[variant],
      sizes[size],
      className,
    ].filter(Boolean).join(' ');

    return (
      <div className={badgeClasses} ref={ref} {...props}>
        {icon && <span className="mr-1">{icon}</span>}
        {children}
      </div>
    );
  }
);

Badge.displayName = 'Badge';
