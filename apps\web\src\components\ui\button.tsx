// CTRON Home Design System - Web Button Component
// Professional, accessible button implementation for web admin

import React from 'react';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'emergency' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className = '',
    variant = 'primary',
    size = 'md',
    loading = false,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    children,
    disabled,
    ...props
  }, ref) => {
    const baseStyles = [
      // Base styles
      'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
      'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',

      // Enhanced hover effects
      'hover:transform hover:scale-105 hover:shadow-xl active:transform active:scale-95',
    ].join(' ');

    const variants = {
      primary: [
        'bg-gradient-to-r from-blue-600 to-blue-700 text-white',
        'hover:from-blue-700 hover:to-blue-800',
        'shadow-lg hover:shadow-2xl',
        'border border-transparent',
      ].join(' '),
      secondary: [
        'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900',
        'hover:from-gray-200 hover:to-gray-300',
        'shadow-md hover:shadow-lg',
        'border border-gray-300',
      ].join(' '),
      emergency: [
        'bg-gradient-to-r from-red-600 to-red-700 text-white',
        'hover:from-red-700 hover:to-red-800',
        'shadow-lg hover:shadow-2xl',
        'border border-transparent',
      ].join(' '),
      ghost: [
        'bg-transparent text-gray-700 border border-transparent',
        'hover:bg-gray-100',
      ].join(' '),
      outline: [
        'bg-white text-gray-700 border border-gray-300',
        'hover:bg-gray-50 hover:border-gray-400',
        'shadow-sm hover:shadow-md',
      ].join(' '),
    };

    const sizes = {
      sm: 'px-4 py-3 text-sm min-h-touch min-w-touch', // 44px minimum for touch
      md: 'px-6 py-3 text-base min-h-touch-lg min-w-touch-lg', // 48px minimum for touch
      lg: 'px-8 py-4 text-lg min-h-[52px] min-w-[52px]',
    };

    const widthClass = fullWidth ? 'w-full' : '';

    const buttonClasses = [
      baseStyles,
      variants[variant],
      sizes[size],
      widthClass,
      className
    ].filter(Boolean).join(' ');

    const renderContent = () => {
      if (loading) {
        return (
          <>
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            Loading...
          </>
        );
      }

      if (icon) {
        return (
          <>
            {iconPosition === 'left' && <span className="mr-2">{icon}</span>}
            {children}
            {iconPosition === 'right' && <span className="ml-2">{icon}</span>}
          </>
        );
      }

      return children;
    };

    return (
      <button
        className={buttonClasses}
        ref={ref}
        disabled={disabled || loading}
        aria-label={props['aria-label'] || (typeof children === 'string' ? children : undefined)}
        aria-describedby={props['aria-describedby']}
        aria-disabled={disabled || loading}
        {...props}
      >
        {renderContent()}
      </button>
    );
  }
);

Button.displayName = 'Button';
