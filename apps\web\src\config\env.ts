// apps/web/src/config/env.ts

// Get environment-specific configuration
const getEnvironmentConfig = () => {
  const isDevelopment = import.meta.env.MODE === 'development';

  return {
    // API Configuration - only allow localhost in development
    API_URL: import.meta.env.VITE_API_URL || (isDevelopment ? 'http://localhost:3001/api' : ''),
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || (isDevelopment ? 'http://localhost:3001' : ''),
    SOCKET_URL: import.meta.env.VITE_SOCKET_URL || (isDevelopment ? 'http://localhost:3001' : ''),

    // Stripe Configuration
    STRIPE_PUBLIC_KEY: import.meta.env.VITE_STRIPE_PUBLIC_KEY || '',

    // App Configuration
    APP_NAME: import.meta.env.VITE_APP_NAME || 'CTRON Home',
    APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
    APP_ENVIRONMENT: import.meta.env.VITE_APP_ENVIRONMENT || (isDevelopment ? 'development' : 'production'),

    // External Services
    GOOGLE_MAPS_API_KEY: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
    ANALYTICS_ID: import.meta.env.VITE_ANALYTICS_ID || '',

    // Feature Flags
    ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    ENABLE_DEBUG: import.meta.env.VITE_ENABLE_DEBUG === 'true',
    ENABLE_MOCK_DATA: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',

    // Upload Configuration
    MAX_FILE_SIZE: parseInt(import.meta.env.VITE_MAX_FILE_SIZE || '10485760'),
    ALLOWED_FILE_TYPES: import.meta.env.VITE_ALLOWED_FILE_TYPES?.split(',') || ['image/jpeg', 'image/png', 'image/webp'],

    // Timeouts and Limits
    API_TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
    SOCKET_TIMEOUT: parseInt(import.meta.env.VITE_SOCKET_TIMEOUT || '5000'),

    // Development helpers
    isDevelopment: import.meta.env.DEV,
    isProduction: import.meta.env.PROD,
  };
};

// Environment configuration for the web app
export const config = getEnvironmentConfig();

// Validation function to ensure required environment variables are set
export const validateConfig = () => {
  const requiredVars = [
    'VITE_API_URL',
    'VITE_API_BASE_URL',
    'VITE_SOCKET_URL',
  ];

  const missing = requiredVars.filter(varName => !import.meta.env[varName]);

  if (missing.length > 0) {
    const errorMessage = `Missing required environment variables: ${missing.join(', ')}`;
    if (import.meta.env.DEV) {
      console.error('❌', errorMessage);
    }
    throw new Error(errorMessage);
  }

  if (import.meta.env.DEV) {
    console.log('✅ Environment configuration validated successfully');
  }
};

export default config;
