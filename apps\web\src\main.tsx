// 📁 File: apps/web/src/main.tsx

import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import { AuthProvider } from './hooks/useAuth';

// Global styling
import './index.css';
import './styles/globals.css';

// Toast notifications
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Environment configuration validation
import { validateConfig } from './config/env';
import { validateAndDisplayEnvironment } from './utils/environmentValidator';

// Validate environment configuration on startup
try {
  validateConfig();
  validateAndDisplayEnvironment();
} catch (error) {
  if (import.meta.env.DEV) {
    console.error('Failed to validate environment configuration:', error);
  }
}

const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Root element not found');
}

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <ErrorBoundary
          fallback={<div className="p-4 text-red-600">Critical app error - Please refresh</div>}
          onError={(error) => console.error('Root error boundary caught:', error)}
        >
          <App />
          <ToastContainer
            position="top-right"
            autoClose={3000}
            newestOnTop
            theme="colored"
            hideProgressBar={false}
            pauseOnFocusLoss
            pauseOnHover
            closeOnClick
            draggable
            style={{
              fontSize: '14px',
              zIndex: 9999
            }}
            toastStyle={{
              minHeight: '44px'
            }}
          />
        </ErrorBoundary>
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>
);
