// 📁 File: apps/web/src/pages/admin/Assistant.tsx

import { useState } from 'react';
import { Button } from '../../components/ui/Button';
import api from '../../services/api';
import { toast } from 'react-toastify';

const Assistant = () => {
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);

  const askAssistant = async () => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      const res = await api.post<{ answer: string }>('/assistant/query', { query });
      setResponse(res.data.answer);
    } catch (err) {
      toast.error('Failed to fetch response');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">AI Assistant</h1>

      <div className="mb-4">
        <textarea
          rows={4}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Ask something like: Who are the top-rated technicians this week?"
          className="w-full border p-2 rounded"
        />
      </div>

      <Button onClick={askAssistant} disabled={loading} className="bg-blue-600 text-white">
        {loading ? 'Thinking...' : 'Ask'}
      </Button>

      {response && (
        <div className="mt-6 bg-white p-4 rounded shadow">
          <h2 className="font-bold mb-2">Answer</h2>
          <p className="text-gray-800 whitespace-pre-line">{response}</p>
        </div>
      )}
    </div>
  );
};

export default Assistant;
