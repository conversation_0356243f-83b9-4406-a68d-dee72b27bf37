// apps/web/src/pages/admin/JobDetails.tsx

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, MapPin, Clock, User, Phone, Mail } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';

interface JobDetail {
  id: string;
  issue: string;
  description: string;
  status: string;
  createdAt: string;
  scheduledAt: string;
  completedAt?: string;
  address: string;
  customer: {
    name: string;
    phone: string;
    email: string;
  };
  technician?: {
    name: string;
    phone: string;
    rating: number;
  };
  payment: {
    amount: number;
    status: string;
    isReleased: boolean;
    isFrozen: boolean;
  };
}

const JobDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [job, setJob] = useState<JobDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for now - replace with actual API call
    const mockJob: JobDetail = {
      id: id || '1',
      issue: 'Leaking Kitchen Faucet',
      description: 'Kitchen faucet has been leaking for 2 days. Water dripping constantly from the base.',
      status: 'IN_PROGRESS',
      createdAt: '2024-01-15T10:30:00Z',
      scheduledAt: '2024-01-16T14:00:00Z',
      address: '123 Main Street, London, SW1A 1AA',
      customer: {
        name: 'John Smith',
        phone: '+44 20 7946 0958',
        email: '<EMAIL>'
      },
      technician: {
        name: 'Mike Johnson',
        phone: '+44 20 7946 0123',
        rating: 4.8
      },
      payment: {
        amount: 85.50,
        status: 'PENDING',
        isReleased: false,
        isFrozen: false
      }
    };

    setTimeout(() => {
      setJob(mockJob);
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="flex items-center gap-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="text-gray-600 font-medium">Loading job details...</span>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="text-center py-16">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Job not found</h2>
        <p className="text-gray-600 mb-4">The requested job could not be found.</p>
        <Button onClick={() => navigate('/jobs')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Jobs
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <Button
          variant="outline"
          onClick={() => navigate('/jobs')}
          className="self-start"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Jobs
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Job Details</h1>
          <p className="text-gray-600 mt-1">Job #{job.id}</p>
        </div>
        <Badge className="self-start sm:self-auto">{job.status}</Badge>
      </div>

      {/* Job Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Main Details */}
        <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Job Information</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-1">{job.issue}</h3>
              <p className="text-gray-600 text-sm">{job.description}</p>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <MapPin className="w-4 h-4" />
              <span>{job.address}</span>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              <span>Scheduled: {new Date(job.scheduledAt).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Customer Details */}
        <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Details</h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900">{job.customer.name}</span>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-gray-500" />
              <a href={`tel:${job.customer.phone}`} className="text-blue-600 hover:underline">
                {job.customer.phone}
              </a>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="w-4 h-4 text-gray-500" />
              <a href={`mailto:${job.customer.email}`} className="text-blue-600 hover:underline">
                {job.customer.email}
              </a>
            </div>
          </div>
        </div>

        {/* Technician Details */}
        {job.technician && (
          <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Assigned Technician</h2>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <User className="w-4 h-4 text-gray-500" />
                <span className="text-gray-900">{job.technician.name}</span>
                <Badge variant="secondary" className="text-xs">
                  ⭐ {job.technician.rating}
                </Badge>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-gray-500" />
                <a href={`tel:${job.technician.phone}`} className="text-blue-600 hover:underline">
                  {job.technician.phone}
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Payment Information */}
        <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Amount:</span>
              <span className="font-semibold text-gray-900">£{job.payment.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Status:</span>
              <Badge 
                className={`text-xs ${
                  job.payment.isReleased ? 'bg-green-100 text-green-700' :
                  job.payment.isFrozen ? 'bg-red-100 text-red-700' :
                  'bg-yellow-100 text-yellow-700'
                }`}
              >
                {job.payment.isReleased ? 'Released' : 
                 job.payment.isFrozen ? 'Frozen' : 'Pending'}
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetails;
