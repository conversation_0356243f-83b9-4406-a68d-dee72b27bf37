// apps/web/src/pages/admin/Jobs.tsx

import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { toast } from 'react-toastify';

interface Payment {
  isReleased: boolean;
  isFrozen: boolean;
  freezeReason?: string;
}

interface Job {
  id: string;
  issue: string;
  status: string;
  createdAt: string;
  scheduledAt: string;
  technician?: {
    user: {
      fullName: string;
    };
  };
  user: {
    fullName: string;
  };
  payment?: Payment;
}

const JobsPage: React.FC = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const res = await axios.get<{ jobs: Job[] }>('/api/jobs');
      setJobs(res.data.jobs);
    } catch (err: any) {
      console.error(err?.response?.data || err.message);
      toast.error('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const handleFreeze = async (jobId: string) => {
    try {
      const reason = prompt('Enter freeze reason:');
      if (!reason) return;
      await axios.patch(`/api/payments/${jobId}/freeze`, { reason });
      toast.success('Payment frozen');
      fetchJobs();
    } catch {
      toast.error('Freeze failed');
    }
  };

  const handleUnfreeze = async (jobId: string) => {
    try {
      await axios.patch(`/api/payments/${jobId}/unfreeze`);
      toast.success('Payment unfrozen');
      fetchJobs();
    } catch {
      toast.error('Unfreeze failed');
    }
  };

  const handleManualRelease = async (jobId: string) => {
    try {
      await axios.patch(`/api/payments/${jobId}/release-manually`);
      toast.success('Payment released manually');
      fetchJobs();
    } catch {
      toast.error('Manual release failed');
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Job Payments</h1>
          <p className="text-gray-600 mt-1">Manage job payments and releases</p>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-16">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-gray-600 font-medium">Loading jobs...</span>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.id} className="bg-white border border-gray-200 rounded-2xl p-4 md:p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex flex-col lg:flex-row lg:items-start gap-4">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 text-base md:text-lg mb-2">{job.issue}</h3>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p className="flex items-center gap-2">
                      <span className="font-medium">Scheduled:</span>
                      <span>{new Date(job.scheduledAt).toLocaleString()}</span>
                    </p>
                    <p className="flex items-center gap-2">
                      <span className="font-medium">Customer:</span>
                      <span>{job.user.fullName}</span>
                    </p>
                    {job.technician && (
                      <p className="flex items-center gap-2">
                        <span className="font-medium">Technician:</span>
                        <span>{job.technician.user.fullName}</span>
                      </p>
                    )}
                  </div>
                  <div className="mt-3 flex flex-wrap gap-2">
                    <Badge className="text-xs">{job.status}</Badge>
                    {job.payment?.isReleased && (
                      <Badge className="bg-green-500 text-white text-xs">Released</Badge>
                    )}
                    {job.payment?.isFrozen && (
                      <Badge className="bg-red-500 text-white text-xs">Frozen</Badge>
                    )}
                  </div>
                </div>

                <div className="flex flex-row lg:flex-col gap-2 lg:w-48">
                  {!job.payment?.isReleased && (
                    <>
                      {!job.payment?.isFrozen ? (
                        <Button
                          onClick={() => handleFreeze(job.id)}
                          className="flex-1 lg:w-full text-sm"
                          size="sm"
                        >
                          Freeze
                        </Button>
                      ) : (
                        <Button
                          onClick={() => handleUnfreeze(job.id)}
                          variant="outline"
                          className="flex-1 lg:w-full text-sm"
                          size="sm"
                        >
                          Unfreeze
                        </Button>
                      )}
                      <Button
                        onClick={() => handleManualRelease(job.id)}
                        variant="secondary"
                        className="flex-1 lg:w-full text-sm"
                        size="sm"
                      >
                        Manual Release
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default JobsPage;
