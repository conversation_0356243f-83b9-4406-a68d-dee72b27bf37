import axios from 'axios';
import { config } from '../config/env';

const api = axios.create({
  baseURL: config.API_URL,
  timeout: config.API_TIMEOUT,
});

api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers = config.headers || {};
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api; // ✅ must be default export
