/* CTRON Home Design System - Global Styles */
/* Web Admin Panel CSS Implementation */

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Primary Colors */
  --color-primary-50: #F0F4F8;
  --color-primary-100: #D9E2EC;
  --color-primary-200: #BCCCDC;
  --color-primary-300: #9FB3C8;
  --color-primary-400: #829AB1;
  --color-primary-500: #627D98;
  --color-primary-600: #486581;
  --color-primary-700: #334E68;
  --color-primary-800: #243B53;
  --color-primary-900: #1B365D;

  /* Secondary Colors */
  --color-secondary-50: #FFF5F0;
  --color-secondary-100: #FFE4D6;
  --color-secondary-200: #FFCAB0;
  --color-secondary-300: #FFAB7A;
  --color-secondary-400: #FF8C42;
  --color-secondary-500: #FF6B35;
  --color-secondary-600: #E55A2B;
  --color-secondary-700: #CC4A1F;
  --color-secondary-800: #B33A13;
  --color-secondary-900: #992A07;

  /* Success Colors */
  --color-success-50: #F0FDF4;
  --color-success-100: #DCFCE7;
  --color-success-200: #BBF7D0;
  --color-success-300: #86EFAC;
  --color-success-400: #4ADE80;
  --color-success-500: #22C55E;
  --color-success-600: #16A34A;
  --color-success-700: #15803D;
  --color-success-800: #166534;
  --color-success-900: #14532D;

  /* Status Colors */
  --color-status-pending: #F59E0B;
  --color-status-assigned: #3B82F6;
  --color-status-active: #EF4444;
  --color-status-completed: #10B981;
  --color-status-cancelled: #6B7280;
  --color-status-overdue: #DC2626;

  /* Semantic Colors */
  --color-error: #EF4444;
  --color-warning: #F59E0B;
  --color-info: #3B82F6;
  --color-success: #10B981;

  /* Gray Scale */
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;

  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;

  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #4B5563;
  --text-tertiary: #6B7280;
  --text-inverse: #FFFFFF;

  /* Border Colors */
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-dark: #9CA3AF;

  /* Spacing Scale */
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.25rem;
  --radius-base: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.25rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #111827;
  --bg-secondary: #1F2937;
  --bg-tertiary: #374151;

  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --text-tertiary: #9CA3AF;
  --text-inverse: #111827;

  --border-light: #374151;
  --border-medium: #4B5563;
  --border-dark: #6B7280;
}

/* Professional Theme */
[data-theme="professional"] {
  --bg-primary: #F4F6F9;
  --bg-secondary: #E8EDF5;
  --bg-tertiary: #D1DCE8;

  --text-primary: #1B365D;
  --text-secondary: #2E5984;
  --text-tertiary: #4A90B8;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-family-primary);
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: background-color var(--transition-base), color var(--transition-base);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-700);
  text-decoration: underline;
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  /* Improve touch targets */
  button, input, select, textarea, a {
    min-height: 44px;
  }

  /* Better text sizing on mobile */
  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  input[type="number"],
  textarea,
  select {
    font-size: 16px;
  }

  /* Mobile-friendly spacing */
  .mobile-padding {
    padding: 1rem;
  }

  /* Hide scrollbars on mobile for cleaner look */
  .mobile-hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .mobile-hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

/* Touch-friendly utilities */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.touch-target-lg {
  min-height: 48px;
  min-width: 48px;
}

/* Prevent horizontal scroll on mobile */
body {
  overflow-x: hidden;
}

/* Fix for iOS Safari viewport height issues */
@supports (-webkit-touch-callout: none) {
  .ios-vh-fix {
    height: -webkit-fill-available;
  }
}

/* Safe area handling for mobile devices */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-inset-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .safe-area-inset-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}
