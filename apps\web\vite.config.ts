// 📁 File: apps/web/vite.config.ts

import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [react()],
    server: {
      port: 5173,
      host: true, // Allow external connections
    },
    define: {
      // Make environment variables available to the client
      __APP_ENV__: JSON.stringify(env.APP_ENV),
    },
    // Ensure environment variables are properly loaded
    envPrefix: 'VITE_',
  };
});
